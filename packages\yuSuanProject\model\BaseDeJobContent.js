"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeJobContent2022 = exports.BaseDeJobContent = void 0;
const typeorm_1 = require("typeorm");
/**
 * 定额工作内容表
 */
let BaseDeJobContent = class BaseDeJobContent {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], BaseDeJobContent.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent.prototype, "deId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_job_content", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent.prototype, "deJobContent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent.prototype, "deName", void 0);
BaseDeJobContent = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_job_content" })
], BaseDeJobContent);
exports.BaseDeJobContent = BaseDeJobContent;
let BaseDeJobContent2022 = class BaseDeJobContent2022 {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], BaseDeJobContent2022.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent2022.prototype, "deId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent2022.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_job_content", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent2022.prototype, "deJobContent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent2022.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "remark", nullable: true }),
    __metadata("design:type", String)
], BaseDeJobContent2022.prototype, "remark", void 0);
BaseDeJobContent2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_job_content_2022" })
], BaseDeJobContent2022);
exports.BaseDeJobContent2022 = BaseDeJobContent2022;
//# sourceMappingURL=BaseDeJobContent.js.map