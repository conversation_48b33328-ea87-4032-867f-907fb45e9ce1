"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysParams = void 0;
const typeorm_1 = require("typeorm");
const typeorm_2 = require("typeorm");
/**
 * 定额表
 */
let SysParams = class SysParams {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], SysParams.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "params_tag", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "paramsTag", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "params_name", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "paramsName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "content", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rec_user_code", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "recUserCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rec_date", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "recDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rec_status", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "recStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "extend1", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "extend1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "extend2", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "extend2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "extend3", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "extend3", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "description", nullable: true }),
    __metadata("design:type", String)
], SysParams.prototype, "description", void 0);
SysParams = __decorate([
    (0, typeorm_2.Entity)({ name: "sys_params" })
], SysParams);
exports.SysParams = SysParams;
//# sourceMappingURL=SysParams.js.map