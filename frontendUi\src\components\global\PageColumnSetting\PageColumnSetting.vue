<!--
 * @Descripttion: 页面显示列设置
 * @Author: renmingming
 * @Date: 2024-03-04 17:51:43
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-10 10:20:47
-->
<template>
  <commonModal
    className="dialog-comm page-column-setting"
    v-model:modelValue="modelValue"
    title="页面显示列设置"
    @show="openModal"
    @close="close">
    <!-- {{checkedList}} -->
    <div class="page-setting">
      <div class="tree-box">
        <a-tree
          :tree-data="treeData"
          :defaultExpandAll="true"
          v-if="treeData.length"
          @select="treeSelect"></a-tree>
      </div>
      <div class="setting-box">
        <div class="content-box">
          <!-- <a-checkbox-group
            v-model:value="checkedList"
            @change="checkChange($event)"
            :options="columnOptions"
            ><template #label="{ title }">
              {{ title }}</template
            ></a-checkbox-group
          > -->
          <template v-for="item in columnOptions">
            <template v-if="item?.children?.length > 0">
              <template v-for="childItem in item.children">
                <a-checkbox
                  v-if="
                    (['show', 'all'].includes(columnVisible) && childItem.visible) ||
                    (['hide', 'all'].includes(columnVisible) && !childItem.visible)
                  "
                  :disabled="childItem.isDisabled"
                  v-model:checked="childItem.visible"
                  @change="checkChange(childItem, $event)">
                  <a-tooltip>
                    <template #title>{{ childItem.parentName }}/{{ childItem.title }}</template>
                    {{ childItem.parentName }}/{{ childItem.title }}
                  </a-tooltip>
                </a-checkbox>
              </template>
            </template>
            <template v-else>
              <a-checkbox
                :disabled="item.isDisabled"
                v-model:checked="item.visible"
                @change="checkChange(item, $event)">
                <a-tooltip>
                  <template #title>{{ item.title }}</template>
                  {{ item.title }}
                </a-tooltip>
              </a-checkbox>
            </template>
          </template>
        </div>
        <div class="foot-box">
          <a-radio-group
            @change="setCheckedList(copyData)"
            v-model:value="columnVisible"
            name="radioGroup">
            <a-radio value="all">所有列</a-radio>
            <a-radio value="show">当前显示列</a-radio>
            <a-radio value="hide">当前隐藏列</a-radio>
          </a-radio-group>
        </div>
      </div>
    </div>
    <div class="page-setting-foot">
      <div class="left">
        <a-button style="margin-right: 13px" @click="defaultSetting">恢复默认设置</a-button>
        <a-checkbox
          v-model:checked="checkAll"
          :indeterminate="indeterminate"
          @change="onCheckAllChange">
          全选
        </a-checkbox>
      </div>
      <div class="right">
        <a-button style="margin-right: 25px" @click="close">取消</a-button>
        <a-button type="primary" @click="save">确定</a-button>
      </div>
    </div>
  </commonModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import xeUtils from 'xe-utils';
const emit = defineEmits(['save']);
let modelValue = ref(false);
const props = defineProps({
  columnOptions: {
    type: Array,
    default: () => [],
  },
  getDefaultColumns: {
    type: Function,
    default: () => [],
  },
});
const checkedList = ref([]);
const columnVisible = ref('all');
const treeData = [
  {
    title: '所有选项',
    key: 0,
    children: [
      {
        title: '常用选项',
        key: 1,
      },
      {
        title: '费用选项',
        key: 2,
      },
      // {
      //   title: '其他选项',
      //   key: 3,
      // },
    ],
  },
];

const checkChange = (e, event) => {
  if (e.visible && !e?.parentName) {
    checkedList.value.push(e.field);
  } else {
    checkedList.value = checkedList.value.filter(item => item !== e.field);
  }
  if (e?.parentName) {
    //当前是组合的子级被选中
    let tar = copyData.value.find(a => a.title === e.parentName);
    let list = tar.children;
    console.log(
      list.some(b => b.visible),
      list.every(b => !b.visible)
    );
    if (e.visible && list.some(b => b.visible) && !checkedList.value.find(a => a === tar.field)) {
      copyData.value.find(a => a.title === e.parentName).visible = true;
      checkedList.value.push(tar.field);
    } else if (list.every(b => !b.visible) && checkedList.value.find(a => a === tar.field)) {
      copyData.value.find(a => a.title === e.parentName).visible = false;
      checkedList.value = checkedList.value.filter(item => item !== tar.field);
    }
  }
  //else {
  //   if (e.visible) {
  //     checkedList.value.push(e.field);
  //   } else {
  //     checkedList.value = checkedList.value.filter(item => item !== e.field);
  //   }
  // }
};

//全选操作
let checkAll = ref(false);
let indeterminate = ref(true);
const onCheckAllChange = e => {
  //全选忽略默认选中项
  let disabledList = copyData.value.filter(item => item.isDisabled); //默认选中项数组
  checkedList.value = e.target.checked
    ? copyData.value.map(item => item.value)
    : copyData.value.filter(item => item.isDisabled).map(item => item.value);
  copyData.value = copyData.value.map(item => {
    item.visible = item?.isDisabled ? item.visible : e.target.checked;
    if (item?.children?.length > 0) {
      item.children.map(a => (a.visible = a?.isDisabled ? item.visible : e.target.checked));
    }
    return item;
  });
  // indeterminate.value = e.target.checked;
};

watch(
  () => checkedList.value,
  val => {
    // 多选控制
    indeterminate.value = !!val.length && val.length < copyData.value.length;
    checkAll.value = val.length === copyData.value.length;
  },
  {
    //有选中行的话全选按钮设置半选状态---深度监听
    deep: true,
  }
);
let currentNodeKey = ref(0);
const treeSelect = selectedKeys => {
  currentNodeKey.value = selectedKeys[0];
  setCheckedList(copyData.value);
};

let copyData = ref(JSON.parse(JSON.stringify(props.columnOptions)));
let initialData = ref([]); //初始数据字段
watch(
  () => props.columnOptions,
  val => {
    copyData.value = JSON.parse(JSON.stringify(val));
    // initialData.value = xeUtils.toTreeArray(defaultColumns, { children: 'children' });
  }
);
/**
 * 左侧切换筛选以及切换当前显示隐藏列筛选
 */
const columnOptions = computed(() => {
  return copyData.value
    .map(item => {
      item.value = item.field;
      return item;
    })
    .filter(item => {
      // 左侧和底部联动筛选
      if (item?.children?.length > 0) {
        return setIsVisible(item, '2');
      } else {
        return setIsVisible(item, '1');
      }
      return false;
    });
});
const setIsVisible = (item, type = '1') => {
  if (currentNodeKey.value === 0 || item.classType === currentNodeKey.value) {
    if (columnVisible.value === 'all') return true;
    if (
      columnVisible.value === 'show' &&
      ((type === '1' && item.visible) || (type === '2' && item.children.some(a => a.visible)))
    )
      return true;
    if (
      columnVisible.value === 'hide' &&
      ((type === '1' && !item.visible) || (type === '2' && item.children.some(a => !a.visible)))
    )
      return true;
  }
};
// watch(
//   () => columnOptions.value,
//   (val, old) => {
//     if (!old || (old && old.length)) {
//       // 初次设置选中值
//       checkedList.value = val
//         .filter(item => item.visible)
//         .map(item => item.field);
//     }
//   },
//   {
//     immediate: true,
//   }
// );
const setCheckedList = data => {
  checkedList.value = data.filter(item => item.visible).map(item => item.field);
};
// 默认恢复
const defaultSetting = async () => {
  const defaultColumns = await props.getDefaultColumns('1');
  //有些页面存在子级-需要转为平铺结构进行获取默认展示字段
  const d = xeUtils.toTreeArray(defaultColumns, { children: 'children' });
  const defaultKeys = d.filter(item => item.visible).map(item => item.field);
  console.log('defaultSetting', defaultKeys);

  copyData.value = defaultColumns.map(item => {
    if (defaultKeys.includes(item.field)) {
      item.visible = true;
      if (item?.children?.length > 0) {
        item.children.map(a => {
          if (defaultKeys.includes(a.field)) {
            a.visible = true;
          }
        });
      }
    }
    return item;
  });
  checkedList.value = copyData.value.filter(item => item.visible).map(item => item.field);
};
const getShowColumn = () => {
  if (checkedList.value.length === 0) return false;
  let list = [];
  copyData.value.map(a => {
    if (!a?.children?.length && a.visible) {
      list.push(a.field);
    }
    if (a?.children?.length > 0 && a.visible) {
      a.children.map(b => {
        if (b.visible) {
          list.push(b.field);
        }
      });
    }
  });
  console.log('getShowColumn', list);
  return list.length < 6 ? false : true;
};
const save = () => {
  // debugger;
  if (!getShowColumn()) {
    message.error('不可少于六列');
    return;
  }
  console.log('checkedList.value', checkedList.value, copyData.value);
  emit('save', checkedList.value, copyData.value);
  close();
};
const close = () => {
  modelValue.value = false;
  // bus.emit('focusTableData');
};
// 显示重置数据
const openModal = () => {
  copyData.value = JSON.parse(JSON.stringify(props.columnOptions));
  setCheckedList(copyData.value);
};
const open = () => {
  modelValue.value = true;
};
defineExpose({ open, close });
</script>
<style lang="scss" scoped>
:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  height: calc(60vh - 84px);
}
.page-setting {
  display: flex;
  height: 60vh;
  .tree-box {
    width: 160px;
    padding: 10px 0;
    border: 1px solid #e1e1e1;
    margin-right: 10px;
    background: rgba(255, 255, 255, 1);
  }
  .setting-box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .content-box {
    flex: 1;
    padding: 15px;
    overflow: scroll;
    border: 1px solid #e1e1e1;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 1);
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    height: calc(60vh - 84px);
    :deep(.ant-checkbox-wrapper) {
      margin-left: 0;

      .ant-checkbox + span {
        width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
      }
    }
  }
  .foot-box {
    height: 44px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    border: 1px solid #e1e1e1;
    background: rgba(255, 255, 255, 1);
  }
}
.page-setting-foot {
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
}
</style>
<style>
.page-column-setting .vxe-modal--body {
  background: #f5f5f5;
}
</style>
