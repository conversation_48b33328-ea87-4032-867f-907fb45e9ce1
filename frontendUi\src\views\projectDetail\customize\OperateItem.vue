<template>
  <div>
    <a-tooltip
      placement="bottom"
      v-model:visible="item.decVisible"
      @visibleChange="val => infoVisibleChange(val, item)"
    >
      <template #title>
        <span style="font-size: 12px; text-decoration: underline">{{
          item.label
        }}</span>
        <p v-if="item.infoDec" style="font-size: 10px">
          {{ item.infoDec }}
        </p>
      </template>
      <template v-if="['select', 'selectRadio'].includes(item.type)">
        <a-dropdown trigger="click" @visibleChange="visibleChange(item)">
          <div class="select-radio" v-if="['selectRadio'].includes(item.type)">
            <div class="select-head">
              <icon-font
                :type="item.iconType"
                class="iconType"
                :style="item.iconStyle ?? {}"
              />
              <div class="label" :style="item.labelStyle ?? {}">
                {{ item.label }}
              </div>
              <icon-font
                type="icon-xiala"
                style="color: rgba(51, 51, 51, 0.39)"
              />
            </div>
            <div class="sub-name">
              {{
                item.options.find(opt => opt.kind === item.value)?.name || ''
              }}
            </div>
          </div>
          <div v-else>
            <OperateItemTitle :item="item">
              <template #label>
                {{ item.label }}
                <icon-font
                  type="icon-xiala"
                  style="color: rgba(51, 51, 51, 0.39)"
                />
              </template>
            </OperateItemTitle>
          </div>
          <template #overlay>
            <a-menu>
              <a-menu-item
                v-for="selectitem in item.options"
                :key="selectitem.kind"
                :disabled="!selectitem.isValid"
                @click="setSelectEmit(selectitem, item)"
              >
                <a-checkbox
                  v-if="['selectCheck'].includes(item.type)"
                  :checked="selectitem.kind == checkedIndex"
                ></a-checkbox>
                <a-radio
                  v-if="['selectRadio'].includes(item.type)"
                  :checked="selectitem.kind == item.value"
                ></a-radio>
                <span
                  v-if="['select-color'].includes(item.name)"
                  class="color-border"
                  :class="`${selectitem.kind}`"
                ></span>
                {{ selectitem.name }}
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
      <template v-else>
        <div>
          <OperateItemTitle :item="item"></OperateItemTitle>
        </div>
      </template>
    </a-tooltip>
  </div>
</template>

<script setup>
import { ref } from 'vue';

import OperateItemTitle from './OperateItemTitle.vue';
const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  parentItem: {
    type: Object,
    default: () => null,
  },
});
const emit = defineEmits(['setSelectEmit', 'setEmit']);
const setSelectEmit = (item, data) => {
  emit('setSelectEmit', { item, data });
};
const visibleChange = item => {
  emit('setEmit', item);
};
const infoVisibleChange = (val, item) => {
  if (val) {
    item.decVisible = true;
  } else {
    item.decVisible = false;
  }
};
</script>
<style lang="scss" scoped></style>
