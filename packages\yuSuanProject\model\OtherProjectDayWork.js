"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectDayWork = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectDayWork extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, dispNo, sortNo, worksName, specification, unit, quantitativeExpression, tentativeQuantity, price, total, taxRemoval, jxTotal, csPrice, csTotal, jxTaxAmount, unitId, spId, constructId, parentId, dataType, rcjFlag) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.dispNo = dispNo;
        this.sortNo = sortNo;
        this.worksName = worksName;
        this.specification = specification;
        this.unit = unit;
        this.quantitativeExpression = quantitativeExpression;
        this.tentativeQuantity = tentativeQuantity;
        this.price = price;
        this.total = total;
        this.taxRemoval = taxRemoval;
        this.jxTotal = jxTotal;
        this.csPrice = csPrice;
        this.csTotal = csTotal;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
        this.parentId = parentId;
        this.dataType = dataType;
        this.rcjFlag = rcjFlag;
        this.jxTaxAmount = jxTaxAmount;
    }
}
exports.OtherProjectDayWork = OtherProjectDayWork;
//# sourceMappingURL=OtherProjectDayWork.js.map