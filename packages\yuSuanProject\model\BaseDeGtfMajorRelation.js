"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeGtfMajorRelation2022 = exports.BaseDeGtfMajorRelation = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 定额表
 */
let BaseDeGtfMajorRelation = class BaseDeGtfMajorRelation extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "add_rate_height", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation.prototype, "addRateHeight", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fgjzgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation.prototype, "fgjzgc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "lhgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation.prototype, "lhgc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "ylgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation.prototype, "ylgc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "gjxsgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation.prototype, "gjxsgc", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "default_flag", nullable: true }),
    __metadata("design:type", Number)
], BaseDeGtfMajorRelation.prototype, "defaultFlag", void 0);
BaseDeGtfMajorRelation = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_gtf_major_relation" })
], BaseDeGtfMajorRelation);
exports.BaseDeGtfMajorRelation = BaseDeGtfMajorRelation;
let BaseDeGtfMajorRelation2022 = class BaseDeGtfMajorRelation2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "add_rate_height", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation2022.prototype, "addRateHeight", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fgjzgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation2022.prototype, "fgjzgc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "lhgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation2022.prototype, "lhgc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "ylgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation2022.prototype, "ylgc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "gjxsgc", nullable: true }),
    __metadata("design:type", String)
], BaseDeGtfMajorRelation2022.prototype, "gjxsgc", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "default_flag", nullable: true }),
    __metadata("design:type", Number)
], BaseDeGtfMajorRelation2022.prototype, "defaultFlag", void 0);
BaseDeGtfMajorRelation2022 = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_gtf_major_relation_2022" })
], BaseDeGtfMajorRelation2022);
exports.BaseDeGtfMajorRelation2022 = BaseDeGtfMajorRelation2022;
//# sourceMappingURL=BaseDeGtfMajorRelation.js.map