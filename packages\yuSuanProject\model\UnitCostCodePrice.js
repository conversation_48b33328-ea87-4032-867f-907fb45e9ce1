"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitCostCodePrice = void 0;
const BaseModel_1 = require("./BaseModel");
class UnitCostCodePrice extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, unitId, code, name, type, price, remark) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.unitId = unitId;
        this.code = code;
        this.name = name;
        this.type = type;
        this.price = price;
        this.remark = remark;
    }
}
exports.UnitCostCodePrice = UnitCostCodePrice;
//# sourceMappingURL=UnitCostCodePrice.js.map