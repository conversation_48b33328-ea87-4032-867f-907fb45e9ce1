"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RcjDetails = void 0;
const BaseModel_1 = require("./BaseModel");
/**
 * 人材机设备明细表(RcjDetails)DTO类
 * 与BS保持一致
 */
class RcjDetails extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, rcjId, standardId, pbCode, pbName, type, materialCode, kind, materialName, specification, unit, dispNo, resQty, total, totalNumber, dePrice, marketPrice, sourcePrice, taxRemoval, taxRemovalBackUp, libraryCode, donorMaterialPrice, donorMaterialName, donorSpecification, ifDonorMaterial, donorMaterialNumber, kindBackUp, materialSequenceNbr, ifLockStandardPrice, ifLockQuantity, provisionalEstimateName, provisionalEstimatePrice, provisionalEstimateSpecification, provisionalEstimateSequenceNbr, ifProvisionalEstimate, deId, initResQty, markSum, producer, manufactor, brand, deliveryLocation, qualityGrade, priceDifferenc, priceDifferencSum, jxTotal, referenceRecord, edit, supplementDeRcjFlag, marketPriceBeforeLoading, isExecuteLoadPrice, highlight, tempDeleteFlag, kindSc, transferFactor, priceBaseJournal, priceBaseJournalTax, priceMarket, priceMarketTax, taxRate, isSupplement, kind7Sort, isCbrRcj, unitPostil, unitPostilState, singlePostil, singlePostilState, constructPostil, constructPostilState) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.rcjId = rcjId;
        this.standardId = standardId;
        this.pbCode = pbCode;
        this.pbName = pbName;
        this.type = type;
        this.materialCode = materialCode;
        this.kind = kind;
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.dispNo = dispNo;
        this.resQty = resQty;
        this.total = total;
        this.totalNumber = totalNumber;
        this.dePrice = dePrice;
        this.marketPrice = marketPrice;
        this.sourcePrice = sourcePrice;
        this.taxRemoval = taxRemoval;
        this.taxRemovalBackUp = taxRemovalBackUp;
        this.libraryCode = libraryCode;
        this.donorMaterialPrice = donorMaterialPrice;
        this.donorMaterialName = donorMaterialName;
        this.donorSpecification = donorSpecification;
        this.ifDonorMaterial = ifDonorMaterial;
        this.donorMaterialNumber = donorMaterialNumber;
        this.kindBackUp = kindBackUp;
        this.materialSequenceNbr = materialSequenceNbr;
        this.ifLockStandardPrice = ifLockStandardPrice;
        this.ifLockQuantity = ifLockQuantity;
        this.provisionalEstimateName = provisionalEstimateName;
        this.provisionalEstimatePrice = provisionalEstimatePrice;
        this.provisionalEstimateSpecification = provisionalEstimateSpecification;
        this.provisionalEstimateSequenceNbr = provisionalEstimateSequenceNbr;
        this.ifProvisionalEstimate = ifProvisionalEstimate;
        this.deId = deId;
        this.initResQty = initResQty;
        this.markSum = markSum;
        this.producer = producer;
        this.manufactor = manufactor;
        this.brand = brand;
        this.deliveryLocation = deliveryLocation;
        this.qualityGrade = qualityGrade;
        this.priceDifferenc = priceDifferenc;
        this.priceDifferencSum = priceDifferencSum;
        this.jxTotal = jxTotal;
        this.referenceRecord = referenceRecord;
        this.edit = edit;
        this.supplementDeRcjFlag = supplementDeRcjFlag;
        this.marketPriceBeforeLoading = marketPriceBeforeLoading;
        this.isExecuteLoadPrice = isExecuteLoadPrice;
        this.highlight = highlight;
        this.tempDeleteFlag = tempDeleteFlag;
        this.kindSc = kindSc;
        this.transferFactor = transferFactor;
        this.priceBaseJournal = priceBaseJournal;
        this.priceBaseJournalTax = priceBaseJournalTax;
        this.priceMarket = priceMarket;
        this.priceMarketTax = priceMarketTax;
        this.taxRate = taxRate;
        this.isSupplement = isSupplement;
        this.kind7Sort = kind7Sort;
        this.isCbrRcj = isCbrRcj;
        this.unitPostil = unitPostil;
        this.unitPostilState = unitPostilState;
        this.singlePostil = singlePostil;
        this.singlePostilState = singlePostilState;
        this.constructPostil = constructPostil;
        this.constructPostilState = constructPostilState;
    }
}
exports.RcjDetails = RcjDetails;
//# sourceMappingURL=RcjDetails.js.map