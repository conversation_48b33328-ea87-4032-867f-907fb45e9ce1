import api from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import {ref} from "vue";
export const useBcData = () => {
    let bcCode = ref(null);
    const projectStore = projectDetailStore();
    const defaultCodeColl = (type, kind = null) => {
        let apiData = {
            constructId: projectStore.currentTreeGroupInfo?.constructId,
            singleId: projectStore.currentTreeGroupInfo?.singleId,
            unitId: projectStore.currentTreeInfo?.id,
            type: type,
            clKind: kind,
            noRedo:true,
        }
        api.defaultCodeColl(apiData).then(res => {
            console.log('默认值', res)
            if (res.status === 200 && res.result) {
                bcCode.value = res.result;
            }
        })
    }
    return {
        defaultCodeColl,
        bcCode
    }
}
