"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseTaxReformDocuments = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
let BaseTaxReformDocuments = class BaseTaxReformDocuments extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseTaxReformDocuments.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseTaxReformDocuments.prototype, "type", void 0);
BaseTaxReformDocuments = __decorate([
    (0, typeorm_1.Entity)()
], BaseTaxReformDocuments);
exports.BaseTaxReformDocuments = BaseTaxReformDocuments;
//# sourceMappingURL=BaseTaxReformDocuments.js.map