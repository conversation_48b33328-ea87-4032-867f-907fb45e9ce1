const {ConvertUtil} = require("../utils/ConvertUtils");
const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");

/**
 * 造价分析接口
 */
class GljCostAnalysisController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取造价分析
     */
    async getCostAnalysisData(args) {
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        let deepCopy = ConvertUtil.deepCopy(costAnalysiss);
        return ResponseData.success(deepCopy);
    }

    /**
     * 修改造价分析 建筑面积
     *
     */
    async updateCostAnalysis(args, redo="编辑- -造价分析 工程规模") {
        await this.service.gongLiaoJiProject.gljCostAnalysisService.updateCostAnalysis(args);
        return ResponseData.success(true);
    }

    /**
     * 导出造价分析
     * @param args
     */
    async exportCostAnalysis(args) {
        let costAnalysiss = await this.service.gongLiaoJiProject.gljCostAnalysisService.getCostAnalysisData(args);
        let result;
        if (args.type === ProjectTypeConstants.PROJECT_TYPE_PROJECT) {
            result = await this.service.gongLiaoJiProject.gljCostAnalysisService.exportProjectCostAnalysis(costAnalysiss.costAnalysisConstructVOList);
        } else if (args.type === ProjectTypeConstants.PROJECT_TYPE_SINGLE) {
            result = await this.service.gongLiaoJiProject.gljCostAnalysisService.exportProjectCostAnalysis(costAnalysiss.costAnalysisSingleVOList);
        } else if (args.type === ProjectTypeConstants.PROJECT_TYPE_UNIT) {
            result = await this.service.gongLiaoJiProject.gljCostAnalysisService.exportUnitCostAnalysis(costAnalysiss.costAnalysisUnitVOList);
        }
        return ResponseData.success(result);
    }

    /**
     * 获取三材数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getScCountList(args) {
        let {constructId, singleId, unitId} = args;
        let scCountList = await this.service.gongLiaoJiProject.gljRcjCollectService.getScCountList(constructId, singleId, unitId);
        return ResponseData.success(scCountList);
    }

}

GljCostAnalysisController.toString = () => '[class GljCostAnalysisController]';
module.exports = GljCostAnalysisController;