"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("../../../core");
const { ResponseData } = require("../utils/ResponseData");
const { PricingFileFindUtils } = require("../utils/PricingFileFindUtils");
// 标准换算
class ConversionDeController extends core_1.Controller {
    constructor(ctx) {
        super(ctx);
    }
    static toString() {
        return "[class ConversionDeController]";
    }
    // 清除换算记录
    async cleanRules(dto) {
        await this.service.yuSuanProject.conversionInfoService.clearConversionInfo(dto.constructId, dto.singleId, dto.unitId, dto.fbFxDeId);
        return ResponseData.success(true);
    }
    // 清除换算记录
    async cleanRulesOld(dto) {
        await this.service.yuSuanProject.conversionDeProcess.cleanRules(dto.standardDeId, dto.fbFxDeId, dto.constructId, dto.singleId, dto.unitId);
        await this.service.yuSuanProject.autoCostMathService.autoCostMath({
            constructId: dto.constructId,
            singleId: dto.singleId,
            unitId: dto.unitId
        });
        //重新计算费用汇总
        await this.service.yuSuanProject.unitCostCodePriceService.countCostCodePrice({
            constructId: dto.constructId,
            singleId: dto.singleId,
            unitId: dto.unitId
        });
        await this.service.yuSuanProject.management.sycnTrigger("unitDeChange");
        await this.service.yuSuanProject.management.trigger("itemChange");
        return ResponseData.success(true);
    }
    /**
     * 获取标准换算数据
     * @param dto
     */
    async conversionRuleList(dto) {
        const result = await this.service.yuSuanProject.conversionDeProcess.conversionRuleList(dto);
        return ResponseData.success(result);
    }
    /**
     * 更新换算信息
     * @param dto
     */
    async updateDeConversionInfo(dto) {
        const { fbFxDeId, constructId, singleId, unitId, selectId, operateAction } = dto;
        const result = await this.service.yuSuanProject.conversionInfoService.updateDeConversionInfo(constructId, singleId, unitId, fbFxDeId, selectId, //当前行id
        operateAction //up上移,down下移,delete删除
        );
        return ResponseData.success(result);
    }
    /**
     * 获取换算信息数据
     * @param dto
     */
    getDefDonversion(dto) {
        const res = this.service.yuSuanProject.conversionDeService.getDefDonversion(dto);
        return ResponseData.success(res);
    }
    /**
     * 标准换算右侧统一系数处理
     * @param dto
     */
    async updateDefDonversion(dto) {
        const { constructId, singleId, unitId, deId, donversions } = dto;
        await this.service.yuSuanProject.conversionDeService.upDateDefault(constructId, singleId, unitId, deId, donversions);
        await this.service.yuSuanProject.management.sycnTrigger("unitDeChange");
        await this.service.yuSuanProject.management.trigger("itemChange");
        return ResponseData.success(true);
    }
    /**
     * 标准换算 数据更新操作
     * @param dto
     */
    async batchOperationalConversionRule(dto) {
        // TODO 先kind1 再 kind3 再取消kind1  kind3失效
        const { fbFxDeId, constructId, singleId, unitId, rules } = dto;
        await this.service.yuSuanProject.conversionDeService.conversionRule(constructId, singleId, unitId, fbFxDeId, dto.rules);
        // TODO 人材机（合计数量及合价） 单价构成计算 费用代码计算 费用汇总数据
        await Promise.all([
            this.service.yuSuanProject.management.sycnTrigger("unitDeChange"),
            this.service.yuSuanProject.management.trigger("itemChange"),
        ]);
        return ResponseData.success(true);
    }
    // 定额标准换算勾选
    async operationalConversionRule(dto) {
        const { fbFxDeId, baseRuleDetails, constructId, singleId, unitId, clpb } = dto;
        baseRuleDetails.clpb = clpb; //材料配比
        const result = await this.service.yuSuanProject.conversionDeService.conversionRule(constructId, singleId, unitId, fbFxDeId, baseRuleDetails);
        await this.service.yuSuanProject.management.sycnTrigger("unitDeChange");
        await this.service.yuSuanProject.management.trigger("itemChange");
        return ResponseData.success(result);
    }
    /**
     * 换算信息列表数据
     * @param dto
     */
    conversionInfoList(dto) {
        const { fbFxDeId, constructId, singleId, unitId } = dto;
        const result = this.service.yuSuanProject.conversionInfoService.getDeConversionInfo(constructId, singleId, unitId, fbFxDeId);
        return ResponseData.success(result);
    }
    getGroupNames(dto) {
        const { libraryCode, constructId, singleId, unitId } = dto;
        const res = this.service.yuSuanProject.conversionDeProcess.getGroupNames(libraryCode, constructId, singleId, unitId);
        return ResponseData.success(res);
    }
    getGroupDetail(dto) {
        const { groupName, libraryCode, constructId, singleId, unitId } = dto;
        const res = this.service.yuSuanProject.conversionDeProcess.getGroupDetail(groupName, libraryCode, constructId, singleId, unitId);
        return ResponseData.success(res);
    }
    // 切换当前换算标准 是默认值计算 还是 当前输入值计算
    async switchConversionMod(dto) {
        const res = await this.service.yuSuanProject.conversionDeService.switchConversionMod(dto);
        return ResponseData.success(res);
    }
    // 切换主材换算模式 参与或不参与换算
    async switchConversionMainMatMod(dto) {
        const res = await this.service.yuSuanProject.conversionDeService.switchConversionMainMatMod(dto);
        return ResponseData.success(res);
    }
}
module.exports = ConversionDeController;
//# sourceMappingURL=conversionDeController.js.map