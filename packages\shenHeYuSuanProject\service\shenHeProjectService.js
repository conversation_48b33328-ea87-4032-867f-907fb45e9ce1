'use strict';


const {
  app: electronApp, BrowserWindow,
  dialog
} = require('electron');
const { ResponseData } = require('../../yuSuanProject/utils/ResponseData');
const { PricingFileFindUtils } = require('../../yuSuanProject/utils/PricingFileFindUtils');
const { Snowflake } = require('../../yuSuanProject/utils/Snowflake');
const { PricingFileWriteUtils } = require('../../yuSuanProject/utils/PricingFileWriteUtils');
const { FileUtils } = require('../utils/FileUtils');
const { Service } = require('../../../core');
const { ShenHeWinManageUtils } = require('../utils/ShenHeWinManageUtils');
const { FileLevelTreeNode } = require('../../yuSuanProject/model/FileLevelTreeNode');
const { ConvertUtil } = require('../../yuSuanProject/utils/ConvertUtils');
const { arrayToTree, treeToArray } = require('../../yuSuanProject/main_editor/tree');
const ConstructBiddingTypeConstant = require('../../yuSuanProject/enum/ConstructBiddingTypeConstant');
const UpdateStrategy = require('../../yuSuanProject/main_editor/update/updateStrategy');
const { toJsonYsfString } = require('../../yuSuanProject/main_editor/util');
const { WinManageUtils } = require('../../../common/WinManageUtils');
const ConstantUtil = require('../../yuSuanProject/enum/ConstantUtil');
const { UPCContext } = require('../../../electron/unit_price_composition/core/UPCContext');
const { ConstructOperationUtil } = require('../../yuSuanProject/utils/ConstructOperationUtil');
const { ObjectUtils } = require('../utils/ObjectUtils');
const { ProjectFileUtils } = require('../../../common/ProjectFileUtils');
const { SingleProject } = require('../../yuSuanProject/model/SingleProject');
const { UnitProject } = require('../../yuSuanProject/model/UnitProject');
const BranchProjectLevelConstant = require('../../yuSuanProject/enum/BranchProjectLevelConstant');
const StepItemCostLevelConstant = require('../../yuSuanProject/enum/StepItemCostLevelConstant');
const YsshssConstant = require('../enum/YsshssConstant');
const {NumberUtil} = require("../../yuSuanProject/utils/NumberUtil");
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");

/**
 * 示例服务
 * @class
 */
class ShenheProjectService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 立即新建审核项目
   */
  async shNewProject(arg) {

    let { projectName, ssFilePath, sdFilePath, matchingSettings } = arg;
    if (ObjectUtils.isEmpty(projectName)) return ResponseData.fail('参数有误');
    if (ObjectUtils.isEmpty(ssFilePath)) return ResponseData.fail('参数有误');

    //获取到送审项目数据
    let ssObj = await PricingFileFindUtils.getProjectObjByPath(ssFilePath);
    //if(ssObj.deStandardReleaseYear == "22")return ResponseData.fail("审核计价暂未开放2022定额序列");
    if (await WinManageUtils.projectIsOpen(ssObj.sequenceNbr)) return ResponseData.fail('预算计价文件正在打开，请关闭后重新导入');

    ssObj.oldSequenceNbr = ssObj.sequenceNbr;
    //修改送审工程ID
    ssObj.sequenceNbr = Snowflake.nextId();

    ObjectUtils.updatePropertyValue(ssObj, 'constructId', ssObj.sequenceNbr);
    //送审数据单项工程挂父子级关系。
    let sssingleProjects = ssObj.singleProjects;
    if (ObjectUtils.isNotEmpty(sssingleProjects)) {
      await this.updateSignProjects(sssingleProjects);
    }
    //复制后的审定
    let newSdObj = ObjectUtils.cloneDeep(ssObj);
    let oldSequenceNbr = newSdObj.oldSequenceNbr;
    if (ObjectUtils.isNotEmpty(sdFilePath)) {
      if (ObjectUtils.isEmpty(matchingSettings)) return ResponseData.fail('至少选择一项；');
      //获取到审定项目数据
      let sdObj = await PricingFileFindUtils.getProjectObjByPath(sdFilePath);
      if (await WinManageUtils.projectIsOpen(sdObj.sequenceNbr)) return ResponseData.fail('预算计价文件正在打开，请关闭后重新导入');
      //if(ssObj.deStandardReleaseYear == "22")return ResponseData.fail("审核计价暂未开放2022定额序列");
      if (ssObj.deStandardReleaseYear != sdObj.deStandardReleaseYear) return ResponseData.fail('您导入的送审工程与审定工程序列不同，请核实后重新选择；');
      //不同计税方式（一般计税&简易计税）
      if (ssObj.projectTaxCalculation.taxCalculationMethod != sdObj.projectTaxCalculation.taxCalculationMethod) return ResponseData.fail('您导入的送审工程与审定工程计税方式不同，不能进行对比审核!');
      //不同计价类型（预算&结算）

      //不同项目结构（单位工程项目&招投标项目）
      if (ssObj.biddingType != sdObj.biddingType && (ssObj.biddingType === 2 || sdObj.biddingType === 2)) return ResponseData.fail('送审和审定文件不是同类文件，请重新上传');

      newSdObj = sdObj;
      //防止送审 审定文件一致id统一修改
      let singleProjects = newSdObj.singleProjects;
      if (ObjectUtils.isNotEmpty(singleProjects)) {
        await this.updateSignProjects(singleProjects);
      }
      oldSequenceNbr = newSdObj.sequenceNbr;
    }

    //处理审定文件
    let sequenceNbr = Snowflake.nextId();
    newSdObj.sequenceNbr = sequenceNbr;
    newSdObj.ysshConstructId = ssObj.sequenceNbr;
    newSdObj.oldConstructName = newSdObj.constructName;
    newSdObj.oldConstructId = oldSequenceNbr;
    newSdObj.oldSequenceNbr = oldSequenceNbr;
    newSdObj.constructName = projectName;
    newSdObj.path = ObjectUtils.isNotEmpty(sdFilePath) ? sdFilePath : ssFilePath;
    newSdObj.matchingSettings = matchingSettings;
    ObjectUtils.updatePropertyValue(newSdObj, 'constructId', sequenceNbr);
    if (newSdObj.biddingType === 2) newSdObj.unitProject.upName = projectName;

    //ProjectFileUtils.writeUserHistoryListFile(newSdObj);
    //内存数据写入
    ssObj.biddingTypeOld = ssObj.biddingType
    PricingFileWriteUtils.writeToMemory(ssObj);
    if (ssObj.UPCContext) {
      UPCContext.load(ssObj.UPCContext);
      await this.handleUPCContextSequenceNbr(ssObj);
    }
    PricingFileFindUtils.getUnitList(ssObj.sequenceNbr).forEach(k => {
      k.itemBillProjects = arrayToTree(k.itemBillProjects);
      k.measureProjectTables = arrayToTree(k.measureProjectTables);
    });
    newSdObj.biddingTypeOld = newSdObj.biddingType
    PricingFileWriteUtils.writeToMemory(newSdObj);
    if (newSdObj.UPCContext) {
      UPCContext.load(newSdObj.UPCContext);
      await this.handleUPCContextSequenceNbr(newSdObj);
    }
    PricingFileFindUtils.getUnitList(newSdObj.sequenceNbr).forEach(k => {
      k.itemBillProjects = arrayToTree(k.itemBillProjects);
      k.measureProjectTables = arrayToTree(k.measureProjectTables);
    });

    //由于上边更改了文件的id必须按照多文件来匹配
    //await this.autoBindingRule(newSdObj);
    this.differenceFileBindingRule(ssObj, newSdObj);
    //自动匹配
    if (ObjectUtils.isNotEmpty(ssFilePath) && ObjectUtils.isNotEmpty(sdFilePath)) {
      //差异文件默认对比规则
      //this.differenceFileBindingRule(ssObj,newSdObj);
      return ResponseData.success({ type: 'open', constructId: newSdObj.sequenceNbr });
    }
    let unitProjects = PricingFileFindUtils.getUnitList(newSdObj.sequenceNbr);
    //送审
    let sshUnitProjects = PricingFileFindUtils.getUnitList(newSdObj.ysshConstructId);
    if (ObjectUtils.isNotEmpty(unitProjects) && ObjectUtils.isNotEmpty(sshUnitProjects)) {
      for (let i = 0; i < unitProjects.length; i++) {
        let unitProject = unitProjects[i];
        if (ObjectUtils.isNotEmpty(unitProject.ysshUnitId)) {
          let sshUnitProject = sshUnitProjects.find(item => item.sequenceNbr === unitProject.ysshUnitId);
          await this.init(newSdObj.sequenceNbr, unitProject.spId, unitProject.sequenceNbr, newSdObj.ysshConstructId, sshUnitProject.spId, sshUnitProject.sequenceNbr);

        }
      }
    }
    let defaultStoragePath = await this.service.yuSuanProject.commonService.getSetStoragePath(projectName);
    const dialogOptions = {
      title: '保存文件',
      defaultPath: this.replaceAfterFirstDot(defaultStoragePath,"YSH"),
      filters: [{ name: '云算房文件', extensions: ['YSH'] }]
    };
    let result = dialog.showSaveDialogSync(null, dialogOptions);
    if (result) {
      if (ShenHeWinManageUtils.pullWin(newSdObj.sequenceNbr)) return ResponseData.success();
      newSdObj.path = result;
      ShenHeWinManageUtils.createWindow(newSdObj.constructName, newSdObj.sequenceNbr);
      let caiPingJson = {};
      try {
        //获取【工程项目审核汇总表】财评系统数据
        let gCXMSHHZBJson = await this.getGCXMSHHZBJson(newSdObj.sequenceNbr,ssObj.sequenceNbr);
        caiPingJson.gcxmshhzb = gCXMSHHZBJson;
        //获取【工程项目审核详细比对表】财评系统数据
        let gCXMSHXXBDBJson = await this.getGCXMSHXXBDBJson(newSdObj.sequenceNbr,ssObj.sequenceNbr);
        caiPingJson.gcxmshxxbdb = gCXMSHXXBDBJson;
      } catch (e) {
        console.log("审核保存添加财评系统数据报错",e);
      }
      ssObj.biddingType = 5;
      newSdObj.biddingType = 5;
      FileUtils.creatYshFile(ssObj, newSdObj, result,caiPingJson);
      ProjectFileUtils.writeUserHistoryListFile(newSdObj);
      ssObj.biddingType = ssObj.biddingTypeOld
      newSdObj.biddingType = newSdObj.biddingTypeOld
      return ResponseData.success(newSdObj.sequenceNbr);
    }
    return ResponseData.success();
  }

  /**
   * 处理工程项目sequenceNbr发生变化后  UPCContext中原来的sequenceNbr相关数据也需要发生变化
   */
  async handleUPCContextSequenceNbr(obj) {
    if (ObjectUtils.isEmpty(obj) || ObjectUtils.isEmpty(obj.UPCContext)) {
      return;
    }
    const unitList = PricingFileFindUtils.getUnitList(obj.sequenceNbr);
    if (ObjectUtils.isEmpty(unitList)) {
      return;
    }

    for (const unit of unitList) {
      const oldSequenceNbrStr = obj.oldSequenceNbr + ',' + unit.oldSequenceNbr;
      const newSequenceNbrStr = obj.sequenceNbr + ',' + unit.sequenceNbr;
      if (ObjectUtils.isNotEmpty(obj.UPCContext.qfCodeMap)) {
        UPCContext.qfCodeMap = new Map(Object.entries(ObjectUtils.cloneDeep(obj.UPCContext.qfCodeMap)));
        for (const [key, value] of Object.entries(obj.UPCContext.qfCodeMap)) {
          if (key.includes(oldSequenceNbrStr)) {
            UPCContext.qfCodeMap.set(key.replace(oldSequenceNbrStr, newSequenceNbrStr), value);
          }
        }
      }
      if (ObjectUtils.isNotEmpty(obj.UPCContext.feeFileMap)) {
        const cloneFeeFileMap = ObjectUtils.cloneDeep(obj.UPCContext.feeFileMap);
        for (const [key, value] of Object.entries(cloneFeeFileMap)) {
          if (key.includes(oldSequenceNbrStr)) {
            UPCContext.feeFileMap.set(key.replace(oldSequenceNbrStr, newSequenceNbrStr), new Map(Object.entries(value)));
          }
        }
      }
      if (ObjectUtils.isNotEmpty(obj.UPCContext.incrementTemplateListMap)) {
        // 因为UPCContext里面属性名称是imitationTemplateListMap 但是来源的属性名称又是incrementTemplateListMap
        // 并且UPCContext的load方法使用的属性名称也是incrementTemplateListMap
        // 所以这里使用incrementTemplateListMap的数据放入imitationTemplateListMap
        for (const [key, value] of Object.entries(obj.UPCContext.incrementTemplateListMap)) {
          if (key.includes(oldSequenceNbrStr)) {
            UPCContext.imitationTemplateListMap.set(key.replace(oldSequenceNbrStr, newSequenceNbrStr), new Map(Object.entries(ObjectUtils.cloneDeep(value))));
          }
        }
      }
    }
  }


  async openProject() {
    let defaultStoragePath = await this.service.yuSuanProject.commonService.getSetStoragePath(null);

    const options = {
      properties: ['openFile'],
      defaultPath: defaultStoragePath, // 默认保存路径
      filters: [
        { name: '云算房', extensions: ['YSH'] } // 可选的文件类型
      ]
    };
    let result = dialog.showOpenDialogSync(null, options);
    if (ObjectUtils.isEmpty(result)) {
      console.log('未选中任何文件');
      return;
    }
    //获取选中的路径
    let path = result[0];
    //获取项目数据
    let data = await PricingFileFindUtils.getProjectObjByPath(path);
    let shd = JSON.parse(data.shd);
    let ssh = JSON.parse(data.ssh);
    shd.path = path;
    //将项目数据写入到内存当中
    PricingFileWriteUtils.writeToMemory(ssh);
    PricingFileWriteUtils.writeToMemory(shd);
    if (ssh.UPCContext) {
      UPCContext.load(ssh.UPCContext);
    }
    if (shd.UPCContext) {
      UPCContext.load(shd.UPCContext);
    }
    this.service.yuSuanProject.systemService.loadProject(shd);
    this.service.yuSuanProject.systemService.loadProject(ssh);

    let windowId = shd.sequenceNbr;
    let windowName = shd.constructName;

    // 从文件路径提取文件名（不含扩展名）
    const pathModule = require('path');
    if (path) {
        let fileName = pathModule.basename(path, pathModule.extname(path));
        // 如果文件名和项目名不同，则在窗口标题中显示文件名
        if (fileName && fileName !== windowName) {
            windowName = `${windowName} - ${fileName}`;
        }
    }

    if (!ObjectUtils.isEmpty(global.windowMap) && global.windowMap.has(windowId)) {
      let newVar = global.windowMap.get(windowId);
      // 获取对应的窗口引用
      let win = BrowserWindow.fromId(newVar);
      if (win.isMinimized()) {
        win.restore();
      }
      //将窗口移动到顶部
      win.moveTop();
      // 更新现有窗口的标题
      win.setTitle(windowName);
      return;
    }
    //创建窗口
    let win = ShenHeWinManageUtils.createWindow(windowName, shd.sequenceNbr);
    // 定义全局map
    if (ObjectUtils.isEmpty(global.windowMap)) {
      global.windowMap = new Map();
    }
    windowMap.set(windowId, win.id);
    return windowId;
  }


  /**
   * 差异文件绑定规则
   */
  differenceFileBindingRule(ssObj, newSdObj) {
    //审定数据查询
    //获取审定的所有单项
    let sdSingleProjects = this.getSingleProjectsById(newSdObj.sequenceNbr);
    //获取审定的所有单位
    let sdUnits = PricingFileFindUtils.getUnitList(newSdObj.sequenceNbr);

    //父级未匹配上 子级不匹配
    let set = new Set();

    //送审数据查询
    //获取送审的所有单项
    let sshSingleProjects = this.getSingleProjectsById(ssObj.sequenceNbr);
    //获取送审的所有单位
    let sshUnitProjects = PricingFileFindUtils.getUnitList(ssObj.sequenceNbr);
    //重新返回树形状数据

    //循环审定的单项工程
    sdSingleProjects.forEach(sdSingleProject => {
      //送审中如果有和审定相同的单项
      let sshItemData = sshSingleProjects.find(singleProject => singleProject.projectName === sdSingleProject.projectName);
      if (ObjectUtils.isNotEmpty(sshItemData) && !set.has(sdSingleProject.parentId)) {
        sdSingleProject.ysshSingleId = sshItemData.sequenceNbr;
        //获取单项下的单位工程
        let sdSingleByUnits = sdUnits.filter(k => k.spId == sdSingleProject.sequenceNbr);
        let ssSingleByUnits = sshUnitProjects.filter(k => k.spId == sshItemData.sequenceNbr);

        sdSingleByUnits.forEach(k => {
          let sshUnit = ssSingleByUnits.find(i => i.upName === k.upName && i.deStandardReleaseYear === k.deStandardReleaseYear);
          if (ObjectUtils.isNotEmpty(sshUnit)) k.ysshUnitId = sshUnit.sequenceNbr;
        });
      } else {
        set.add(sdSingleProject.sequenceNbr);
      }
    });

    //循环审定的单位
    // sdUnits.forEach(sdUnit => {
    //     let sshItemData = sshUnitProjects.find(ssUnit => ssUnit.upName === sdUnit.upName);
    //     if(ObjectUtils.isNotEmpty(sshItemData))sdUnit.ysshUnitId = sshItemData.sequenceNbr;
    // })
  }


  /**
   * 自动绑定规则
   * @param obj
   */
  async autoBindingRule(obj) {

    let { sequenceNbr, ysshConstructId } = obj;
    let singleProjectList = this.getSingleProjectsById(sequenceNbr);

    //所有单项工程
    if (ObjectUtils.isNotEmpty(singleProjectList)) {
      singleProjectList.forEach((item, index) => {
        item.ysshSingleId = item.sequenceNbr;
      });
    }
    //所有单位工程
    let unitList = PricingFileFindUtils.getUnitList(sequenceNbr);
    if (ObjectUtils.isNotEmpty(unitList)) {
      for (let item in unitList) {
        const unitItem = unitList[item];
        unitItem.ysshUnitId = unitItem.sequenceNbr;
        unitItem.ysshSingleId = unitItem.spId;
        unitItem.ysshConstructId = ysshConstructId;
        await this.init(sequenceNbr, unitItem.spId, unitItem.sequenceNbr, ysshConstructId, unitItem.ysshSingleId, unitItem.ysshUnitId);
      }
    }


  }

  /**
   * 数据初始化  -用于其他功能挂关系地方
   * @param constructId 审定ID
   * @param singleId
   * @param unitId
   * @param ssConstructId 送审ID
   * @param ssSingleId
   * @param ssUnitId
   * @return {Promise<void>}
   */
  async init(constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId) {
    // matchingSettingArr 该参数是匹配设置参数

    let matchingSettingArr = PricingFileFindUtils.getProjectObjById(constructId).matchingSettings;
    if (ObjectUtils.isEmpty(matchingSettingArr)) {
      // 01 清单12位编码
      // 02 清单前9位编码
      // 03 清单名称
      // 04 清单项目特征
      matchingSettingArr = '02,03';
    }
    let args = { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId, matchingSettingArr };

    //分部分项初始化
    try {
      await this.service.shenHeYuSuanProject.ysshFbfxService.matchBillProject(args);
    } catch (e) {
      console.log('分部分项初始化数据报错' + e.stack);
    }
    try {
      await this.service.shenHeYuSuanProject.ysshMeasureService.initMatchMeasureProject(args);
    } catch (e) {
      console.log('措施项目初始化关联关系：' + e.stack);
    }
    // 其他项目数据初始化
    await this.service.shenHeYuSuanProject.ysshOtherProjectService.initOtherProjectAllMatch(args);
    try {
      // 费用汇总数据初始化
      await this.service.shenHeYuSuanProject.ysshCostSummaryService.initMatchCostSummaryProject(args);
    } catch (e) {
      console.log('费用汇总数据初始化关联关系：' + e.stack);
    }

    //整体分部分项  措施项目清单解锁
    this.service.yuSuanProject.unitProjectService.unLockAll(args);
    args.constructId = ssConstructId;
    args.singleId = ssSingleId;
    args.unitId = ssUnitId;
    this.service.yuSuanProject.unitProjectService.unLockAll(args);
    //人材机临时删除项改为正常项
    let unitList = PricingFileFindUtils.getUnitList(constructId);
    if (ObjectUtils.isNotEmpty(unitList)) {
      for (let i = 0; i < unitList.length; i++) {
        let unit = unitList[i];
        if (ObjectUtils.isNotEmpty(unit)) {
          let rcjList = PricingFileFindUtils.getRcjList(constructId, unit.spId, unit.sequenceNbr);
          if (ObjectUtils.isNotEmpty(rcjList)) {
            for (let i = 0; i < rcjList.length; i++) {
              let rcj = rcjList[i];
              //在审核不展示临时删除项
              if (ObjectUtils.isNotEmpty(rcj.tempDeleteFlag) && rcj.tempDeleteFlag) {
                rcj.tempDeleteFlag = false;
              }
              //增加预算数据标识，区分审核中得删除功能
              rcj.budgetData = true;
            }
          }
        }
      }
    }
  }


  /**
   * isDuibi 是否对比  0 否 1是
   * @param arg
   * @returns {Promise<*>}
   */
  async generateLevelTreeNodeStructure(arg) {
    let { sequenceNbr, isDuibi } = arg;
    if (ObjectUtils.isEmpty(isDuibi)) {
      isDuibi = 0;
    }
    //获取审定数据
    const sdProjectoriginal = PricingFileFindUtils.getProjectObjById(sequenceNbr);
    sdProjectoriginal.biddingType = sdProjectoriginal.biddingTypeOld;
    //获取送审数据
    const ssProjectoriginal = PricingFileFindUtils.getProjectObjById(sdProjectoriginal.ysshConstructId);
    ssProjectoriginal.biddingType = ssProjectoriginal.biddingTypeOld;
    //对比页面请求时备份数据 用户点击X是不保存对比结果。
    if (isDuibi === 1) {
      let sdProject = ConvertUtil.deepCopy(sdProjectoriginal);
      sdProject.oldShenDingProjectId = sequenceNbr;
      sdProject.sequenceNbr = Snowflake.nextId();
      //查询备份数据，操作备份数据。
      sequenceNbr = sdProject.sequenceNbr;
      //保留之前的关联id
      sdProject.oldysshConstructId = sdProject.ysshConstructId;
      ObjectUtils.updatePropertyValue(sdProject, 'constructId', sdProject.sequenceNbr);

      let ssProject = ConvertUtil.deepCopy(ssProjectoriginal);
      ssProject.oldSongShenProjectId = ssProjectoriginal.sequenceNbr;
      ssProject.sequenceNbr = Snowflake.nextId();
      sdProject.ysshConstructId = ssProject.sequenceNbr;
      ObjectUtils.updatePropertyValue(ssProject, 'constructId', ssProject.sequenceNbr);
      //审定 送审 数据存入缓存
      ObjectUtils.updatePropertyValue(sdProject, 'ysshConstructId', ssProject.sequenceNbr);
      PricingFileWriteUtils.writeToMemory(sdProject);
      await this.handleUPCContextSequenceNbrV1(sdProject);
      PricingFileWriteUtils.writeToMemory(ssProject);
      await this.handleUPCContextSequenceNbrV1(ssProject);
    }else{
      if (sdProjectoriginal.UPCContext) {
        UPCContext.load(sdProjectoriginal.UPCContext);
        await this.handleUPCContextSequenceNbr(sdProjectoriginal);
      }
      if (ssProjectoriginal.UPCContext) {
        UPCContext.load(ssProjectoriginal.UPCContext);
        await this.handleUPCContextSequenceNbr(ssProjectoriginal);
      }
    }
    //获取项目结构树
    let projectObj = PricingFileFindUtils.getProjectObjById(sequenceNbr);
    let newarg = {
      sequenceNbr: sequenceNbr
    };
    const result = await this.service.yuSuanProject.constructProjectService.generateLevelTreeNodeStructure(newarg);

    // let result = new Array();
    // await this.generateLevelTreeNode(projectObj, result);

    //获取审定的所有单项


    let sdhUnitProjects = PricingFileFindUtils.getUnitList(sequenceNbr);
    //let sdhSingleProjects = PricingFileFindUtils.getSingleProjectList(sequenceNbr);
    let sdhMap = ConstructOperationUtil.flatConstructTreeToMapById(sequenceNbr);
    let sdhConstructOperation = Array.from(sdhMap.values()).map(value => value);
    let sdhSingleProjects = sdhConstructOperation.filter(k => k.levelType == 2);


    let sshUnitProjects = PricingFileFindUtils.getUnitList(projectObj.ysshConstructId);
    //let sshSingleProjects = PricingFileFindUtils.getSingleProjectList(projectObj.ysshConstructId);

    let sshMap = ConstructOperationUtil.flatConstructTreeToMapById(projectObj.ysshConstructId);
    let sshConstructOperation = Array.from(sshMap.values()).map(value => value);
    let sshSingleProjects = sshConstructOperation.filter(k => k.levelType == 2);
    let sshUnitCopy = ConvertUtil.deepCopy(sshUnitProjects);
    let sshSingleCopy = ConvertUtil.deepCopy(sshSingleProjects);
    //审定树
    result.forEach(item => {
      if (1 == item.levelType) {
        item.ysshConstructId = projectObj.ysshConstructId;
      }

      let unit = sdhUnitProjects.find(k => 3 == item.levelType && k.sequenceNbr == item.id);
      if (ObjectUtils.isNotEmpty(unit)) {
        item.ysshUnitId = unit.ysshUnitId;
      }
      let single = sdhSingleProjects.find(k => 2 == item.levelType && k.sequenceNbr == item.id);
      if (ObjectUtils.isNotEmpty(single)) {
        item.ysshSingleId = single.ysshSingleId;

      }


      if (item.levelType === 2 && ObjectUtils.isNotEmpty(item.ysshSingleId)) {
        let single = sshSingleCopy.find(item2 => item2.sequenceNbr === item.ysshSingleId);
        single.matchFlag = true;
        item.ysshParentId = single.constructId;
        //新增逻辑  审删项添加到审定项目中
        let singleSD = sdhSingleProjects.find(item2 => item2.sequenceNbr === item.id);
        if (ObjectUtils.isNotEmpty(singleSD) && ObjectUtils.isNotEmpty(singleSD.special)) {
          item.type = 'delete';
        }
      }
      if (item.levelType === 3 && ObjectUtils.isNotEmpty(item.ysshUnitId)) {
        let unit = sshUnitCopy.find(item2 => item2.sequenceNbr === item.ysshUnitId);
        //todo 处理送审拉单位特殊逻辑
        unit.matchFlag = true;
        //无需判断类型biddingtype
        item.ysshSingleId = unit.spId;
        item.ysshParentId = ObjectUtils.isEmpty(item.spId) ? unit.constructId : unit.spId;
        //新增逻辑  审删项添加到审定项目中
        let sdunit = sdhUnitProjects.find(item3 => item3.sequenceNbr === item.id);
        if (ObjectUtils.isNotEmpty(sdunit) && ObjectUtils.isNotEmpty(sdunit.special)) {
          item.type = 'delete';
        }
      }
      if (item.levelType !== 1 && ObjectUtils.isEmpty(item.ysshSingleId) && ObjectUtils.isEmpty(item.ysshUnitId)) {
        item.type = 'add';
      }
    });

    sshSingleCopy ? sshSingleCopy.forEach(item => {
      if (!item.matchFlag) {
        let constructTreeNode = new FileLevelTreeNode();
        let singleProject = item;
        //constructTreeNode.id = singleProject.sequenceNbr + "_ss";
        constructTreeNode.id = singleProject.sequenceNbr;
        constructTreeNode.name = singleProject.projectName;
        constructTreeNode.type = 'delete';
        constructTreeNode.levelType = 2;
        constructTreeNode.ysshSingleId = singleProject.sequenceNbr;
        //处理层级关系 迭代三新逻辑
        let parentId = singleProject.parentId;
        //为空代表为最高层级的单项工程
        if (ObjectUtils.isEmpty(parentId) || parentId === projectObj.sequenceNbr) {
          constructTreeNode.parentId = projectObj.sequenceNbr;
        } else {
          //如果上级匹配上了应该赋值  审定的id
          let sdSing = sdhSingleProjects.find(itemData => itemData.ysshSingleId === parentId);
          if (ObjectUtils.isNotEmpty(sdSing)) {
            constructTreeNode.parentId = sdSing.sequenceNbr;
          } else {//如果上级未匹配找自己的送审上级
            constructTreeNode.parentId = parentId;
          }
        }
        result.push(constructTreeNode);
      }
    }) : null;
    sshUnitCopy ? sshUnitCopy.forEach(item => {

      if (!item.matchFlag) {
        let unitProject = item;
        let unitTreeNode = new FileLevelTreeNode();
        //unitTreeNode.id = unitProject.sequenceNbr + "_ss";
        unitTreeNode.id = unitProject.sequenceNbr;
        unitTreeNode.name = unitProject.upName;
        unitTreeNode.levelType = 3;
        unitTreeNode.type = 'delete';
        unitTreeNode.ysshSingleId = unitProject.spId;
        unitTreeNode.ysshUnitId = unitProject.sequenceNbr;
        unitTreeNode.constructMajorType = unitProject.constructMajorType;

        let unitParentid = projectObj.sequenceNbr;
        if (ObjectUtils.isNotEmpty(unitProject.spId)) {
          let shdSingleProjects = PricingFileFindUtils.getSingleProjectList(projectObj.sequenceNbr);
          let singleProject = shdSingleProjects.find(singleItem => singleItem.ysshSingleId === unitProject.spId);
          //singleProject ? unitParentid = singleProject.sequenceNbr : unitParentid = unitProject.spId + "_ss";
          singleProject ? unitParentid = singleProject.sequenceNbr : unitParentid = unitProject.spId;
        }
        unitTreeNode.parentId = unitParentid;
        unitTreeNode.libraryCode = unitProject.mainDeLibrary;
        unitTreeNode.secondInstallationProjectName = unitProject.secondInstallationProjectName;
        result.push(unitTreeNode);
      }
    }) : null;
    return result;
  }

  /**
   * 处理工程项目sequenceNbr发生变化后  UPCContext中原来的sequenceNbr相关数据也需要发生变化
   */
  async handleUPCContextSequenceNbrV1(obj) {
    if (ObjectUtils.isEmpty(obj) || ObjectUtils.isEmpty(obj.UPCContext)) {
      return;
    }
    const unitList = PricingFileFindUtils.getUnitList(obj.sequenceNbr);
    if (ObjectUtils.isEmpty(unitList)) {
      return;
    }

    function copyNewValue(targetObj, oldSequenceNbrStr, newSequenceNbrStr) {
      for (const [key, value] of targetObj) {
        if (key.includes(oldSequenceNbrStr)) {
          targetObj.set(key.replace(oldSequenceNbrStr, newSequenceNbrStr), ObjectUtils.cloneDeep(value));
        }
      }
    }

    for (const unit of unitList) {
      const oldSequenceNbrStr = (ObjectUtils.isNotEmpty(obj.oldShenDingProjectId) ? obj.oldShenDingProjectId : obj.oldSongShenProjectId) + ',' + unit.sequenceNbr;
      const newSequenceNbrStr = obj.sequenceNbr + ',' + unit.sequenceNbr;
      if (ObjectUtils.isNotEmpty(UPCContext.qfCodeMap)) {
        copyNewValue(UPCContext.qfCodeMap, oldSequenceNbrStr, newSequenceNbrStr);
      }
      if (ObjectUtils.isNotEmpty(UPCContext.feeFileMap)) {
        copyNewValue(UPCContext.feeFileMap, oldSequenceNbrStr, newSequenceNbrStr);
      }
      if (ObjectUtils.isNotEmpty(UPCContext.imitationTemplateListMap)) {
        copyNewValue(UPCContext.imitationTemplateListMap, oldSequenceNbrStr, newSequenceNbrStr);
      }
    }
  }


  /**
   * 另存为
   */
  async fileSaveAs(args, fileNameSuffix) {
    let { constructId } = args;

    let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
    let ssProjectObjById = PricingFileFindUtils.getProjectObjById(projectObjById.ysshConstructId);

    let defaultStoragePath = await this.service.yuSuanProject.commonService.getSetStoragePath(projectObjById.constructName + fileNameSuffix);

    const dialogOptions = {
      title: '另存为',
      defaultPath: this.replaceAfterFirstDot(defaultStoragePath,"YSH"),
      filters: [{ name: '云算房文件', extensions: ['YSH'] }]
    };
    let filePath = dialog.showSaveDialogSync(null, dialogOptions);
    if (filePath) {
      if (!filePath.toUpperCase().endsWith('.YSH')) {
        filePath += '.YSH';
      }
      //查询选择的路径是否已经有被打开的文件
      let result = await this.service.yuSuanProject.constructProjectFileService.getOneProDataByPath(filePath);
      if (!ObjectUtils.isEmpty(result)) {
        let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
        if (!ObjectUtils.isEmpty(projectObj)) {
          return ResponseData.success(2);
        }
      }
      //复制一份原始数据
      let copyObj = ConvertUtil.deepCopy(projectObjById);
      copyObj.path = filePath;
      //重新刷新所有的项目ID
      let constructId = Snowflake.nextId();
      copyObj.sequenceNbr = constructId;
      ObjectUtils.updatePropertyValue(copyObj, 'constructId', constructId);

      //PricingFileWriteUtils.creatYsfFile(copyObj);
      PricingFileWriteUtils.writeToMemory(copyObj);
      FileUtils.creatYshFile(ssProjectObjById, projectObjById, filePath);
      //await this.service.ysfHandlerService.creatYsfFile(copyObj);
      global.constructProject[copyObj.sequenceNbr] = null;
      return ResponseData.success(1);
    }
    return ResponseData.success(0);
  }


  /**
   * 查询数据
   * @param  arg
   */
  async shQueryDetail(arg) {
    let { constructId} = arg;

    //审定数据查询
    let sdObj = PricingFileFindUtils.getProjectObjById(constructId);
    if (ObjectUtils.isEmpty(sdObj)) return new ResponseData('100', '未找到对应数据');
    let sdMap = ConstructOperationUtil.flatConstructTreeToMapByObj(sdObj);
    let sdConstructOperation = Array.from(sdMap.values()).map(value => value);

    //获取审定的所有单项
    let sdSingleProjects = sdConstructOperation.filter(k => k.levelType == 2);
    //获取审定的所有单位
    let sdUnits = PricingFileFindUtils.getUnitList(constructId);


    //送审数据查询
    let ysshConstructId = sdObj.ysshConstructId;
    let ssObj = PricingFileFindUtils.getProjectObjById(ysshConstructId);
    if (ObjectUtils.isEmpty(ssObj)) return new ResponseData('100', '未找到对应数据');
    //获取送审的所有单项
    let ssMap = ConstructOperationUtil.flatConstructTreeToMapById(ysshConstructId);
    let ssConstructOperation = Array.from(ssMap.values()).map(value => value);
    let sshSingleProjects = ssConstructOperation.filter(k => k.levelType == 2);
    //获取送审的所有单位
    let sshUnitProjects = PricingFileFindUtils.getUnitList(ysshConstructId);
    //重新返回树形状数据


    //送审数据操作copy的
    let sshSpCopy = ConvertUtil.deepCopy(sshSingleProjects);
    let sshUnitCopy = ConvertUtil.deepCopy(sshUnitProjects);


    //循环审定的单项工程
    sdSingleProjects.forEach(sdSingleProject => {
      //送审中如果有和审定相同的单项
      let sshItemData = sshSpCopy.find(singleProject => singleProject.sequenceNbr === sdSingleProject.ysshSingleId);
      if (ObjectUtils.isNotEmpty(sshItemData)) sshItemData.matchFlag = true;
    });

    //循环审定的单位
    sdUnits.forEach(sdUnit => {
      let sshItemData = sshUnitCopy.find(ssUnit => ssUnit.sequenceNbr === sdUnit.ysshUnitId);
      if (ObjectUtils.isNotEmpty(sshItemData)) sshItemData.matchFlag = true;
    });
    return this.generTreeListNode(sdObj, ssObj, sshSpCopy, sshUnitCopy, sdSingleProjects);
  }

  generTreeListNode(sdObj, ssObj, sshSpCopy, sshUnitCopy, shdSingleProjects) {
    //组装送审树
    let shObjArray = new Array();
    FileUtils.generateLevelTreeNode(ssObj, shObjArray);
    //组装审定树
    let result = new Array();
    //此时可操作审定数据了
    let shendObjCopy = ConvertUtil.deepCopy(sdObj);

    //组装审定树
    FileUtils.generateLevelTreeNode(shendObjCopy, result);

    //判断审定树
    if (ObjectUtils.isNotEmpty(result)) {
      result.forEach(item => {
        if (item.levelType === 1) {
          item.ssName = ssObj.constructName;
          item.ssId = ssObj.sequenceNbr;
        }
        if (item.levelType === 2) {
          let ssSingleData = item.ysshSingleId ? sshSpCopy.find(itemData => itemData.sequenceNbr === item.ysshSingleId) : null;
          item.ssName = ssSingleData ? ssSingleData.projectName : null;
          item.ssId = ssSingleData ? ssSingleData.sequenceNbr : null;
        }

        if (item.levelType === 3) {
          let ssdData = item.ysshUnitId ? sshUnitCopy.find(itemData => itemData.sequenceNbr === item.ysshUnitId) : null;
          item.ssName = ssdData ? ssdData.upName : '';
          item.ssId = ssdData ? ssdData.sequenceNbr : '';
        }
      });
    }
    sshSpCopy.forEach(item => {
      if (!item.matchFlag) {
        let constructTreeNode = new FileLevelTreeNode();
        let singleProject = item;
        //constructTreeNode.ssId = singleProject.sequenceNbr+"_ss";
        constructTreeNode.ssId = singleProject.sequenceNbr;
        constructTreeNode.ssName = singleProject.projectName;
        constructTreeNode.type = 'delete';
        constructTreeNode.levelType = 2;
        //处理层级关系 迭代三新逻辑
        let parentId = singleProject.parentId;
        //为空代表为最高层级的单项工程
        if (ObjectUtils.isEmpty(parentId) || parentId === ssObj.sequenceNbr) {
          constructTreeNode.parentId = shendObjCopy.sequenceNbr;
        } else {
          //如果上级匹配上了应该赋值  审定的id
          let sdSing = shdSingleProjects.find(itemData => itemData.ysshSingleId === parentId);
          if (ObjectUtils.isNotEmpty(sdSing)) {
            constructTreeNode.parentId = sdSing.sequenceNbr;
          } else {//如果上级未匹配找自己的送审上级
            constructTreeNode.parentId = parentId;
          }
        }
        result.push(constructTreeNode);
      }
    });
    sshUnitCopy.forEach(item => {
      if (!item.matchFlag) {
        let unitProject = item;
        let unitTreeNode = new FileLevelTreeNode();
        //unitTreeNode.ssId = unitProject.sequenceNbr+"_ss";
        unitTreeNode.ssId = unitProject.sequenceNbr;
        unitTreeNode.ssName = unitProject.upName;
        unitTreeNode.levelType = 3;
        unitTreeNode.type = 'delete';
        unitTreeNode.constructMajorType = unitProject.constructMajorType;
        let unitParentid = sdObj.sequenceNbr;
        if (ObjectUtils.isNotEmpty(unitProject.spId)) {
          let singleProject = shdSingleProjects ? shdSingleProjects.find(singleItem => singleItem.ysshSingleId === unitProject.spId) : null;
          //singleProject?unitParentid = singleProject.sequenceNbr:unitParentid = unitProject.spId+"_ss";
          singleProject ? unitParentid = singleProject.sequenceNbr : unitParentid = unitProject.spId;
        }
        unitTreeNode.parentId = unitParentid;
        unitTreeNode.libraryCode = unitProject.mainDeLibrary;
        unitTreeNode.secondInstallationProjectName = unitProject.secondInstallationProjectName;
        result.push(unitTreeNode);
      }
    });

    result.forEach(k => {
      // k.id = Snowflake.nextId();

      if (ObjectUtils.isNotEmpty(k.sdId)) {
        k.id = k.sdId;
      } else {
        k.id = k.ssId;
      }


    });
    return ResponseData.success({
      'list': result,
      'ssh': shObjArray
    });
  }


  /**
   * 调整明细
   * @param  arg
   */
  async shSaveDetail(arg) {
    let constructId = arg.constructId;
    let list = JSON.parse(arg.list);


    //审定
    let shendObj = PricingFileFindUtils.getProjectObjById(arg.constructId);

    //送审
    let sshUnitProjects = PricingFileFindUtils.getUnitList(shendObj.ysshConstructId);
    //审定
    let unitProjects = PricingFileFindUtils.getUnitList(constructId);
    //送审得单项工程集合
    let sssingleProject = this.getSingleProjectsById(shendObj.ysshConstructId);


    //维护单位及单项的关系
    if (ObjectUtils.isNotEmpty(list)) {
      //内存中挂关系
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        if (item.levelType === 2 && ObjectUtils.isEmpty(item.type)) {
          //单项
          let singleProject = PricingFileFindUtils.getSingleProject(constructId, item.id);
          if (ObjectUtils.isNotEmpty(singleProject)) {
            singleProject.ysshSingleId = item.ysshSingleId;
          }
        }
        if (item.levelType === 3 && ObjectUtils.isEmpty(item.type)) {
          //单位
          let unitProject = PricingFileFindUtils.getUnit(constructId, item.parentId, item.id);
          if (ObjectUtils.isNotEmpty(unitProject)) {
            unitProject.ysshUnitId = item.ysshUnitId;
          }
        }
        //处理审删得工程项目(基于前端传入的list层级处理下列逻辑)
        if (item.type == 'delete') {
          if (item.levelType === 2) {
            //送审得单项工程
            let singleProject = sssingleProject.find(k => k.sequenceNbr === item.ssId);
            //克隆出添加得审定数据
            let sdsingleProject = ObjectUtils.cloneDeep(singleProject);
            let singparentId = sdsingleProject.parentId;
            sdsingleProject.sequenceNbr = Snowflake.nextId();
            sdsingleProject.ysshSingleId = singleProject.sequenceNbr;
            sdsingleProject.special = 1;//添加得标识
            ObjectUtils.updatePropertyValue(sdsingleProject, 'constructId', constructId);
            let newUnit = new Array();
            //查找当前copy得送审单项工程得上一级
            let ssSingProject = sssingleProject.find(j => j.sequenceNbr === singparentId);
            let sdSingProjects = this.getSingleProjectsById(constructId);
            //如果父级id跟工程id相同直接放入单项工程
            if (ObjectUtils.isEmpty(ssSingProject) || singparentId === shendObj.ysshConstructId) {
              sdsingleProject.parentId = null;
              sdsingleProject.unitProjects = newUnit;
              sdsingleProject.subSingleProjects = null;
              let singleProjects = shendObj.singleProjects;
              if (ObjectUtils.isEmpty(singleProjects)) {
                let sign = new Array();
                sign.push(sdsingleProject);
                singleProjects = sign;
              } else {
                singleProjects.push(sdsingleProject);
              }
            } else {  //当前审定 肯定是已匹配的
              let addsing = sdSingProjects.find(l => l.ysshSingleId == ssSingProject.sequenceNbr);
              //如果上级是未匹配的送审则放入子级
              if (ObjectUtils.isNotEmpty(addsing.special) && addsing.special === 1) {
                sdsingleProject.parentId = addsing.sequenceNbr;
                sdsingleProject.unitProjects = newUnit;
                sdsingleProject.subSingleProjects = null;
                addsing = PricingFileFindUtils.getSingleProject(constructId, addsing.sequenceNbr);
                if (ObjectUtils.isEmpty(addsing.subSingleProjects)) {
                  let sign = new Array();
                  sign.push(sdsingleProject);
                  addsing.subSingleProjects = sign;
                } else {
                  addsing.subSingleProjects.push(sdsingleProject);
                }
              } else {//如果当前层级匹配了则放入同级
                //父级为空则为最高级
                if (ObjectUtils.isEmpty(addsing.parentId) || addsing.parentId === constructId) {
                  sdsingleProject.parentId = null;
                  sdsingleProject.unitProjects = newUnit;
                  sdsingleProject.subSingleProjects = null;
                  shendObj.singleProjects.push(sdsingleProject);
                } else {
                  let tjaddsing = sdSingProjects.find(l => l.sequenceNbr == addsing.parentId);
                  sdsingleProject.parentId = tjaddsing.sequenceNbr;
                  sdsingleProject.unitProjects = newUnit;
                  sdsingleProject.subSingleProjects = null;
                  addsing = PricingFileFindUtils.getSingleProject(constructId, tjaddsing.sequenceNbr);
                  if (ObjectUtils.isEmpty(addsing.subSingleProjects)) {
                    let sign = new Array();
                    sign.push(sdsingleProject);
                    addsing.subSingleProjects = sign;
                  } else {
                    addsing.subSingleProjects.push(sdsingleProject);
                  }
                }
              }
            }

          }
          if (item.levelType === 3) {
            let sshUnit = sshUnitProjects.find(k => k.sequenceNbr === item.ssId);
            let cloneSdUnit = ObjectUtils.cloneDeep(sshUnit);
            //通过送审得单项工程 到审定里边找当前单位关联得单项工程
            let single = sssingleProject.find(s => s.sequenceNbr === cloneSdUnit.spId);
            let sdSingProjects = this.getSingleProjectsById(constructId);
            let sDsingleProject = sdSingProjects.find(q => q.ysshSingleId === single.sequenceNbr);
            cloneSdUnit.spId = sDsingleProject.sequenceNbr;
            cloneSdUnit.constructId = constructId;
            cloneSdUnit.ysshUnitId = sshUnit.sequenceNbr;
            cloneSdUnit.ysshSingleId = single.sequenceNbr;
            cloneSdUnit.ysshConstructId = single.constructId;
            cloneSdUnit.sequenceNbr = Snowflake.nextId();
            cloneSdUnit.special = 1;
            ObjectUtils.updatePropertyValue(cloneSdUnit, 'constructId', constructId);
            ObjectUtils.updatePropertyValue(cloneSdUnit, 'unitId', cloneSdUnit.sequenceNbr);
            let addsing = PricingFileFindUtils.getSingleProject(constructId, sDsingleProject.sequenceNbr);
            if (ObjectUtils.isEmpty(addsing.unitProjects)) {
              let unit = new Array();
              unit.push(cloneSdUnit);
              addsing.unitProjects = unit;
            } else {
              addsing.unitProjects.push(cloneSdUnit);
            }
            this.addSSTOSD(cloneSdUnit, constructId);
          }
        }
      }
    }
    //审定
    unitProjects = PricingFileFindUtils.getUnitList(constructId);
    if (ObjectUtils.isNotEmpty(unitProjects) && ObjectUtils.isNotEmpty(sshUnitProjects)) {
      for (let i = 0; i < unitProjects.length; i++) {
        let unitProject = unitProjects[i];
        if (ObjectUtils.isNotEmpty(unitProject.ysshUnitId)) {
          let sshUnitProject = sshUnitProjects.find(item => item.sequenceNbr === unitProject.ysshUnitId);
          await this.init(constructId, unitProject.spId, unitProject.sequenceNbr, shendObj.ysshConstructId, sshUnitProject.spId, sshUnitProject.sequenceNbr);

        }
      }
      // let shenheObjCopy = ConvertUtil.deepCopy(shendObj);
      // let projectSequenceNbrCopy = Snowflake.nextId();
      // shenheObjCopy.sequenceNbr = projectSequenceNbrCopy;
      // ObjectUtils.updatePropertyValue(shenheObjCopy,"constructId",projectSequenceNbrCopy);
      // shenheObjCopy.oldConstructId = shendObj.sequenceNbr;
      // PricingFileWriteUtils.writeToMemory(shenheObjCopy);
      // return ResponseData.success({constructId:shenheObjCopy.sequenceNbr});
      return ResponseData.success({ constructId: shendObj.sequenceNbr });
    } else {
      return ResponseData.fail('未找到单位项目');
    }
  }


  //复制送审数据添加到审定
  async addSSTOSD(unitProject, constructId) {

    let fbfxupdateStrategy = new UpdateStrategy({
      constructId,
      singleId: unitProject.spId,
      unitId: unitProject.sequenceNbr,
      pageType: 'fbfx'
    });
    let csxmupdateStrategy = new UpdateStrategy({
      constructId,
      singleId: unitProject.spId,
      unitId: unitProject.sequenceNbr,
      pageType: 'csxm'
    });
    //清单工程量置为0
    let fbfxQd = unitProject.itemBillProjects.filter(k => k.kind == '03');
    if (ObjectUtils.isNotEmpty(fbfxQd)) {
      for (let item of fbfxQd) {
        await fbfxupdateStrategy.execute({
          pointLineId: item.sequenceNbr,
          upDateInfo: { column: 'quantityExpression', value: '0' },
          skip: false
        });
      }
    }

    //清单工程量置为0
    let csxmQd = unitProject.measureProjectTables.filter(k => k.kind == '03');
    if (ObjectUtils.isNotEmpty(csxmQd)) {
      for (let item of csxmQd) {
        await csxmupdateStrategy.execute({
          pointLineId: item.sequenceNbr,
          upDateInfo: { column: 'quantityExpression', value: '0' },
          skip: false
        });
      }
    }
  }


  /**
   * 选择文件
   */
  async shYsfSaveLocation(arg) {

    let defaultStoragePath = await this.service.yuSuanProject.commonService.getSetStoragePath(null);

    const options = {
      properties: ['openDirectory'],
      defaultPath: this.replaceAfterFirstDot(defaultStoragePath,arg.suffix),
      filters: []
    };
    let result = dialog.showOpenDialogSync(null, options);
    if (ObjectUtils.isEmpty(result)) {
      console.log('未选中任何文件夹');
      return;
    }
    //获取选中的路径
    let filePath = result[0];
    if (!FileUtils.checkFileExistence(filePath)) {
      console.log('路径有误');
      return;
    }
    return filePath;

  }

  /**
   * 取消数据
   * type
   * constructId 为备份的id
   * saveType 0 取消  1保存
   * source 0 是从新建过去额度,1代表项目里面点击的对比匹配
   * @param {*} arg
   */
  async shRecoveryData(arg) {
    let { type, constructId, isSave, saveType, source } = arg;
    if (type == 'match') {
      if (ObjectUtils.isNotEmpty(isSave) && isSave) {
        //取消匹配删除缓存并
        let shenheObj = PricingFileFindUtils.getProjectObjById(constructId);
        if (saveType === 0) {
          //重新赋值旧的id,查询历史比对数据。
          constructId = shenheObj.oldShenDingProjectId;
          delete global.constructProject[shenheObj.sequenceNbr];
          delete global.constructProject[shenheObj.ysshConstructId];
        } else {
          //删除历史的缓存数据,使用备份的数据
          delete global.constructProject[shenheObj.oldShenDingProjectId];
          delete global.constructProject[shenheObj.oldysshConstructId];
        }
        shenheObj = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(shenheObj.oldConstructId)) {
          return ResponseData.fail('未找到审定项目数据');
        }
        // let shenheObj = PricingFileFindUtils.getProjectObjById(newProject.oldConstructId);
        // if(ObjectUtils.isEmpty(shenheObj)){
        //     return ResponseData.fail('未找到审定项目数据');
        // }
        if (source === 0) {
          let defaultStoragePath = await this.service.yuSuanProject.commonService.getSetStoragePath(shenheObj.constructName);
          const dialogOptions = {
            title: '保存文件',
            defaultPath: this.replaceAfterFirstDot(defaultStoragePath,"YSH"),
            filters: [{ name: '云算房文件', extensions: ['YSH'] }]
          };
          //相同文件，则直接打开保存
          let result = dialog.showSaveDialogSync(null, dialogOptions);
          if (result && !result.canceled) {
            let filePath = result;
            //查询是否覆盖路径
            // FileUtils.isOverlayFile(filePath);
            // 在这里处理保存文件的操作
            if (!filePath.endsWith('.YSH')) {
              filePath += '.YSH';
            }
            let sshObj = PricingFileFindUtils.getProjectObjById(shenheObj.ysshConstructId);
            if (ObjectUtils.isEmpty(sshObj)) {
              return ResponseData.fail('未找到送审数据');
            }
            shenheObj.path = filePath;
            //保存到最近使用项目，此方法会重新生成sequnceNbr
            // FileUtils.writeUserHistoryListFile(shenheObj);
            let caiPingJson = {};
            try {
              //获取【工程项目审核汇总表】财评系统数据
              let gCXMSHHZBJson = await this.getGCXMSHHZBJson(constructId,shenheObj.ysshConstructId);
              caiPingJson.gcxmshhzb = gCXMSHHZBJson;
              //获取【工程项目审核详细比对表】财评系统数据
              let gCXMSHXXBDBJson = await this.getGCXMSHXXBDBJson(constructId,shenheObj.ysshConstructId);
              caiPingJson.gcxmshxxbdb = gCXMSHXXBDBJson;
            } catch (e) {
              console.log("审核保存添加财评系统数据报错",e);
            }
            sshObj.biddingType = 5;
            shenheObj.biddingType = 5;
            FileUtils.creatYshFile(sshObj, shenheObj, filePath,caiPingJson);
            ShenHeWinManageUtils.createWindow(shenheObj.constructName, shenheObj.sequenceNbr);
            ProjectFileUtils.writeUserHistoryListFile(shenheObj);
            sshObj.biddingType = sshObj.biddingTypeOld
            shenheObj.biddingType = shenheObj.biddingTypeOld
          } else {
            return ResponseData.fail('未选择保存路径');
          }
        }

      }
      // if(ObjectUtils.isNotEmpty(shenheObj)&&ObjectUtils.isNotEmpty(shenheObj.ysshConstructId)){
      //     //删除缓存数据
      //     delete global.constructProject[shenheObj.ysshConstructId];
      // }

    }
    return ResponseData.success(constructId);
  }


  /**
   * 绑定项目关系
   * @param arg
   * @return {Promise<ResponseData|null>}
   * bangdingType 0 重新绑定  1 解除关联
   */
  async bindingProRelation(arg) {
    let { detailId, detailLevel, matchingId, matchLevel, constructId, bangdingType } = arg;
    if (ObjectUtils.isEmpty(bangdingType)) {
      bangdingType = 0;
    }
    if (detailLevel !== matchLevel) {
      return ResponseData.fail('跨层级匹配错误');
    }
    let shendObj = PricingFileFindUtils.getProjectObjById(constructId);
    if (ObjectUtils.isEmpty(shendObj)) {
      return ResponseData.fail('未找到对应审定数据');
    }
    let ysshConstructId = shendObj.ysshConstructId;
    //审定数据
    let shdMap = ConstructOperationUtil.flatConstructTreeToMapById(constructId);
    let shdConstructOperation = Array.from(shdMap.values()).map(value => value);
    //获取审定的所有单项
    let shdSingleProjects = shdConstructOperation.filter(k => k.levelType == 2);


    //let shdSingleProjects = PricingFileFindUtils.getSingleProjectList(constructId);
    let shdUnits = PricingFileFindUtils.getUnitList(constructId);
    //送审数据
    //let sshSingleProjects = PricingFileFindUtils.getSingleProjectList(ysshConstructId);
    let sshMap = ConstructOperationUtil.flatConstructTreeToMapById(ysshConstructId);
    let sshConstructOperation = Array.from(sshMap.values()).map(value => value);
    //获取审定的所有单项
    let sshSingleProjects = sshConstructOperation.filter(k => k.levelType == 2);

    let sshUnitProjects = PricingFileFindUtils.getUnitList(ysshConstructId);

    let result = null;
    if (detailLevel === 2) {
      if (ObjectUtils.isEmpty(shdSingleProjects) || ObjectUtils.isEmpty(sshSingleProjects)) {
        return ResponseData.fail('未找到对应数据');
      }
      let singleProject = shdSingleProjects.find(item => item.sequenceNbr === detailId);
      if (ObjectUtils.isEmpty(singleProject)) {
        return ResponseData.fail('未找到对应审定数据');
      }

      let sshSingleProject = sshSingleProjects.find(item => item.sequenceNbr === matchingId);
      if (ObjectUtils.isEmpty(sshSingleProject)) {
        return ResponseData.fail('未找到对应送审数据');
      }
      //重置为空
      let ysshSingleProject = shdSingleProjects.find(item => item.ysshSingleId === matchingId);
      if (ObjectUtils.isNotEmpty(ysshSingleProject)) {
        ysshSingleProject.ysshSingleId = null;
      }

      let sshUnits = sshSingleProject.unitProjects;
      //被挂载的关系解除
      if (!ObjectUtils.isEmpty(sshUnits)) {
        sshUnits.forEach(item => {
          let shdUnit = shdUnits.find(item2 => item.sequenceNbr === item2.ysshUnitId);
          if (ObjectUtils.isNotEmpty(shdUnit) && ObjectUtils.isNotEmpty(shdUnit.ysshUnitId)) {
            shdUnit.ysshUnitId = null;
          }
        });
      }
      //重新挂关系
      if (bangdingType === 0) {
        singleProject.ysshSingleId = sshSingleProject.sequenceNbr;
      }
      let shdUnitProjects = singleProject.unitProjects;
      shdUnitProjects ? shdUnitProjects.forEach(sdItemData => {
        //主动解除以前的关系:null;
        if (ObjectUtils.isNotEmpty(sdItemData.ysshUnitId)) {
          sdItemData.ysshUnitId = null;
        }
        if (bangdingType === 0) {
          let sshItemData = sshUnits ? sshUnits.find(ssQdData => {
            if (ObjectUtils.isEmpty(ssQdData.spId) && ObjectUtils.isEmpty(sdItemData.spId)) {
              return ObjectUtils.compareStringsIgnoringSpaces(ssQdData.upName, sdItemData.upName) && ssQdData.deStandardReleaseYear === sdItemData.deStandardReleaseYear;
            }
            if (ObjectUtils.isNotEmpty(ssQdData.spId) && ObjectUtils.isNotEmpty(sdItemData.spId)) {
              let shdSP = shdSingleProjects.find(item => item.sequenceNbr == sdItemData.spId);

              return shdSP && shdSP.ysshSingleId === ssQdData.spId && ObjectUtils.compareStringsIgnoringSpaces(ssQdData.upName, sdItemData.upName) && ssQdData.deStandardReleaseYear === sdItemData.deStandardReleaseYear;
            }

            return false;
          }) : null;
          if (ObjectUtils.isNotEmpty(sshItemData)) {
            sdItemData.ysshUnitId = sshItemData.sequenceNbr;
          }
        }

      }) : null;

    }
    if (detailLevel === 3) {
      result = this.resetYsshGlId(detailId, matchingId, shdUnits, sshUnitProjects, bangdingType);
    }
    if (ObjectUtils.isNotEmpty(result)) {
      return result;
    }
    return ResponseData.success();
  }

  resetYsshGlId(detailId, matchingId, sdUnitProjects, ssUnitProjects, bangdingType) {

    if (ObjectUtils.isEmpty(sdUnitProjects) || ObjectUtils.isEmpty(ssUnitProjects)) {
      return ResponseData.fail('未找到对应数据');
    }
    let ysshUnitProject = sdUnitProjects.find(item => item.ysshUnitId === matchingId);
    if (!ObjectUtils.isEmpty(ysshUnitProject)) {
      ysshUnitProject.ysshUnitId = null;
    }
    let unitProject = sdUnitProjects.find(item => item.sequenceNbr === detailId);

    let ssUnitProject = ssUnitProjects.find(item => item.sequenceNbr === matchingId);
    if (ObjectUtils.isEmpty(unitProject) || ObjectUtils.isEmpty(ssUnitProject)) {
      return ResponseData.fail('未找到对应数据');
    }
    if (unitProject.deStandardReleaseYear != ssUnitProject.deStandardReleaseYear) {
      return ResponseData.fail('当前工程定额序列不一致，不允许匹配。');
    }
    if (bangdingType === 0) {
      unitProject.ysshUnitId = ssUnitProject.sequenceNbr;
    } else {
      unitProject.ysshUnitId = null;
    }
    return null;
  }

  /**
   * 转为预算
   * 转为预算获取文件类型
   */
  async getshProjectToBudgetUrl(arg) {
    let {constructId} = arg;
    return PricingFileFindUtils.getProjectObjById(constructId).biddingType;
  }

  /**
   * 转为预算
   * isContainDelete = true  保留
   * isOpenYsf  == true 打开本地预算项目
   */
  async shProjectToBudget(arg) {
    let { constructId, type, saveLocation, isContainDelete, isOpenYsf } = arg;
    //获取文件名称，以现有逻辑看，文件名称即为工程名称
    let constructName = await FileUtils.getFileNameNoExt(saveLocation);
    let result = { name: arg.name };
    let shdObj = PricingFileFindUtils.getProjectObjById(constructId);
    if (ObjectUtils.isEmpty(shdObj)) {
      return ResponseData.fail('项目不存在');
    }
    if (!saveLocation.endsWith('.YSF') && !saveLocation.endsWith('.YSFZ') &&  !saveLocation.endsWith('.YSFD')) {
      return ResponseData.fail('暂无文件类型后缀');
    }
    let shdObjCopy = ConvertUtil.deepCopy(shdObj);
    //防止generxmljson后内存数据被覆盖
    let projectSequenceNbr = Snowflake.nextId();
    shdObjCopy.sequenceNbr = projectSequenceNbr;
    ObjectUtils.updatePropertyValue(shdObjCopy, 'constructId', projectSequenceNbr);

    PricingFileFindUtils.getUnitListByConstructObj(shdObjCopy).forEach(k => {
      k.itemBillProjects = treeToArray(k.itemBillProjects);
      k.measureProjectTables = treeToArray(k.measureProjectTables);
    });
    let constructProject = null;

    if (type === 1) {
      //审定数据
      result.id = shdObjCopy.sequenceNbr;
      if (ObjectUtils.isNotEmpty(shdObjCopy)) {
        shdObjCopy.path = null;
      }

      // if(!isContainDelete){
      //     //不包含审删数据的话，将审删数据过滤掉
      //     //let unitListCopy = this.getUnitList(shdObjCopy);
      //     let unitListCopy = PricingFileFindUtils.getUnitListByConstructObj(shdObjCopy);
      //     let ssUnitList = PricingFileFindUtils.getUnitList(shdObj.ysshConstructId);
      //     if(ObjectUtils.isNotEmpty(unitListCopy)){
      //         //审定单位工程数据
      //         unitListCopy.forEach(item=>{
      //             //对应送审单位工程
      //             let ssUnit = ssUnitList.find(ssItem=>ssItem.sequenceNbr == item.ysshUnitId);
      //             if(ObjectUtils.isNotEmpty(ssUnit)){
      //                 //送审的措施项目
      //                 let ssMeasureTables = ssUnit.measureProjectTables||[];
      //                 //送审的分部分项
      //                 let ssFbfxDeals = ssUnit.itemBillProjects||[];
      //                 //措施项目
      //                 let measureTables = item.measureProjectTables||[];
      //                 //分部分项
      //                 let fbfxDeals = item.itemBillProjects||[];
      //                 //措施项目过滤掉审删数据
      //                 let newMeasureTables = measureTables.filter((sdItemData,index)=>{
      //                     if(ObjectUtils.isNotEmpty(sdItemData.ysshGlId)){
      //                         let ssItemData = ssMeasureTables.find(ssMeasureItem=>ssMeasureItem.sequenceNbr == sdItemData.ysshGlId);
      //                         if(ObjectUtils.isNotEmpty(ssItemData) && (ObjectUtils.isEmpty(sdItemData.total) || sdItemData.total === 0) && ObjectUtils.isNotEmpty(ssItemData.total) && ssItemData.total !== 0){
      //                             return false;
      //                         }
      //                     }
      //                     //处理 新增措施分部
      //                     if(sdItemData.kind === BranchProjectLevelConstant.fb
      //                         && ObjectUtils.isNotEmpty(sdItemData.ysshKind) && sdItemData.ysshKind === BranchProjectLevelConstant.fb){
      //                         //如果是 新增措施分部 则不导出
      //                         return false;
      //                     }
      //                     return true;
      //                 });
      //                 //措施项目过滤掉审删数据
      //                 let newfbfxDeals = fbfxDeals.filter((sdItemData,index)=>{
      //                     if(ObjectUtils.isNotEmpty(sdItemData.ysshGlId)){
      //                         let ssItemData = ssFbfxDeals.find(ssMeasureItem=>ssMeasureItem.sequenceNbr == sdItemData.ysshGlId);
      //                         if(ObjectUtils.isNotEmpty(ssItemData) && (ObjectUtils.isEmpty(sdItemData.total) || sdItemData.total === 0) && ObjectUtils.isNotEmpty(ssItemData.total) && ssItemData.total !== 0){
      //                             return false;
      //                         }
      //                     }
      //                     //处理 新增清单分部
      //                     if(sdItemData.kind === BranchProjectLevelConstant.fb
      //                         && ObjectUtils.isNotEmpty(sdItemData.ysshKind) && sdItemData.ysshKind === BranchProjectLevelConstant.fb){
      //                         //如果是 新增清单分部 则不导出
      //                         return false;
      //                     }
      //                     return true;
      //                 });
      //                 item.itemBillProjects = newfbfxDeals;
      //                 item.measureProjectTables = newMeasureTables;
      //             }
      //         })
      //     }
      // }

      //let s = toJsonYsfString(shdObjCopy);
      //内存数据写入
      // PricingFileFindUtils.getUnitListByConstructObj(shdObjCopy).forEach(k =>{
      //     k.itemBillProjects = treeToArray(k.itemBillProjects);
      //     k.measureProjectTables = treeToArray(k.measureProjectTables);
      // });


      /*
          zhaocan
          重现修改处理逻辑：保存审删数据 or 删除审删数据
       */
      //获取审定的所有单位工程数据
      let unitList = PricingFileFindUtils.getUnitListByConstructObj(shdObjCopy);
      //获取所有的送审数据的单位工程数据
      let ssUnitList = PricingFileFindUtils.getUnitList(shdObjCopy.ysshConstructId);
      if (ObjectUtils.isNotEmpty(unitList)) {
        for (let i = 0; i < unitList.length; i++) {
          //审定单位工程
          let sdUnit = unitList[i];
          //分部分项
          let fbfxList = sdUnit.itemBillProjects || [];
          //措施项目
          let csxmList = sdUnit.measureProjectTables || [];

          //送审单位工程
          let ssUnit = ObjectUtils.isNotEmpty(ssUnitList) ? ssUnitList.find(ssItem => ssItem.sequenceNbr == sdUnit.ysshUnitId) : undefined;
          let arg4 = {};
          arg4.isAllFlag = false;//
          arg4.isDisplayAllData = true;
          arg4.pageNum = 1;
          arg4.pageSize = 300000;
          arg4.constructId = constructId;
          arg4.singleId = sdUnit.spId;
          arg4.unitId = sdUnit.sequenceNbr;
          arg4.ssConstructId = shdObjCopy.ysshConstructId;
          arg4.ssSingleId = ObjectUtils.isNotEmpty(ssUnit) ? ssUnit.spId : undefined;
          arg4.ssUnitId = ObjectUtils.isNotEmpty(ssUnit) ? ssUnit.sequenceNbr : undefined;

          //分部分项比对数据
          let fbfxPromise = await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxDataMatching(arg4);
          let fbfxCompareList = fbfxPromise.data;
          //措施项目比对数据
          let csxmCompareList = await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(arg4);

          //分部分项的删项数据
          let fbfxDeleteList = fbfxCompareList.filter(i => {
            if ((ObjectUtils.isNotEmpty(i.ysshSysj) && i.ysshSysj.change === YsshssConstant.delete)
              || (ObjectUtils.isNotEmpty(i.ysshKind) && i.ysshKind === BranchProjectLevelConstant.fb)) {
              //所有删项数据 及 新增分部
              return true;
            }
            return false;
          });
          //措施项目的删项数据
          let csxmDeleteList = csxmCompareList.filter(i => {
            if ((ObjectUtils.isNotEmpty(i.ysshSysj) && i.ysshSysj.change === YsshssConstant.delete)
              || (ObjectUtils.isNotEmpty(i.ysshKind) && i.ysshKind === BranchProjectLevelConstant.fb)) {
              //所有删项数据 及 新增分部
              return true;
            }
            return false;
          });

          //获取不含删项的分部分项数据
          if (ObjectUtils.isNotEmpty(fbfxDeleteList)) {
            fbfxList = fbfxList.filter(i => {
              let deleteItem = ObjectUtils.isNotEmpty(fbfxDeleteList) ? fbfxDeleteList.find(f => f.sequenceNbr === i.sequenceNbr) : null;
              if (ObjectUtils.isNotEmpty(deleteItem)) {
                //该条数据是删项数据则删除
                return false;
              }
              return true;
            });
          }
          //获取不含删项的措施项目数据
          if (ObjectUtils.isNotEmpty(csxmDeleteList)) {
            csxmList = csxmList.filter(i => {
              let deleteItem = ObjectUtils.isNotEmpty(csxmDeleteList) ? csxmDeleteList.find(f => f.sequenceNbr === i.sequenceNbr) : null;
              if (ObjectUtils.isNotEmpty(deleteItem)) {
                //该条数据是删项数据则删除
                return false;
              }
              return true;
            });
          }
          //重置数据
          sdUnit.itemBillProjects = fbfxList;
          sdUnit.measureProjectTables = csxmList;

          //包含审删数据
          if (isContainDelete) {
            if (ObjectUtils.isNotEmpty(fbfxDeleteList)) {
              //送审的分部分项
              let ssFbfxList = ObjectUtils.isNotEmpty(ssUnit) && ObjectUtils.isNotEmpty(ssUnit.itemBillProjects) ? treeToArray(ssUnit.itemBillProjects) : [];
              //获取分部分项 新增分部
              let fbfxDeletFb = fbfxDeleteList.find(i => i.kind === BranchProjectLevelConstant.fb && ObjectUtils.isNotEmpty(i.ysshKind) && i.ysshKind === BranchProjectLevelConstant.fb);
              if (ObjectUtils.isNotEmpty(fbfxDeletFb)) {
                fbfxList.push(fbfxDeletFb);
              }
              //先处理清单
              let qdfbfxDeleteList = fbfxDeleteList.filter(f => {
                if (f.kind === BranchProjectLevelConstant.qd) {
                  //在分部分项数据中查找父级数据
                  let pItem = fbfxList.find(i => i.sequenceNbr === f.parentId);
                  //清单数据且在分部分项数据中可查到父级
                  if (ObjectUtils.isNotEmpty(pItem)) {
                    //父级数据在分部分项数据中则返回
                    return true;
                  }
                }
                return false;
              });
              if (ObjectUtils.isNotEmpty(qdfbfxDeleteList)) {
                //isEmpty
                for (let i of qdfbfxDeleteList) {
                  // qdfbfxDeleteList.forEach(i=>{
                  if (ObjectUtils.isNotEmpty(i.isEmpty) && i.isEmpty && ObjectUtils.isNotEmpty(i.ysshSysj)) {
                    //在送审数据中查找原数据然后将 工程量 单价 合价置空
                    let newItem = ssFbfxList.find(f => f.sequenceNbr === i.ssId);
                    if (ObjectUtils.isNotEmpty(newItem)) {
                      //不修改原数据
                      let newSdItem = ConvertUtil.deepCopy(newItem);
                      newSdItem.sequenceNbr = i.sequenceNbr;
                      newSdItem.parentId = i.parentId;
                      newSdItem.quantity = 0;
                      newSdItem.price = 0;
                      newSdItem.total = 0;
                      newSdItem.zjfPrice = 0;
                      newSdItem.zjfTotal = 0;
                      newSdItem.children = [];
                      fbfxList.push(newSdItem);
                    }
                  } else {
                    fbfxList.push(i);
                  }
                  // });
                }
              }
              //在处理定额
              let defbfxDeleteList = fbfxDeleteList.filter(f => {
                if (f.kind === BranchProjectLevelConstant.de) {
                  //在分部分项数据中查找父级数据
                  let pItem = fbfxList.find(i => i.sequenceNbr === f.parentId);
                  //清单数据且在分部分项数据中可查到父级
                  if (ObjectUtils.isNotEmpty(pItem)) {
                    //父级数据在分部分项数据中则返回
                    return true;
                  }
                }
                return false;
              });
              if (ObjectUtils.isNotEmpty(defbfxDeleteList)) {
                //isEmpty
                for (let i of defbfxDeleteList) {
                  // defbfxDeleteList.forEach(i=>{
                  if (i.isEmpty && ObjectUtils.isNotEmpty(i.ysshSysj)) {
                    //在送审数据中查找原数据然后将 工程量 单价 合价置空
                    let newItem = ssFbfxList.find(f => f.sequenceNbr === i.ssId);
                    if (ObjectUtils.isNotEmpty(newItem)) {
                      //不修改原数据
                      let newSdItem = ConvertUtil.deepCopy(newItem);
                      newSdItem.sequenceNbr = i.sequenceNbr;
                      newSdItem.parentId = i.parentId;
                      newSdItem.quantity = 0;
                      newSdItem.price = 0;
                      newSdItem.total = 0;
                      newSdItem.zjfPrice = 0;
                      newSdItem.zjfTotal = 0;
                      newSdItem.children = [];
                      fbfxList.push(newSdItem);
                    }
                  } else {
                    fbfxList.push(i);
                  }
                  // });
                }
              }
              fbfxList = this.reSortResultList(fbfxList);
            }

            //处理措施项目数据
            if (ObjectUtils.isNotEmpty(csxmDeleteList)) {
              //送审的措施项目
              let ssCsxmList = ObjectUtils.isNotEmpty(ssUnit) && ObjectUtils.isNotEmpty(ssUnit.measureProjectTables) ? treeToArray(ssUnit.measureProjectTables) : [];
              //获取措施项目新增分部
              let csxmDeletFb = csxmDeleteList.find(i => i.kind === BranchProjectLevelConstant.fb && ObjectUtils.isNotEmpty(i.ysshKind) && i.ysshKind === BranchProjectLevelConstant.fb);
              if (ObjectUtils.isNotEmpty(csxmDeletFb)) {
                csxmList.push(csxmDeletFb);
              }
              //先处理清单
              let qdcsxmDeleteList = csxmDeleteList.filter(f => {
                if (f.kind === BranchProjectLevelConstant.qd) {
                  //在措施项目数据中查找父级数据
                  let pItem = csxmList.find(i => i.sequenceNbr === f.parentId);
                  //清单数据且在措施项目数据中可查到父级
                  if (ObjectUtils.isNotEmpty(pItem)) {
                    //父级数据在措施项目数据中则返回
                    return true;
                  }
                }
                return false;
              });
              if (ObjectUtils.isNotEmpty(qdcsxmDeleteList)) {

                for (let i of qdcsxmDeleteList) {
                  // qdcsxmDeleteList.forEach(i=>{
                  if (ObjectUtils.isNotEmpty(i.isEmpty) && i.isEmpty && ObjectUtils.isNotEmpty(i.ysshSysj)) {
                    //在送审数据中查找原数据然后将 工程量 单价 合价置空
                    let newItem = ssCsxmList.find(c => c.sequenceNbr === i.ssId);
                    if (ObjectUtils.isNotEmpty(newItem)) {
                      //不修改原数据
                      let newSdItem = ConvertUtil.deepCopy(newItem);
                      newSdItem.sequenceNbr = i.sequenceNbr;
                      newSdItem.parentId = i.parentId;
                      newSdItem.quantity = 0;
                      newSdItem.price = 0;
                      newSdItem.total = 0;
                      newSdItem.zjfPrice = 0;
                      newSdItem.zjfTotal = 0;
                      newSdItem.children = [];
                      csxmList.push(newSdItem);
                    }
                  } else {
                    csxmList.push(i);
                  }
                  // });
                }
              }
              //在处理定额
              let decsxmDeleteList = csxmDeleteList.filter(f => {
                if (f.kind === BranchProjectLevelConstant.de) {
                  //在措施项目数据中查找父级数据
                  let pItem = csxmList.find(i => i.sequenceNbr === f.parentId);
                  //清单数据且在措施项目数据中可查到父级
                  if (ObjectUtils.isNotEmpty(pItem)) {
                    //父级数据在措施项目数据中则返回
                    return true;
                  }
                }
                return false;
              });
              if (ObjectUtils.isNotEmpty(decsxmDeleteList)) {
                for (let i of decsxmDeleteList) {
                  // decsxmDeleteList.forEach(i=>{
                  if (ObjectUtils.isNotEmpty(i.isEmpty) && i.isEmpty && ObjectUtils.isNotEmpty(i.ysshSysj)) {
                    //在送审数据中查找原数据然后将 工程量 单价 合价置空
                    let newItem = ssCsxmList.find(c => c.sequenceNbr === i.ssId);
                    if (ObjectUtils.isNotEmpty(newItem)) {
                      //不修改原数据
                      let newSdItem = ConvertUtil.deepCopy(newItem);
                      newSdItem.sequenceNbr = i.sequenceNbr;
                      newSdItem.parentId = i.parentId;
                      newSdItem.quantity = 0;
                      newSdItem.price = 0;
                      newSdItem.total = 0;
                      newSdItem.zjfPrice = 0;
                      newSdItem.zjfTotal = 0;
                      newSdItem.children = [];
                      csxmList.push(newSdItem);
                    }
                  } else {
                    csxmList.push(i);
                  }
                  // });
                }
              }
              csxmList = this.reSortResultList(csxmList);
            }

            //重置数据
            sdUnit.itemBillProjects = fbfxList;
            sdUnit.measureProjectTables = csxmList;
          }

        }
      }
      //清除数据
      this.deleteProperty(shdObjCopy, 'yssh');
      //清除增减说明
      this.deleteProperty(shdObjCopy, 'changeExplain');
      constructProject = shdObjCopy;
    } else if (type === 2) {
      //送审数据
      let sshObj = PricingFileFindUtils.getProjectObjById(shdObjCopy.ysshConstructId);
      if (ObjectUtils.isEmpty(sshObj)) {
        return ResponseData.fail('送审项目不存在');
      }
      let sshObjCopy = ConvertUtil.deepCopy(sshObj);
      let projectSequenceNbrSS = Snowflake.nextId();
      sshObjCopy.sequenceNbr = projectSequenceNbrSS;
      ObjectUtils.updatePropertyValue(sshObjCopy, 'constructId', projectSequenceNbrSS);
      PricingFileFindUtils.getUnitListByConstructObj(sshObjCopy).forEach(k => {
        k.itemBillProjects = treeToArray(k.itemBillProjects);
        k.measureProjectTables = treeToArray(k.measureProjectTables);
      });
      result.id = sshObjCopy.sequenceNbr;
      constructProject = sshObjCopy;
    } else {
      return ResponseData.fail('参数类型错误');
    }

    //重置项目名称
    constructProject.constructName = constructName;

    result.filePath = saveLocation;
    result.constructProject = constructProject;
    if (isOpenYsf) {
      let result2 = await this.exportYsfFile(result);
      if (result2.status == 200) {
        //导出数据打开预算软件的窗口
        await this.service.yuSuanProject.systemService.openWindowForProject(result2.result);
        return ResponseData.success(0);
      } else {
        return result2;
      }

    } else {

      return await this.exportYsfFile(result);
    }
  }

  async exportYsfFile(args) {
    let { filePath, constructProject } = args;

    //查询选择的路径是否已经有被打开的文件
    let result = await this.service.yuSuanProject.constructProjectFileService.getOneProDataByPath(filePath);

    if (!ObjectUtils.isEmpty(result)) {
      let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
      if (!ObjectUtils.isEmpty(projectObj)) {
        return ResponseData.success(2);
      }
    }
    constructProject.path = filePath;


    let map = ConstructOperationUtil.flatConstructTreeToMapByObj(constructProject);

    let constructOperation = Array.from(map.values()).map(value => value);
    //获取审定的所有单项
    let unitList = constructOperation.filter(k => k.levelType == 3);
    unitList.forEach(k => {
      k.itemBillProjects = arrayToTree(k.itemBillProjects);
      k.measureProjectTables = arrayToTree(k.measureProjectTables);
    });

    //重新刷新所有的项目ID
    await this.service.yuSuanProject.ysfHandlerService.creatYsfFile(constructProject);

    return ResponseData.success(filePath);
  }

  /**
   * 删除属性
   * @param {*} obj
   * @param {*} propertyKey
   */
  deleteProperty(obj, startPropertyKey) {
    if (ObjectUtils.isEmpty(obj)) return;
    for (let key in obj) {
      if (typeof obj[key] === 'object') {
        if (Array.isArray(obj[key])) {
          // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
          obj[key].forEach((item) => {
            this.deleteProperty(item, startPropertyKey);
          });
        } else {
          // 如果属性的值是对象，则递归调用更新函数
          this.deleteProperty(obj[key], startPropertyKey);
        }
      } else if (key.startsWith(startPropertyKey) && !ObjectUtils.isEmpty(obj[key])) {
        // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
        obj[key] = undefined;
        //delete obj.key;
      }
    }
  }

  getSingleProjectsById(sequenceNbr) {
    let map = ConstructOperationUtil.flatConstructTreeToMapById(sequenceNbr);
    let constructOperation = Array.from(map.values()).map(value => value);
    return constructOperation.filter(k => k.levelType == 2);
  }

  getSingleProjectsByObj(obj) {

    let map = ConstructOperationUtil.flatConstructTreeToMapById(sequenceNbr);
    let constructOperation = Array.from(map.values()).map(value => value);
    return constructOperation.filter(k => k.levelType == 2);
  }


  async getUnitProject(args) {
    let { constructId, singleId, unitId } = args;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    return unit;
  }


  /**
   * 保存
   * isClose 0 否 1是
   */
  async saveYshFile(arg) {
    let { constructId,isClose } = arg;
    let shdObj = PricingFileFindUtils.getProjectObjById(constructId);
    let sshObj = PricingFileFindUtils.getProjectObjById(shdObj.ysshConstructId);
    if (ObjectUtils.isEmpty(shdObj) || ObjectUtils.isEmpty(sshObj)) {
      return ResponseData.fail('项目不存在');
    }
    let caiPingJson = {};
    try {
      //获取【工程项目审核汇总表】财评系统数据
      let gCXMSHHZBJson = await this.getGCXMSHHZBJson(constructId,shdObj.ysshConstructId);
      caiPingJson.gcxmshhzb = gCXMSHHZBJson;
      //获取【工程项目审核详细比对表】财评系统数据
      let gCXMSHXXBDBJson = await this.getGCXMSHXXBDBJson(constructId,shdObj.ysshConstructId);
      caiPingJson.gcxmshxxbdb = gCXMSHXXBDBJson;
    } catch (e) {
      console.log("审核保存添加财评系统数据报错",e);
    }
    //修改文件类型
    sshObj.biddingType = 5;
    shdObj.biddingType = 5;
    await FileUtils.updateYshFile(sshObj, shdObj,caiPingJson);
    //再把文件得缓存类型改回来防止调用报错
    sshObj.biddingType = sshObj.biddingTypeOld;
    shdObj.biddingType = shdObj.biddingTypeOld;
    return ResponseData.success();
  }

  async getGCXMSHXXBDBJson(constructId,ysshConstructId){
    let jsonArr = [];

    let arg = {
      "sequenceNbr": constructId,
      "isDuibi": 0
    }
    let projectArr  = await this.generateLevelTreeNodeStructure(arg);
    let id = null;
    for (const project of projectArr) {
      if(project.levelType === 2){//单项
        id = project.id;
        arg = {
            "levelType": 2,
            "constructId": constructId,
            "singleId": project.id
        }
        //审定造价分析
        let resultSD = await this.service.yuSuanProject.unitProjectService.getCostAnalysisData(arg);
        resultSD = resultSD.costAnalysisSingleVOList
        //单项工程
        let objectJson = {};
        objectJson.id = project.id;
        objectJson.parentId = 0;
        objectJson.name = project.name;
        objectJson.sdgcf = resultSD.gczj;
        objectJson.sdsbfjsj = resultSD.sbfsj;
        objectJson.sdzcf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxzcf, resultSD.djcsxzcf), resultSD.zjcsxzcf);
        objectJson.sdrgf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxrgf, resultSD.djcsxrgf), resultSD.zjcsxrgf);
        objectJson.sdclf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxclf, resultSD.djcsxclf), resultSD.zjcsxclf);
        objectJson.sdjxf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxjxf, resultSD.djcsxjxf), resultSD.zjcsxjxf);
        objectJson.sdje = NumberUtil.add(resultSD.gczj, resultSD.sbfsj);
        if(ObjectUtils.isNotEmpty(project.ysshSingleId)){
          arg =  {
            "levelType": 2,
            "constructId": ysshConstructId,
            "singleId": project.ysshSingleId
          }
          //审定造价分析
          resultSD = await this.service.yuSuanProject.unitProjectService.getCostAnalysisData(arg);
          resultSD = resultSD.costAnalysisSingleVOList
          objectJson.ssgcf = resultSD.gczj;
          objectJson.sssbfjsj = resultSD.sbfsj;
          objectJson.sszcf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxzcf, resultSD.djcsxzcf), resultSD.zjcsxzcf);
          objectJson.ssrgf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxrgf, resultSD.djcsxrgf), resultSD.zjcsxrgf);
          objectJson.ssclf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxclf, resultSD.djcsxclf), resultSD.zjcsxclf);
          objectJson.ssjxf =  NumberUtil.add(NumberUtil.add(resultSD.fbfxjxf, resultSD.djcsxjxf), resultSD.zjcsxjxf);
          objectJson.ssje = NumberUtil.add(resultSD.gczj, resultSD.sbfsj);
        }else{
          objectJson.ssgcf =0;
          objectJson.sssbfjsj = 0;
          objectJson.sszcf =  0;
          objectJson.ssrgf = 0;
          objectJson.ssclf =  0;
          objectJson.ssjxf =  0;
          objectJson.ssje = 0;
        }
        jsonArr.push(objectJson);
      }else if(project.levelType === 3){
        arg={
          "levelType": 3,
          "constructId": constructId,
          "singleId": project.parentId,
          "unitId" : project.id
        }
        let unit = PricingFileFindUtils.getUnit(constructId,project.parentId,project.id);
        let taxMode =unit.projectTaxCalculation.taxCalculationMethod;
        //审定造价分析
        let resultSD = await this.service.yuSuanProject.unitProjectService.getCostAnalysisData(arg);
        resultSD = resultSD.costAnalysisUnitVOList;
        //单项工程
        let objectJson = {};
        objectJson.id = project.id;
        objectJson.parentId = id;
        objectJson.name = project.name;
        objectJson.sdgcf = resultSD[1].context;
        objectJson.sdsbfjsj = resultSD.sbfsj;
        objectJson.sdzcf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[3].context, resultSD[9].childrenList[3].context), resultSD[10].childrenList[3].context);
        objectJson.sdrgf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[0].context, resultSD[9].childrenList[0].context), resultSD[10].childrenList[0].context);
        objectJson.sdclf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[1].context, resultSD[9].childrenList[1].context), resultSD[10].childrenList[1].context);
        objectJson.sdjxf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[2].context, resultSD[9].childrenList[2].context), resultSD[10].childrenList[2].context);
        if(taxMode == TaxCalculationMethodEnum.GENERAL.code){
          if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
              objectJson.sdje = NumberUtil.add(resultSD[19].context, resultSD[1].context);
          }else{
            objectJson.sdje = NumberUtil.add(resultSD[14].context, resultSD[1].context);
          }
        }else{
          if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
            objectJson.sdje =NumberUtil.add(resultSD[15].context, resultSD[1].context);
          }else{
            objectJson.sdje = NumberUtil.add(resultSD[14].context, resultSD[1].context);
          }
        }

        if(ObjectUtils.isNotEmpty(project.ysshUnitId) && ObjectUtils.isNotEmpty(project.ysshSingleId)){
          arg = {
            "levelType": 3,
            "constructId": ysshConstructId,
            "singleId": project.ysshSingleId,
            "unitId" : project.ysshUnitId
          }
          unit = PricingFileFindUtils.getUnit(ysshConstructId,project.ysshSingleId,project.ysshUnitId);
          taxMode =unit.projectTaxCalculation.taxCalculationMethod;
          //审定造价分析
          resultSD = await this.service.yuSuanProject.unitProjectService.getCostAnalysisData(arg);
          resultSD = resultSD.costAnalysisUnitVOList;
          objectJson.ssgcf = resultSD[1].context;
          objectJson.sssbfjsj = resultSD.sbfsj;
          objectJson.sszcf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[3].context, resultSD[9].childrenList[3].context), resultSD[10].childrenList[3].context);
          objectJson.ssrgf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[0].context, resultSD[9].childrenList[0].context), resultSD[10].childrenList[0].context);
          objectJson.ssclf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[1].context, resultSD[9].childrenList[1].context), resultSD[10].childrenList[1].context);
          objectJson.ssjxf =  NumberUtil.add(NumberUtil.add(resultSD[6].childrenList[2].context, resultSD[9].childrenList[2].context), resultSD[10].childrenList[2].context);
          if(taxMode == TaxCalculationMethodEnum.GENERAL.code){
            if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
              objectJson.ssje = NumberUtil.add(resultSD[19].context, resultSD[1].context);
            }else{
              objectJson.ssje = NumberUtil.add(resultSD[14].context, resultSD[1].context);
            }
          }else{
            if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
              objectJson.ssje =NumberUtil.add(resultSD[15].context, resultSD[1].context);
            }else{
              objectJson.ssje = NumberUtil.add(resultSD[14].context, resultSD[1].context);
            }
          }
        }else{
          objectJson.ssgcf = 0;
          objectJson.sssbfjsj = 0;
          objectJson.sszcf = 0;
          objectJson.ssrgf =  0;
          objectJson.ssclf = 0;
          objectJson.ssjxf =  0;
          objectJson.ssje = NumberUtil.add(resultSD[14].context, resultSD[1].context);
        }

        jsonArr.push(objectJson);
      }
    }

    return jsonArr;
  }

  async getGCXMSHHZBJson(constructId,ysshConstructId){
    let jsonArr = [];
    //审定造价分析
    let args = {
      "levelType": 1,
      "constructId": constructId
    }
    let resultSD = await this.service.yuSuanProject.unitProjectService.getCostAnalysisData(args);
    //送审
    args = {
      "levelType": 1,
      "constructId": ysshConstructId
    }
    let resultSS = await this.service.yuSuanProject.unitProjectService.getCostAnalysisData(args);
    //审定数据
    let sdgczj = 0;
    let sdfbfx = 0;
    let sdcsxhj = 0;
    let sdsbfsj = 0;
    let sdqtxm = 0;
    let sdgfee = 0;
    let sdsj = 0;
    let sdrgf = 0;
    let sdclf = 0;
    let sdjxf = 0;
    let sdzcf = 0;
    if(ObjectUtils.isNotEmpty(resultSD.costAnalysisConstructVOList)){
      resultSD.costAnalysisConstructVOList.forEach(item=>{
        if(ObjectUtils.isNotEmpty(item.gczj)){
          sdgczj = NumberUtil.add(sdgczj, item.gczj);
        }
        if(ObjectUtils.isNotEmpty(item.sbfsj)){
          sdgczj = NumberUtil.add(sdgczj, item.sbfsj);
        }
        if(ObjectUtils.isNotEmpty(item.fbfx)){
          sdfbfx = NumberUtil.add(sdfbfx, item.fbfx);
        }
        if(ObjectUtils.isNotEmpty(item.csxhj)){
          sdcsxhj = NumberUtil.add(sdcsxhj, item.csxhj);
        }
        if(ObjectUtils.isNotEmpty(item.sbfsj)){
          sdsbfsj = NumberUtil.add(sdsbfsj, item.sbfsj);
        }
        if(ObjectUtils.isNotEmpty(item.qtxm)){
          sdqtxm = NumberUtil.add(sdqtxm, item.qtxm);
        }
        if(ObjectUtils.isNotEmpty(item.gfee)){
          sdgfee = NumberUtil.add(sdgfee, item.gfee);
        }
        if(ObjectUtils.isNotEmpty(item.sj)){
          sdsj = NumberUtil.add(sdsj, item.sj);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxrgf)){
          sdrgf = NumberUtil.add(sdrgf, item.fbfxrgf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxrgf)){
          sdrgf = NumberUtil.add(sdrgf, item.djcsxrgf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxrgf)){
          sdrgf = NumberUtil.add(sdrgf, item.zjcsxrgf);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxclf)){
          sdclf = NumberUtil.add(sdclf, item.fbfxclf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxclf)){
          sdclf = NumberUtil.add(sdclf, item.djcsxclf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxclf)){
          sdclf = NumberUtil.add(sdclf, item.zjcsxclf);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxjxf)){
          sdjxf = NumberUtil.add(sdjxf, item.fbfxjxf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxjxf)){
          sdjxf = NumberUtil.add(sdjxf, item.djcsxjxf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxjxf)){
          sdjxf = NumberUtil.add(sdjxf, item.zjcsxjxf);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxzcf)){
          sdzcf = NumberUtil.add(sdzcf, item.fbfxzcf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxzcf)){
          sdzcf = NumberUtil.add(sdzcf, item.djcsxzcf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxzcf)){
          sdzcf = NumberUtil.add(sdzcf, item.zjcsxzcf);
        }
      })
    }else if(resultSD.costAnalysisUnitVOList){
      //单位工程
      let unit = PricingFileFindUtils.getUnit(constructId,null,null);
      let taxMode =unit.projectTaxCalculation.taxCalculationMethod;
      let costunit = resultSD.costAnalysisUnitVOList;
      sdgczj = NumberUtil.add(sdgczj, costunit[1].context);
      if(taxMode == TaxCalculationMethodEnum.GENERAL.code){
        if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
          sdgczj = NumberUtil.add(costunit[19].context, sdgczj);
          sdsj = NumberUtil.add(sdsj, costunit[19].context);
        }else{
          sdgczj = NumberUtil.add(costunit[14].context, sdgczj);
          sdsj = NumberUtil.add(sdsj, costunit[14].context);
        }
      }else{
        if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
          sdgczj =NumberUtil.add(costunit[15].context, sdgczj);
          sdsj = NumberUtil.add(sdsj, costunit[15].context);
        }else{
          sdgczj = NumberUtil.add(costunit[14].context, sdgczj);
          sdsj = NumberUtil.add(sdsj, costunit[14].context);
        }
      }
      sdfbfx = NumberUtil.add(sdfbfx, costunit[6].context);

      sdcsxhj = NumberUtil.add(sdcsxhj, costunit[8].context);
      sdsbfsj = NumberUtil.add(sdsbfsj, costunit[14].context);
      sdqtxm = NumberUtil.add(sdqtxm, costunit[11].context);
      if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
        sdgfee = NumberUtil.add(sdgfee, costunit[12].context);
      }
      sdzcf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[3].context, costunit[9].childrenList[3].context), costunit[10].childrenList[3].context);
      sdrgf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[0].context, costunit[9].childrenList[0].context), costunit[10].childrenList[0].context);
      sdclf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[1].context, costunit[9].childrenList[1].context), costunit[10].childrenList[1].context);
      sdjxf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[2].context, costunit[9].childrenList[2].context), costunit[10].childrenList[2].context);
    }



    //送审数据
    let ssgczj = 0;
    let ssfbfx = 0;
    let sscsxhj = 0;
    let sssbfsj = 0;
    let ssqtxm = 0;
    let ssgfee = 0;
    let sssj = 0;
    let ssrgf = 0;
    let ssclf = 0;
    let ssjxf = 0;
    let sszcf = 0;
    if(ObjectUtils.isNotEmpty(resultSS.costAnalysisConstructVOList)){
      resultSS.costAnalysisConstructVOList.forEach(item=>{
        if(ObjectUtils.isNotEmpty(item.gczj)){
          ssgczj = NumberUtil.add(ssgczj, item.gczj);
        }
        if(ObjectUtils.isNotEmpty(item.sbfsj)){
          ssgczj = NumberUtil.add(ssgczj, item.sbfsj);
        }
        if(ObjectUtils.isNotEmpty(item.fbfx)){
          ssfbfx = NumberUtil.add(ssfbfx, item.fbfx);
        }
        if(ObjectUtils.isNotEmpty(item.csxhj)){
          sscsxhj = NumberUtil.add(sscsxhj, item.csxhj);
        }
        if(ObjectUtils.isNotEmpty(item.sbfsj)){
          sssbfsj = NumberUtil.add(sssbfsj, item.sbfsj);
        }
        if(ObjectUtils.isNotEmpty(item.qtxm)){
          ssqtxm = NumberUtil.add(ssqtxm, item.qtxm);
        }
        if(ObjectUtils.isNotEmpty(item.gfee)){
          ssgfee = NumberUtil.add(ssgfee, item.gfee);
        }
        if(ObjectUtils.isNotEmpty(item.sj)){
          sssj = NumberUtil.add(sssj, item.sj);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxrgf)){
          ssrgf = NumberUtil.add(ssrgf, item.fbfxrgf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxrgf)){
          ssrgf = NumberUtil.add(ssrgf, item.djcsxrgf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxrgf)){
          ssrgf = NumberUtil.add(ssrgf, item.zjcsxrgf);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxclf)){
          ssclf = NumberUtil.add(ssclf, item.fbfxclf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxclf)){
          ssclf = NumberUtil.add(ssclf, item.djcsxclf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxclf)){
          ssclf = NumberUtil.add(ssclf, item.zjcsxclf);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxjxf)){
          ssjxf = NumberUtil.add(ssjxf, item.fbfxjxf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxjxf)){
          ssjxf = NumberUtil.add(ssjxf, item.djcsxjxf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxjxf)){
          ssjxf = NumberUtil.add(ssjxf, item.zjcsxjxf);
        }
        if(ObjectUtils.isNotEmpty(item.fbfxzcf)){
          sszcf = NumberUtil.add(sszcf, item.fbfxzcf);
        }
        if(ObjectUtils.isNotEmpty(item.djcsxzcf)){
          sszcf = NumberUtil.add(sszcf, item.djcsxzcf);
        }
        if(ObjectUtils.isNotEmpty(item.zjcsxzcf)){
          sszcf = NumberUtil.add(sszcf, item.zjcsxzcf);
        }
      })
    }else if(resultSS.costAnalysisUnitVOList){
      //单位工程
      let unit = PricingFileFindUtils.getUnit(ysshConstructId,null,null);
      let taxMode =unit.projectTaxCalculation.taxCalculationMethod;
      let costunit = resultSS.costAnalysisUnitVOList;
      ssgczj = NumberUtil.add(ssgczj, costunit[1].context);
      if(taxMode == TaxCalculationMethodEnum.GENERAL.code){
        if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
          ssgczj = NumberUtil.add(costunit[19].context, ssgczj);
          sssj = NumberUtil.add(sssj, costunit[19].context);
        }else{
          ssgczj = NumberUtil.add(costunit[14].context, ssgczj);
          sssj = NumberUtil.add(sssj, costunit[14].context);
        }
      }else{
        if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
          ssgczj =NumberUtil.add(costunit[15].context, ssgczj);
          sssj = NumberUtil.add(sssj, costunit[15].context);
        }else{
          ssgczj = NumberUtil.add(costunit[14].context, ssgczj);
          sssj = NumberUtil.add(sssj, costunit[14].context);
        }
      }
      ssfbfx = NumberUtil.add(ssfbfx, costunit[6].context);

      sscsxhj = NumberUtil.add(sscsxhj, costunit[8].context);
      sssbfsj = NumberUtil.add(sssbfsj, costunit[14].context);
      ssqtxm = NumberUtil.add(ssqtxm, costunit[11].context);
      if (unit.constructDeStandard == ConstantUtil.DE_STANDARD_12) {
        ssgfee = NumberUtil.add(ssgfee, costunit[12].context);
      }
      sszcf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[3].context, costunit[9].childrenList[3].context), costunit[10].childrenList[3].context);
      ssrgf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[0].context, costunit[9].childrenList[0].context), costunit[10].childrenList[0].context);
      ssclf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[1].context, costunit[9].childrenList[1].context), costunit[10].childrenList[1].context);
      ssjxf =  NumberUtil.add(NumberUtil.add(costunit[6].childrenList[2].context, costunit[9].childrenList[2].context), costunit[10].childrenList[2].context);
    }



    let fbfxszje = 0;
    let fbfxsjje = 0;
    let csxmszje = 0;
    let csxmsjje = 0;
    let arg = {
      "sequenceNbr": constructId,
      "isDuibi": 0
    }
    let projectArr  = await this.generateLevelTreeNodeStructure(arg);
    let gcxm = projectArr.find(item => item.levelType === 1);//工程项目
    for (const project of projectArr) {
      if(project.levelType === 3){//单位工程
        //查询分部分项对比接口获取增减金额
          let argFBFX={
            "ssConstructId": gcxm.ysshConstructId,
            "ssSingleId": project.ysshSingleId,
            "ssUnitId": project.ysshUnitId,
            "constructId": gcxm.id,
            "singleId": project.parentId,
            "unitId": project.id,
            "pageNum": 1,
            "pageSize": 300000,
            "isAllFlag": true,
            "isDisplayAllData": true
          }
        let fbfxArr =  await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxDataMatching(argFBFX);
        let qdArr = fbfxArr.data.filter(fbfx => fbfx.kind === BranchProjectLevelConstant.qd);
        for(let qd of qdArr){
          if(ObjectUtils.isNotEmpty(qd) && ObjectUtils.isNotEmpty(qd.ysshSysj) && ObjectUtils.isNotEmpty(qd.ysshSysj.changePrice)
              && qd.ysshSysj.changePrice !== 0 && qd.ysshSysj.changePrice !== 0.00){
              if(qd.ysshSysj.changePrice>0){
                fbfxszje = NumberUtil.add(fbfxszje, qd.ysshSysj.changePrice);
              }else{
                fbfxsjje = NumberUtil.add(fbfxsjje, qd.ysshSysj.changePrice);
              }
          }
        }
        //获取措施项目
        argFBFX.type = 0;
        let resultList =  await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(argFBFX);
        let csxmqdArr = resultList.filter(csxm => csxm.kind === BranchProjectLevelConstant.qd);
        for(let qd of csxmqdArr){
          if(ObjectUtils.isNotEmpty(qd) && ObjectUtils.isNotEmpty(qd.ysshSysj) && ObjectUtils.isNotEmpty(qd.ysshSysj.changePrice)
              && qd.ysshSysj.changePrice !== 0 && qd.ysshSysj.changePrice !== 0.00){
            if(qd.ysshSysj.changePrice>0){
              csxmszje = NumberUtil.add(csxmszje, qd.ysshSysj.changePrice);
            }else{
              csxmsjje = NumberUtil.add(csxmsjje, qd.ysshSysj.changePrice);
            }
          }
        }
      }
    }
    //工程造价
    let objectJson = {};
    objectJson.name = "工程造价";
    objectJson.ssje = ssgczj;
    objectJson.sdje = sdgczj;
    objectJson.changePrice =  NumberUtil.subtract(sdgczj, ssgczj);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdgczj, ssgczj), ssgczj);
    jsonArr.push(objectJson);
    //工程造价(大写)
    objectJson = {};
    objectJson.name = "工程造价(大写)";
    objectJson.ssje = NumberUtil.numToCny(ssgczj);
    objectJson.sdje = NumberUtil.numToCny(sdgczj);
    objectJson.changePrice = NumberUtil.numToCny(NumberUtil.subtract(sdgczj, ssgczj));
    objectJson.changeRatio =  NumberUtil.numToCny(this._calculateDiffTotalRate(NumberUtil.subtract(sdgczj, ssgczj), ssgczj));
    jsonArr.push(objectJson);
    //分部分项金额
    objectJson = {};
    objectJson.name = "分部分项金额";
    objectJson.ssje = ssfbfx;
    objectJson.sdje = sdfbfx;
    objectJson.changePrice = NumberUtil.subtract(sdfbfx, ssfbfx);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdfbfx, ssfbfx), ssfbfx);
    jsonArr.push(objectJson);
    //分部分项审增金额
    objectJson = {};
    objectJson.name = "分部分项审增金额";
    objectJson.ssje = "";
    objectJson.sdje = fbfxszje;
    objectJson.changePrice = "";
    objectJson.changeRatio = "";
    jsonArr.push(objectJson);
    //分部分项审减金额
    objectJson = {};
    objectJson.name = "分部分项审减金额";
    objectJson.ssje = "";
    objectJson.sdje = fbfxsjje;
    objectJson.changePrice = "";
    objectJson.changeRatio = "";
    jsonArr.push(objectJson);
    //             分部分项增减金额
    objectJson = {};
    objectJson.name = "分部分项增减金额";
    objectJson.ssje = "";
    objectJson.sdje = NumberUtil.add(fbfxszje, fbfxsjje);
    objectJson.changePrice = "";
    objectJson.changeRatio = "";
    jsonArr.push(objectJson);
    //   措施项目金额
    objectJson = {};
    objectJson.name = "措施项目金额";
    objectJson.ssje = sscsxhj;
    objectJson.sdje = sdcsxhj;
    objectJson.changePrice = NumberUtil.subtract(sdcsxhj, sscsxhj);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdcsxhj, sscsxhj), sscsxhj);
    jsonArr.push(objectJson);
    // 措施项目审增金额
    objectJson = {};
    objectJson.name = "措施项目审增金额";
    objectJson.ssje = "";
    objectJson.sdje = csxmszje;
    objectJson.changePrice = "";
    objectJson.changeRatio = "";
    jsonArr.push(objectJson);
    // 措施项目审减金额
    objectJson = {};
    objectJson.name = "措施项目审减金额";
    objectJson.ssje = "";
    objectJson.sdje = csxmsjje;
    objectJson.changePrice = "";
    objectJson.changeRatio = "";
    jsonArr.push(objectJson);
    // 措施项目增减金额
    objectJson = {};
    objectJson.name = "措施项目增减金额";
    objectJson.ssje = "";
    objectJson.sdje = NumberUtil.add(csxmszje, csxmsjje);
    objectJson.changePrice = "";
    objectJson.changeRatio = "";
    jsonArr.push(objectJson);
    //      其他项目金额
    objectJson = {};
    objectJson.name = "其他项目金额";
    objectJson.ssje = ssqtxm;
    objectJson.sdje = sdqtxm;
    objectJson.changePrice = NumberUtil.subtract(sdqtxm, ssqtxm);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdqtxm, ssqtxm), ssqtxm);
    jsonArr.push(objectJson);
    //      人工费金额
    objectJson = {};
    objectJson.name = "人工费金额";
    objectJson.ssje = ssrgf;
    objectJson.sdje = sdrgf;
    objectJson.changePrice = NumberUtil.subtract(sdrgf, ssrgf);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdrgf, ssrgf), ssrgf);
    jsonArr.push(objectJson);
    //         材料费金额
    objectJson = {};
    objectJson.name = "材料费金额";
    objectJson.ssje = ssclf;
    objectJson.sdje = sdclf;
    objectJson.changePrice = NumberUtil.subtract(sdclf, ssclf);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdclf, ssclf), ssclf);
    jsonArr.push(objectJson);
    //            机械费金额
    objectJson = {};
    objectJson.name = "机械费金额";
    objectJson.ssje = ssjxf;
    objectJson.sdje = sdjxf;
    objectJson.changePrice = NumberUtil.subtract(sdjxf, ssjxf);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdjxf, ssjxf), ssjxf);
    jsonArr.push(objectJson);
    //               设备费金额
    objectJson = {};
    objectJson.name = "设备费金额";
    objectJson.ssje = sssbfsj;
    objectJson.sdje = sdsbfsj;
    objectJson.changePrice = NumberUtil.subtract(sdsbfsj, sssbfsj);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdsbfsj, sssbfsj), sssbfsj);
    jsonArr.push(objectJson);
    //                主材费金额
    objectJson = {};
    objectJson.name = "主材费金额";
    objectJson.ssje = sszcf;
    objectJson.sdje = sdzcf;
    objectJson.changePrice = NumberUtil.subtract(sdzcf, sszcf);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdzcf, sszcf), sszcf);
    jsonArr.push(objectJson);
    //                规费金额
    if(resultSD.deStandardReleaseYear === "12"){
      objectJson = {};
      objectJson.name = "规费金额";
      objectJson.ssje = ssgfee;
      objectJson.sdje = sdgfee;
      objectJson.changePrice = NumberUtil.subtract(sdgfee, ssgfee);
      objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdgfee, ssgfee), ssgfee);
      jsonArr.push(objectJson);
    }
    //                   税金金额
    objectJson = {};
    objectJson.name = "税金金额";
    objectJson.ssje = sssj;
    objectJson.sdje = sdsj;
    objectJson.changePrice = NumberUtil.subtract(sdsj, sssj);
    objectJson.changeRatio = this._calculateDiffTotalRate(NumberUtil.subtract(sdsj, sssj), sssj);
    jsonArr.push(objectJson);
    return jsonArr;
  }


  /**
   * 计算百分比
   * @param diffTotal
   * @param ssTotal 送审
   */
  _calculateDiffTotalRate(diffTotal, ssTotal){
    let diffTotalRate = 100;
    //为0 无价差
    if(diffTotal === 0){
      diffTotalRate = 0;
    }else if(ssTotal === 0){
      diffTotalRate = 100;
    }else{
      diffTotalRate = NumberUtil.divide(diffTotal, ssTotal);
      //百分比 *100且保留两位小数
      diffTotalRate = NumberUtil.numberScale2(NumberUtil.multiply(diffTotalRate, 100));
    }
    return diffTotalRate;
  }

  async updateSignProjects(signProjects) {
    signProjects.forEach(s => {
      //修改单项工程数据ID
      s.oldSequenceNbr = s.sequenceNbr;
      s.sequenceNbr = Snowflake.nextId();
      let unitProjects = s.unitProjects;
      if (ObjectUtils.isNotEmpty(unitProjects)) {
        this.updatesubUnitProjects(unitProjects, s.sequenceNbr);
      }
      let subSingleProjects = s.subSingleProjects;
      if (ObjectUtils.isNotEmpty(subSingleProjects)) {
        this.updatesubSingleProjects(subSingleProjects, s.sequenceNbr);
      }
    });
  }


  async updatesubSingleProjects(subSingleProjects, sequenceNbr) {
    subSingleProjects.forEach(s => {
      s.oldSequenceNbr = s.sequenceNbr;
      s.sequenceNbr = Snowflake.nextId();
      s.parentId = sequenceNbr;
      let unitProjects = s.unitProjects;
      if (ObjectUtils.isNotEmpty(unitProjects)) {
        this.updatesubUnitProjects(unitProjects, s.sequenceNbr);
      }
      let subSingleProjects = s.subSingleProjects;
      if (ObjectUtils.isNotEmpty(subSingleProjects)) {
        this.updatesubSingleProjects(subSingleProjects, s.sequenceNbr);
      }
    });
  }


  async updatesubUnitProjects(unitProjects, spid) {
    unitProjects.forEach(s => {
      s.oldSequenceNbr = s.sequenceNbr;
      s.sequenceNbr = Snowflake.nextId();
      s.spId = spid;
      if (ObjectUtils.isNotEmpty(s)) {
        ObjectUtils.updatePropertyValue(s, 'singleId', spid);
        ObjectUtils.updatePropertyValue(s, 'unitId', s.sequenceNbr);
        //针对标记修改送审中新增数据的处理逻辑，此处对原有数据进行标记
        this.setOldValueFlag(s);
      }
    });
  }

  /**
   * 在原有数据中添加标记来区分是否后期新增
   * @param s
   */
  async setOldValueFlag(unit) {
    //分部分项数据
    let itemBillProjects = unit.itemBillProjects;
    if (ObjectUtils.isNotEmpty(itemBillProjects)) {
      itemBillProjects = treeToArray(itemBillProjects);
      //新增原有数据标记：oldSongShenData = 1
      itemBillProjects.forEach(item => item[YsshssConstant.oldSongShenData] = 1);
    }
    //措施项目数据
    let measureProjectTables = unit.measureProjectTables;
    if (ObjectUtils.isNotEmpty(measureProjectTables)) {
      measureProjectTables = treeToArray(measureProjectTables);
      //新增原有数据标记：oldSongShenData = 1
      measureProjectTables.forEach(item => item[YsshssConstant.oldSongShenData] = 1);
    }
  }

  /**
   * 对分部分项和措施项目数据重新排序
   * @param dataList
   * @returns {undefined}
   */
  reSortResultList(resultList) {
    if (ObjectUtils.isEmpty(resultList)) {
      return resultList;
    }
    let sortResultList = [];
    //顶级数据
    let top = resultList.find(item => item.kind === StepItemCostLevelConstant.top);
    sortResultList.push(top);
    let btList = resultList.filter(item => item.kind === StepItemCostLevelConstant.bt)
      .sort((a, b) => a.index - b.index);
    if (ObjectUtils.isEmpty(btList)) {
      btList = resultList.filter(item => item.kind === StepItemCostLevelConstant.qd)
        .sort((a, b) => a.index - b.index);
    }

    //递归排序插入子集数据
    this.sortAllResultList(btList, resultList, sortResultList);

    return sortResultList;
  }

  /**
   * 递归排序
   * @param dataList
   * @param resultList
   * @param sortResultList
   */
  sortAllResultList(dataList, resultList, sortResultList) {
    if (ObjectUtils.isEmpty(sortResultList)) {
      sortResultList = [];
    }
    if (ObjectUtils.isEmpty(dataList) || ObjectUtils.isEmpty(resultList)) {
      return sortResultList;
    }
    for (let item of dataList) {
      //装入最终集合
      sortResultList.push(item);
      //查询是否含有子集
      let childenList = resultList.filter(r => r.parentId === item.sequenceNbr);
      if (ObjectUtils.isNotEmpty(childenList)) {
        //排序
        childenList = childenList.sort((a, b) => a.index - b.index);
        //递归处理子集数据
        this.sortAllResultList(childenList, resultList, sortResultList);
      }

    }

  }


   replaceAfterFirstDot(str, newContent) {
     let url = "";
     if(str.includes("YSFZ")){
       url = str.replace("YSFZ",newContent);
     }else if(str.includes("YSFD")){
       url = str.replace("YSFD",newContent);
     }else{
       url = str.replace("YSF",newContent);
     }
     return  url;
  }


  // 拖拽项目结构
  async dragDropProjectStructure(param) {
    let constructId = param.id;
    let oldConstructFlatMap = await ConstructOperationUtil.flatConstructTreeToMapById(constructId);
    // 1. 工程项目处理
    let constructObj = await PricingFileFindUtils.getProjectObjById(constructId);
    return await this.dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj);
  }


  /**
   * 工程项目重新赋值
   * @param param 前端的树
   * @param oldConstructFlatMap 内存中的数据平铺
   * @param constructObj  内存中的数据
   * @returns {Promise<ResponseData>}
   */
  async dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj) {

    // 2. 单项工程处理
    if (ObjectUtils.isNotEmpty(param.children)) {
      for (const item of param.children) {
        if (item.levelType == 2) {
          await this._editSingleProjectStructure(param, item, oldConstructFlatMap, constructObj, true, constructObj);
        } else if (item.levelType == 3) {
          await this._editUnitStructure(param, null, item, oldConstructFlatMap, constructObj, true, constructObj);
        }
      }
    }
    return ResponseData.success();
  }


  async _editSingleProjectStructure(constructParam, singleParam, oldConstructFlatMap, parent, ifParentIsConstruct, parentSame) {
    let oldSingle = null;
    //送审的工程项目转换成map
    let oldConstructFlatMapSS = await ConstructOperationUtil.flatConstructTreeToMapById(parentSame.ysshConstructId);
    if(singleParam.copyType === 0){
      oldSingle = oldConstructFlatMapSS.get(singleParam.ysshSingleId);
    }else{
      oldSingle = oldConstructFlatMap.get(singleParam.id);
    }

    let newSingle = await ConvertUtil.deepCopy(oldSingle);
    let constructId = constructParam.id;

    if (!ObjectUtils.isEmpty(singleParam.copyFromId)) {
      let newSingle = await ConvertUtil.deepCopy(oldSingle);
      newSingle.sequenceNbr = Snowflake.nextId();
      newSingle.spId = parent.sequenceNbr;
      newSingle.projectName = singleParam.name;
      //复制过来的校验名称是否有重复
      await this.repeatInitSingleName(ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleParam.id);
      //重新赋值单项下的单位id和单项spId
      await this.repeatInitSingleItemId(newSingle);
      let oldysshSingleId = null;
      if(singleParam.copyType === 2){
        oldysshSingleId = newSingle.ysshSingleId;
      }else{
        oldysshSingleId = singleParam.ysshSingleId;
      }
      newSingle.ysshSingleId = Snowflake.nextId();
      if (ifParentIsConstruct) {
        if(singleParam.copyType === 2 || singleParam.copyType === 1){
          if(ObjectUtils.isEmpty(parentSame.singleProjects)){
            parentSame.singleProjects = new Array();
          }
          parentSame.singleProjects.push(newSingle);
        }
        //添加送审复制到内容
       if(singleParam.copyType === 2 || singleParam.copyType === 0){
         //放入的对象
         let constructObjSS = await PricingFileFindUtils.getProjectObjById(parentSame.ysshConstructId);
         //复制的对象
         let singObjectSS = await PricingFileFindUtils.getSingleProject(parentSame.ysshConstructId,oldysshSingleId);
         let copysingObjectSS = await ConvertUtil.deepCopy(singObjectSS);
         copysingObjectSS.sequenceNbr = newSingle.ysshSingleId;
         copysingObjectSS.projectName = singleParam.name;
         copysingObjectSS.spId = constructObjSS.sequenceNbr;
         //复制过来的校验名称是否有重复
         await this.repeatInitSingleName(ifParentIsConstruct, constructObjSS, oldConstructFlatMapSS, copysingObjectSS, oldysshSingleId);
         //重新赋值单项下的单位id和单项spId
         await this.repeatInitSingleItemId(copysingObjectSS);
         if(ObjectUtils.isEmpty(constructObjSS.subSingleProjects)){
           constructObjSS.subSingleProjects = new Array();
         }
         constructObjSS.subSingleProjects.push(copysingObjectSS);
       }
      } else {
        if(singleParam.copyType === 2 || singleParam.copyType === 1) {
          let newsingObject = await PricingFileFindUtils.getSingleProject(parentSame.sequenceNbr,  parent.sequenceNbr);
          if(ObjectUtils.isEmpty(parent.ysshSingleId)){
            delete newSingle.matchFlag;
            delete newSingle.ysshSingleId;
          }
          if(ObjectUtils.isEmpty(newsingObject.subSingleProjects)){
            newsingObject.subSingleProjects = new Array();
          }
          newsingObject.subSingleProjects.push(newSingle);
        }
        //添加送审复制到内容
        if((singleParam.copyType === 2 || singleParam.copyType === 0) && ObjectUtils.isNotEmpty(parent.ysshSingleId)) {
          //放入的对象
          let newsingObjectSS = await PricingFileFindUtils.getSingleProject(parentSame.ysshConstructId,  parent.ysshSingleId);
          //复制的对象
          let singObjectSS = await PricingFileFindUtils.getSingleProject(parentSame.ysshConstructId,oldysshSingleId);
          let copysingObjectSS = await ConvertUtil.deepCopy(singObjectSS);
          copysingObjectSS.spId = newsingObjectSS.sequenceNbr;
          if(ObjectUtils.isNotEmpty(newSingle.ysshSingleId)){
            copysingObjectSS.sequenceNbr = newSingle.ysshSingleId;
          }else{
            copysingObjectSS.sequenceNbr = Snowflake.nextId();
          }
          copysingObjectSS.projectName = singleParam.name;
          //复制过来的校验名称是否有重复
          await this.repeatInitSingleName(ifParentIsConstruct, newsingObjectSS, oldConstructFlatMapSS, copysingObjectSS, parent.ysshSingleId);
          //重新赋值单项下的单位id和单项spId
          await this.repeatInitSingleItemId(copysingObjectSS);
          if(ObjectUtils.isEmpty(newsingObjectSS.subSingleProjects)){
            let arr = [];
            arr.push(copysingObjectSS);
            newsingObjectSS.subSingleProjects = arr;
          }else{
            newsingObjectSS.subSingleProjects.push(copysingObjectSS);
          }
          if(singleParam.copyType === 0){//添加审定的数据
            //送审得单项工程 //克隆出添加得审定数据
            let copysingObjectSD = await ConvertUtil.deepCopy(copysingObjectSS);
            copysingObjectSD.sequenceNbr = Snowflake.nextId();
            copysingObjectSD.ysshSingleId = copysingObjectSS.sequenceNbr;
            copysingObjectSD.ysshConstructId = parentSame.ysshConstructId;
            copysingObjectSD.special = 1;//添加得标识
            copysingObjectSD.parentId = parent.sequenceNbr;
            ObjectUtils.updatePropertyValue(copysingObjectSD, 'constructId', parentSame.sequenceNbr);
            let newUnit = new Array();
            copysingObjectSD.unitProjects = newUnit;
            copysingObjectSD.subSingleProjects = null;
            //放入的对象
            let newsingObjectSD = await PricingFileFindUtils.getSingleProject(parentSame.sequenceNbr,  parent.sequenceNbr);
            if(ObjectUtils.isEmpty(newsingObjectSS.subSingleProjects)){
              newsingObjectSD.subSingleProjects = new Array();
              newsingObjectSD.subSingleProjects.push(copysingObjectSD)
            }else{
              newsingObjectSD.subSingleProjects.push(copysingObjectSD)
            }
          }
        }
      }
    } else {
        if (ObjectUtils.isNotEmpty(singleParam.children)) {
          for (const item of singleParam.children) {
            if (item.levelType == 2) {
              await this._editSingleProjectStructure(constructParam, item, oldConstructFlatMap, newSingle, false, parentSame);
            } else if (item.levelType == 3) {
              await this._editUnitStructure(constructParam, newSingle, item, oldConstructFlatMap, newSingle, false, parentSame);
            }
          }
        }
    }
  }



  async _editUnitStructure(constructParam, singleObject, unitParam, oldConstructFlatMap, parent, ifParentIsConstruct, constructObj) {

    let oldUnit = null;
    //送审的工程项目转换成map
    let oldConstructFlatMapSS = await ConstructOperationUtil.flatConstructTreeToMapById(constructObj.ysshConstructId);
    if(unitParam.copyType === 0){
      oldUnit = oldConstructFlatMapSS.get(unitParam.ysshUnitId);
    }else{
      oldUnit = oldConstructFlatMap.get(unitParam.id);
    }

    let newUnit = await ConvertUtil.deepCopy(oldUnit);

    let constructId = constructParam.id;

    // let constructObj = PricingFileFindUtils.getProjectObjById(constructId);
    let singleId = null;
    if(ObjectUtils.isNotEmpty(singleObject)){
      singleId = singleObject.sequenceNbr;
    }

    if (!ObjectUtils.isEmpty(unitParam.copyFromId)) {
      //代表是复制的单位
      newUnit.sequenceNbr = Snowflake.nextId();
      newUnit.spId = singleId;
      newUnit.upName = unitParam.name;

      let oldysshUnitId = null;
      if(unitParam.copyType === 2){
        oldysshUnitId = newUnit.ysshUnitId;
        newUnit.ysshUnitId = Snowflake.nextId();
      }else{
        oldysshUnitId = unitParam.ysshUnitId;
      }


      if (ifParentIsConstruct) {
        if(unitParam.copyType === 2 || unitParam.copyType === 1) {
          if(ObjectUtils.isEmpty(constructObj.unitProjectArray)){
            constructObj.unitProjectArray = new Array();
          }
          constructObj.unitProjectArray.push(newUnit);
        }
        //添加到送审
        if(unitParam.copyType === 2 || unitParam.copyType === 0) {
          //放入的对象
          let constructObjSS = await PricingFileFindUtils.getSingleProject(constructObj.ysshConstructId);
          //复制的对象
          let UnitObject = await PricingFileFindUtils.getUnit(constructObj.ysshConstructId,newUnit.ysshSingleId,oldysshUnitId);
          let copyUnitObject = await ConvertUtil.deepCopy(UnitObject);
          copyUnitObject.sequenceNbr = newUnit.ysshUnitId;
          copyUnitObject.spId = null;
          copyUnitObject.upName = unitParam.name;
          if(ObjectUtils.isEmpty(constructObjSS.unitProjectArray)){
            constructObjSS.unitProjectArray = new Array();
          }
          constructObjSS.unitProjectArray.push(copyUnitObject);
        }
      } else {
        if(unitParam.copyType === 2 || unitParam.copyType === 1){
          //重新赋值单位名称（有重复的话+1）
          await this.repeatInitUnitName(constructId, singleId, newUnit);
          //重新赋值分部分项等数据的unitId和spId
          await this.repeatInitUnitItemId(newUnit);
          //重新复制人材机ID和人材机子集ID
          await this.repeatRcj(newUnit.constructProjectRcjs,newUnit.rcjDetailList)
          let newsingObject = await PricingFileFindUtils.getSingleProject(constructObj.sequenceNbr,  parent.sequenceNbr);
          if(ObjectUtils.isEmpty(parent.ysshSingleId)){
              delete newUnit.matchFlag;
              delete newUnit.ysshUnitId;
          }
          if(ObjectUtils.isEmpty(newsingObject.unitProjects)){
            newsingObject.unitProjects = new Array();
          }
          newsingObject.unitProjects.push(newUnit);
        }

        //添加到送审
        if((unitParam.copyType === 2 || unitParam.copyType === 0) && ObjectUtils.isNotEmpty(parent.ysshSingleId)) {
          //放入的对象
          let singObjectADD = await PricingFileFindUtils.getSingleProject(constructObj.ysshConstructId,parent.ysshSingleId);
          //复制的对象
          let unitObjectList = await PricingFileFindUtils.getUnitList(constructObj.ysshConstructId);
          let UnitObject = unitObjectList.find(item => item.sequenceNbr === oldysshUnitId);
          let copyUnitObject = await ConvertUtil.deepCopy(UnitObject);
          if(ObjectUtils.isNotEmpty(newUnit.ysshUnitId)){
            copyUnitObject.sequenceNbr = newUnit.ysshUnitId;
          }  else{
            copyUnitObject.sequenceNbr = Snowflake.nextId();
          }
            copyUnitObject.spId = singObjectADD.sequenceNbr;
            copyUnitObject.upName = unitParam.name;
           //重新赋值单位名称（有重复的话+1）
           await this.repeatInitUnitName(constructObj.ysshConstructId, singObjectADD.sequenceNbr, copyUnitObject);
           //重新赋值分部分项等数据的unitId和spId
           await this.repeatInitUnitItemId(copyUnitObject);
           //重新复制人材机ID和人材机子集ID
           await this.repeatRcj(copyUnitObject.constructProjectRcjs,copyUnitObject.rcjDetailList)
            if(ObjectUtils.isEmpty(singObjectADD.unitProjects)){
              singObjectADD.unitProjects  = new Array();
              singObjectADD.unitProjects.push(copyUnitObject);
            }else{
              singObjectADD.unitProjects.push(copyUnitObject);
            }
            //之前是送审要复制一份到审定
            if(unitParam.copyType === 0){
              //放入的对象
              let singObjectADDsd = await PricingFileFindUtils.getSingleProject(constructObj.sequenceNbr,parent.sequenceNbr);
              let copyUnitObjectsd = await ConvertUtil.deepCopy(copyUnitObject);
              copyUnitObjectsd.ysshUnitId = copyUnitObject.sequenceNbr;
              copyUnitObjectsd.sequenceNbr = Snowflake.nextId();
              copyUnitObjectsd.spId = singObjectADDsd.sequenceNbr;
              copyUnitObjectsd.special = 1;
              copyUnitObjectsd.ysshSingleId = parent.ysshSingleId;
              copyUnitObjectsd.ysshConstructId = constructObj.ysshConstructId
              copyUnitObjectsd.constructId = constructObj.sequenceNbr;
              delete copyUnitObjectsd.matchFlag;
              ObjectUtils.updatePropertyValue(copyUnitObjectsd, 'constructId', constructObj.sequenceNbr);
              ObjectUtils.updatePropertyValue(copyUnitObjectsd, 'unitId', copyUnitObjectsd.sequenceNbr);
              if(ObjectUtils.isEmpty(singObjectADDsd.unitProjects)){
                singObjectADDsd.unitProjects  = new Array();
                singObjectADDsd.unitProjects.push(copyUnitObjectsd);
              }else{
                singObjectADDsd.unitProjects.push(copyUnitObjectsd);
              }
              this.addSSTOSD(copyUnitObjectsd, constructObj.sequenceNbr);
            }
        }else if(unitParam.copyType === 0 && ObjectUtils.isEmpty(parent.ysshSingleId)){//送审复制到审定
          //放入的对象
          let list = [];
          this.getParentSingleByChildId(constructObj.sequenceNbr,parent.sequenceNbr,list);
          const reversedArray = [...list].reverse();
          let singIdSS = null;
          let singIdSD = null;
          for (let item of reversedArray) {
              if(ObjectUtils.isEmpty(item.ysshSingleId)){
                item.ysshSingleId = Snowflake.nextId();
                let itemCopy = ConvertUtil.deepCopy(item);
                //审定赋值关联关系
               /* itemCopy.ysshSingleId = itemCopy.sequenceNbr;
                itemCopy.ysshConstructId =  constructObj.ysshConstructId;*/
                //复制审定到送审
                itemCopy.sequenceNbr = item.ysshSingleId
                itemCopy.special = 1;//添加得标识
                ObjectUtils.updatePropertyValue(itemCopy, 'constructId', constructObj.ysshConstructId);
                let newUnit = new Array();
                itemCopy.parentId = null;
                itemCopy.unitProjects = newUnit;
                itemCopy.subSingleProjects = null;
                //查找审定的父级并查询送审的关联关系 找出送审的放入位置
                if(ObjectUtils.isNotEmpty(item.parentId) && item.parentId ===constructObj.sequenceNbr){
                  let projectSSADD = await PricingFileFindUtils.getProjectObjById(constructObj.ysshConstructId);
                  itemCopy.parentId = constructObj.ysshConstructId;
                  projectSSADD.singleProjects.push(itemCopy);
                }else{//parentId为空的情况
                  //通过查询审定的上一级  来确定送审放入的位置
                  let sdsign = PricingFileFindUtils.getSingleProject(constructObj.sequenceNbr,item.parentId);
                  if(ObjectUtils.isNotEmpty(sdsign.ysshSingleId)){
                    //放入的送审sign
                    let sssign = PricingFileFindUtils.getSingleProject(constructObj.ysshConstructId,sdsign.ysshSingleId);
                    itemCopy.parentId = sssign.sequenceNbr;
                    if(ObjectUtils.isEmpty(sssign.subSingleProjects)){
                      sssign.subSingleProjects = [];
                      sssign.subSingleProjects.push(itemCopy);
                    }else{
                      sssign.subSingleProjects.push(itemCopy);
                    }
                  }
                }
                singIdSS = itemCopy.sequenceNbr;
                singIdSD = item.sequenceNbr;
              }else{
                singIdSS = item.ysshSingleId;
                singIdSD = item.sequenceNbr;
              }
          }
          let sssignADD = PricingFileFindUtils.getSingleProject(constructObj.ysshConstructId,singIdSS);
          let sdsignADD = PricingFileFindUtils.getSingleProject(constructObj.sequenceNbr,singIdSD);
          //复制的对象
        /*  let unitObjectList = await PricingFileFindUtils.getUnitList(constructObj.ysshConstructId);
          let UnitObject = unitObjectList.find(item => item.sequenceNbr === oldysshUnitId);*/
          let copyUnit = await ConvertUtil.deepCopy(newUnit);
          copyUnit.sequenceNbr = Snowflake.nextId();
          copyUnit.spId = sssignADD.sequenceNbr;
          copyUnit.upName = unitParam.name;
          copyUnit.special = 1;
          //重新赋值单位名称（有重复的话+1）
          await this.repeatInitUnitName(constructObj.ysshConstructId, sssignADD.sequenceNbr, copyUnit);
          //重新赋值分部分项等数据的unitId和spId
          await this.repeatInitUnitItemId(copyUnit);
          //重新复制人材机ID和人材机子集ID
          await this.repeatRcj(copyUnit.constructProjectRcjs,copyUnit.rcjDetailList)
          if(ObjectUtils.isEmpty(sssignADD.unitProjects)){
            sssignADD.unitProjects  = new Array();
            sssignADD.unitProjects.push(copyUnit);
          }else{
            sssignADD.unitProjects.push(copyUnit);
          }
          //送审添加完成要复制一份到审定
          let copyUnitObject = await ConvertUtil.deepCopy(copyUnit);
          copyUnitObject.ysshUnitId = copyUnit.sequenceNbr;
          copyUnitObject.ysshSingleId = sssignADD.sequenceNbr;
          copyUnitObject.ysshConstructId = constructObj.ysshConstructId;
          copyUnitObject.sequenceNbr = Snowflake.nextId();
          copyUnitObject.special = 1;
          copyUnitObject.spId = sdsignADD.sequenceNbr;
          ObjectUtils.updatePropertyValue(copyUnitObject, 'constructId', constructObj.sequenceNbr);
          ObjectUtils.updatePropertyValue(copyUnitObject, 'unitId', copyUnitObject.sequenceNbr);
          if(ObjectUtils.isEmpty(sdsignADD.unitProjects)){
            sdsignADD.unitProjects  = new Array();
            sdsignADD.unitProjects.push(copyUnitObject);
          }else{
            sdsignADD.unitProjects.push(copyUnitObject);
          }
          this.addSSTOSD(copyUnitObject, constructObj.sequenceNbr);
        }
      }
    }
  }

   getParentSingleByChildId(constructId,singleProjectId,list){
      let singObject =  PricingFileFindUtils.getSingleProject(constructId,singleProjectId);
      list.push(singObject);
      if(singObject.parentId !== constructId) {
        this.getParentSingleByChildId(constructId, singObject.parentId, list);
      }
  }



  async repeatInitSingleItemId(newSingle) {
    if (ObjectUtils.isNotEmpty(newSingle.unitProjects)) {
      //单项下有单位
      for (let i = 0; i < newSingle.unitProjects.length; i++) {
        let item = newSingle.unitProjects[i];
        item.sequenceNbr = Snowflake.nextId();
        item.spId = newSingle.sequenceNbr;
        //重新赋值分部分项等数据的unitId
        await this.repeatInitUnitItemId(item);
      }
    } else if (ObjectUtils.isNotEmpty(newSingle.subSingleProjects)) {
      //单项下有子单项
      for (let i = 0; i < newSingle.subSingleProjects.length; i++) {
        let item = newSingle.subSingleProjects[i];
        item.sequenceNbr = Snowflake.nextId();
        item.spId = newSingle.sequenceNbr;
        await this.repeatInitSingleItemId(item);
      }
    }
  }


  //单位和单项本层级有重复名称时重新计算名称
  async repeatInitSingleNameCal(singleUnitNameList, oldName) {
    let newName = oldName;
    if (singleUnitNameList.includes(newName)) {
      newName = newName + '_1';
      while (singleUnitNameList.includes(newName)) {
        let lastIndex = newName.lastIndexOf('_');
        let count = newName.slice(lastIndex + 1);
        // const regex = "^[1-9]*$";
        let number = parseInt(count);
        number = number + 1;
        newName = newName.slice(0, -1) + number;
      }
    }
    return newName;
  }


  async repeatInitUnitName(constructId, singleId, newUnit) {
    let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
    let unitProjects = singleProject.unitProjects;
    if (ObjectUtils.isNotEmpty(unitProjects)) {
      let singleUnitNameList = unitProjects.map(obj => obj.upName);
      let unitNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newUnit.upName);
      newUnit.upName = unitNewName;
    }
  }

  async repeatInitUnitItemId(newUnit) {
    //重刷单位id
    ObjectUtils.updatePropertyValue(newUnit, 'unitId', newUnit.sequenceNbr);
    //重刷单项id
    ObjectUtils.updatePropertyValue(newUnit, 'spId', newUnit.spId);

   }



  /**
   * 复制后替换人材机ID
   */
  async repeatRcj(constructProjectRcjs,rcjDetailList) {
    const oldIdToChildrenMap = new Map();
    // 1. 建立子级映射（旧父ID -> 子项数组）
    if (rcjDetailList) {
      for (const child of rcjDetailList) {
        if (!oldIdToChildrenMap.has(child.rcjId)) {
          oldIdToChildrenMap.set(child.rcjId, []);
        }
        oldIdToChildrenMap.get(child.rcjId).push(child);
      }
    }

    // 2. 处理父级并生成新旧ID映射
    if (constructProjectRcjs) {
      for (const parent of constructProjectRcjs) {
        const newId = Snowflake.nextId();

        parent.sequenceNbr = newId

        // 3. 处理对应子级（直接修改原对象）
        const children = oldIdToChildrenMap.get(parent.sequenceNbr) || [];
        for (const child of children) {
          // 生成新子级ID
          const newChildId = Snowflake.nextId();
          child.sequenceNbr = newChildId;
          child.rcjId = newId;
        }
      }
    }

  }


  async repeatInitSingleName(ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleId) {
    if (ifParentIsConstruct) {
      //单项
      let singleProjects = parent.singleProjects;
      if (ObjectUtils.isNotEmpty(singleProjects)) {
        let singleUnitNameList = singleProjects.map(obj => obj.projectName);
        let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.projectName);
        newSingle.projectName = singleNewName;
      }
    } else {
      //子单项
      let newVar = oldConstructFlatMap.get(singleId);
      let subSingleProjects = newVar.subSingleProjects;
      if (ObjectUtils.isNotEmpty(subSingleProjects)) {
        let singleUnitNameList = subSingleProjects.map(obj => obj.projectName);
        let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.projectName);
        newSingle.projectName = singleNewName;
      }
    }
  }


  async delUnitOrSign(arg){
    let list = JSON.parse(arg.list);
      for(let idStr of  list){
        if(idStr.levelType ===2 ){
          if(ObjectUtils.isNotEmpty(idStr.constructId) && ObjectUtils.isNotEmpty(idStr.singleId)){
            let argsd = {
              constructId : idStr.constructId,
              singleId : idStr.singleId
            }
            this.service.yuSuanProject.singleProjectService.delSingleProject(argsd);
          }
          if(ObjectUtils.isNotEmpty(idStr.ssConstructId) && ObjectUtils.isNotEmpty(idStr.ssSingleId)){
            let argss = {
              constructId : idStr.ssConstructId,
              singleId : idStr.ssSingleId
            }
            this.service.yuSuanProject.singleProjectService.delSingleProject(argss);
          }
        }
        if(idStr.levelType ===3 ){
          if(ObjectUtils.isNotEmpty(idStr.constructId) && ObjectUtils.isNotEmpty(idStr.singleId) && ObjectUtils.isNotEmpty(idStr.unitId)){
            let argsd = {
              constructId : idStr.constructId,
              singleId : idStr.singleId,
              unitId : idStr.unitId
            }
            this.service.yuSuanProject.singleProjectService.delUnitProject(argsd)
          }
          if(ObjectUtils.isNotEmpty(idStr.ssConstructId) && ObjectUtils.isNotEmpty(idStr.ssSingleId) && ObjectUtils.isNotEmpty(idStr.ssUnitId)){
            let argss = {
              constructId : idStr.ssConstructId,
              singleId : idStr.ssSingleId,
              unitId : idStr.ssUnitId
            }
            this.service.yuSuanProject.singleProjectService.delUnitProject(argss);
          }
        }
      }
  }



}

ShenheProjectService.toString = () => '[class ShenheProjectService]';
module.exports = ShenheProjectService;
