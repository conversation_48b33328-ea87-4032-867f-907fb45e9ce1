<!--
 * @Descripttion:
 * @Author:
 * @Date: 2024-12-30 16:35:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-25 10:42:55
-->
<template>
  <template v-for="option of props.options">
    <div class="list" v-if="option.type === 'checkbox'">
      <a-checkbox
        v-model:checked="option.value"
        :disabled="option.disabled"
        @change="changeCheck(option)"
        >{{ option.name }}</a-checkbox
      >
    </div>
    <div class="list" v-if="option.type === 'radio'">
      <span v-if="option.name" class="name">{{ option.name }}：</span>
      <a-radio-group v-model:value="option.value" :disabled="option.disabled">
        <a-radio :value="item.value" v-for="item of option.options">{{ item.label }}</a-radio>
      </a-radio-group>
    </div>
    <div class="list" v-if="option.type === 'input'">
      <span v-if="option.name" class="name">{{ option.name }}：</span>
      <a-input
        style="width: auto"
        v-model:value="option.value"
        :disabled="option.disabled"
        @blur="emit('onChange', $event, option.field)"
      ></a-input>
    </div>
    <div class="list" v-if="option.type === 'filePath'">
      <span v-if="option.name" class="name">{{ option.name }}：</span>
      <a-button :disabled="option.disabled" @click="setData(option)">更改路径</a-button>
      <span style="margin-left: 8px">{{ option.value }}</span>
    </div>
    <div class="list" v-if="option.type === 'table'">
      <a-table
        :pagination="false"
        size="small"
        bordered
        :dataSource="option.value"
        :scroll="{ y: stableHeight }"
        :columns="option.columns"
      >
        <template #bodyCell="{ text, record: row, index, column, key, openEditor, closeEditor }">
          <!-- 精度下拉 -->
          <template v-if="column.dataIndex == 'value'">
            <a-select
              style="width: 100%"
              :value="row.value"
              :options="column.selectList"
              :bordered="false"
              size="small"
              transfer
              :disabled="option.disabled"
              @update:value="
                v => {
                  row.value = v;
                  row.label = row.value === 0 ? '整数' : `小数点后${row.value}位`;
                  // setData()
                }
              "
            >
            </a-select>
          </template>
        </template>
      </a-table>
    </div>
  </template>
</template>

<script setup>
import csProject from '@/api/csProject';
import { onBeforeUnmount, onMounted, ref, defineEmits } from 'vue';
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
});
let stableHeight = ref(340); //表格设置默认固定表头-需要获取滚动高度
const getTableHeight = () => {
  // console.log('getTableHeight', props.options);
  let tableEl = document.querySelector('.content-box');
  stableHeight.value = tableEl.clientHeight - 120;
};
const changeCheck = option => {
  if (['qdgclJingDu', 'degclJingDu'].includes(option.field)) {
    // props.options[0].value = option.value;
    props.options[1].disabled = !props.options[0].value;
  }
};
onMounted(() => {
  window.addEventListener('resize', getTableHeight);
  getTableHeight();
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', getTableHeight);
});
const setData = params => {
  console.log('🚀 ~ setData ~ params:', params);
  csProject.selectPath({ paramsTag: params.field, defaultPath: params.value }).then(res => {
    console.log('🚀 ~ csProject.setSetUp ~ res:', res);
    if (res.result) {
      params.value = res.result;
    }
  });
};
const emit = defineEmits(['onChange']);
</script>
<style lang="scss" scoped>
.list {
  font-size: 14px;
  padding: 2px 0;
  color: #2a2a2a;
  .name {
    white-space: nowrap;
  }
  :deep(.ant-radio-group) {
    padding: 2px 0;
    // 重写radio样式为checkbox样式
    // .ant-radio-checked .ant-radio-inner {
    //   background-color: #1890ff;
    // }
    // .ant-radio-inner {
    //   border-radius: 2px;
    //   &::after {
    //     position: absolute;
    //     top: 50%;
    //     left: 21.5%;
    //     width: 5.71428571px;
    //     height: 9.14285714px;
    //     margin-top: 0;
    //     margin-left: 0;
    //     background-color: transparent;
    //     border-radius: 0;
    //     display: table;
    //     border: 2px solid #fff;
    //     border-top: 0;
    //     border-left: 0;
    //     transform: rotate(45deg) scale(1) translate(-50%, -50%);
    //     opacity: 1;
    //     transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    //     content: ' ';
    //   }
    // }
  }
  :deep(.ant-radio-disabled + span) {
    color: rgba(0, 0, 0, 0.85);
  }
  :deep(.ant-checkbox-disabled + span) {
    color: rgba(0, 0, 0, 0.85);
  }
}
</style>
