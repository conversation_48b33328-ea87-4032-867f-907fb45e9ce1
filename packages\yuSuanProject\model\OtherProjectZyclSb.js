"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectZyclSb = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectZyclSb extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, sortNo, name, specification, unit, quantitativeExpression, amount, price, total, unitId, spId, constructId) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.sortNo = sortNo;
        this.name = name;
        this.specification = specification;
        this.unit = unit;
        this.quantitativeExpression = quantitativeExpression;
        this.amount = amount;
        this.price = price;
        this.total = total;
        this.description = description;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
    }
}
exports.OtherProjectZyclSb = OtherProjectZyclSb;
//# sourceMappingURL=OtherProjectZyclSb.js.map