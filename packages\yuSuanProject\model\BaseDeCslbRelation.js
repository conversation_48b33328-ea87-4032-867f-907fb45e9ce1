"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeCslbRelation2022 = exports.BaseDeCslbRelation = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 定额表
 */
let BaseDeCslbRelation = class BaseDeCslbRelation extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "rate_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation.prototype, "rateName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rate_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation.prototype, "rateCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_main", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation.prototype, "isMain", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sgzj_class_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation.prototype, "sgzjClassName", void 0);
BaseDeCslbRelation = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_cslb_relation" })
], BaseDeCslbRelation);
exports.BaseDeCslbRelation = BaseDeCslbRelation;
let BaseDeCslbRelation2022 = class BaseDeCslbRelation2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "rate_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation2022.prototype, "rateName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rate_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation2022.prototype, "rateCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_main", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation2022.prototype, "isMain", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sgzj_class_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeCslbRelation2022.prototype, "sgzjClassName", void 0);
BaseDeCslbRelation2022 = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_cslb_relation_2022" })
], BaseDeCslbRelation2022);
exports.BaseDeCslbRelation2022 = BaseDeCslbRelation2022;
//# sourceMappingURL=BaseDeCslbRelation.js.map