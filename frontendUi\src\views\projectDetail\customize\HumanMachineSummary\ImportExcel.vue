<!--
 * @Descripttion: 导入excel
 * @Author: renmingming
 * @Date: 2023-05-22 15:36:24
 * @LastEditors: wangru
 * @LastEditTime: 2024-12-23 15:21:23
-->
<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="auto"
    @close="dialogVisible = false"
    v-model:modelValue="dialogVisible"
    title="导入excel"
  >
    <a-spin :spinning="spinning">
      <div class="file-wrap">
        <a-upload-dragger
          class="file-uploadFile-important"
          name="file"
          :multiple="false"
          accept=".xls,.xlsx"
          :max-count="1"
          v-model:file-list="fileList"
          :before-upload="beforeUpload"
        >
          <img
            src="~@/assets/img/importExcel.png"
            alt=""
            class="file-img"
            style="margin: 20px 0"
          />
          <p style="font-size: 12px; margin-top: 5px">
            将excel文件拖拽到此处或<span style="color: #287cfa; text-decoration: underline">点击上传</span>
          </p>
          <template #itemRender="{ file, actions }">
            <!-- <a-space> -->
            <p style="margin: 0">
              <icon-font
                class="icon"
                type="icon-daochuExcel"
              />
              <span style="margin: 10px">{{ file.name }}</span>
              <icon-font
                class="icon"
                type="icon-shanchu2"
                @click="actions.remove"
                style="margin: 8px 15px 0 0; float: right"
              />
            </p>

            <!-- </a-space> -->
          </template>
        </a-upload-dragger>

        <div class="footer-btn">
          <a-button
            type="primary"
            :loading="spinning || loading"
            @click="onSubmit"
          >确定</a-button>
        </div>
      </div>
    </a-spin>
  </common-modal>
  <common-modal
    className="dialog-comm tree-dialog"
    width="500"
    @close="nextLevel"
    v-model:modelValue="inspecModal"
    title="提示"
  >
    <div>
      <p v-if="analysisStatue">
        <icon-font
          class="icon"
          type="icon-ruotixing"
        />
        导入成功<span v-if="successNum">本次导入，修改了{{ successNum }}条人材机数据</span>
      </p>
      <p v-else>
        <icon-font
          class="icon"
          type="icon-querenshanchu"
        />
        导入解析失败
        <span
          class="download"
          @click="downloadExcel"
        >下载标准excel模板</span>
      </p>
      <a-button
        type="primary"
        :loading="spinning || loading"
        @click="nextLevel"
        style="float: right; margin: 10px 0px 0 0"
      >确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import projectDetailApi from '@/api/projectDetail.js';
import { message } from 'ant-design-vue';
import {
  getCurrentInstance,
  ref,
  reactive,
  watch,
  nextTick,
  onMounted,
  toRaw,
  toRefs,
} from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
const form = ref();
const loading = ref(false);
const spinning = ref(false);
const emits = defineEmits(['closeImportExcel', 'updateImportData']);
const dialogVisible = ref(false);

let fileList = ref([]);

// 上传前置判断
const beforeUpload = file => {
  let tar = file.path.split('.');
  file.fileType = tar[tar.length - 1];
  if (!['xls', 'xlsx'].includes(file.fileType)) {
    message.error('上传文件格式不正确!');
    return false;
  }
  if (fileList.value.length > 0) {
    message.warning('只能上传一个文件!');
    return false;
  }
  return true;
};
watch(
  () => fileList.value,
  (newVal, oldVal) => {
    if (fileList.value.length > 0 && newVal.length > oldVal.length) {
      console.log('fileList.value', fileList.value, newVal, oldVal);
      fileList.value = fileList.value.filter(a =>
        ['xls', 'xlsx'].includes(a.originFileObj.fileType)
      );
    }
  }
);
let inspectionData = ref([]); //上传单位文件列表校验结果
let inspecModal = ref(false); //上传单位文件列表校验弹框
let analysisStatue = ref(false); // 解析是否成功
let successNum = ref(0); // 成功数量
const onSubmit = () => {
  if (spinning.value || loading.value) {
    message.error('正在发送中...');
    return;
  }
  if (fileList.value.length === 0) {
    message.warning('请上传文件');
    return;
  }
  console.log('fileList', fileList.value);
  spinning.value = true;
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    path: fileList.value[0].originFileObj.path,
  };
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  let apiFunName =
    store.currentTreeInfo.levelType === 3
      ? 'useUnitExcelRcjPrice'
      : store.currentTreeInfo.levelType === 1
      ? 'useConstructExcelRcjPrice'
      : 'useSingleExcelRcjPrice';
  console.log('报表导入', apiData, apiFunName);
  projectDetailApi[apiFunName](apiData)
    .then(({ result }) => {
      console.log('result', result);
      inspecModal.value = true;
      if (store.currentTreeInfo.levelType === 3) {
        if (result === null) {
          analysisStatue.value = false;
          return;
        }
        if (result >= 0) {
          analysisStatue.value = true;
          successNum.value = result;
        }
      } else {
        if (!result) {
          analysisStatue.value = false;
          return;
        }
        const { updateRcj, size } = result;
        emits('updateImportData', updateRcj);
        analysisStatue.value = true;
        successNum.value = size;
      }
    })
    .finally(() => {
      spinning.value = false;
      dialogVisible.value = false;
    });
};
const nextLevel = () => {
  inspecModal.value = false;
  if (store.currentTreeInfo.levelType === 3) {
    emits('closeImportExcel');
  }
};
const downloadExcel = () => {
  projectDetailApi.downloadExcelRcjTotal({
    deStandardReleaseYear: store.deStandardReleaseYear,
  });
};
const open = () => {
  console.log('进入导入项目');
  dialogVisible.value = true;
  fileList.value = [];
};
defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.file-wrap {
  width: 50vw;
  min-width: 500px;
  max-width: 600px;
  .ft-tips {
    display: flex;
    align-items: center;
  }
}
.file-upload {
  background: #fff6f6;
  border: 1px dashed rgba(151, 151, 151, 0.37);
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  cursor: pointer;
  &:hover {
    background: rgba(251, 228, 228, 0.5);
  }

  .upload-btn {
    display: flex;
    align-items: center;
    padding: 6px 18px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
    background: #dc3838;
    border-radius: 22px;
    margin-left: 6px;
    span {
      margin: 0 4px;
    }
  }
}

.disabled {
  cursor: no-drop;
  opacity: 0.4;
}

.form-wrap {
  margin-top: 44px;
}

.file-success {
  background-color: rgb(246, 246, 246);
  border: 1px dashed rgba(151, 151, 151, 0.37);
  flex-direction: row;
  align-items: center;
  .file-box {
    width: 45%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 10px;
    justify-content: center;
    .type-img {
      width: 38px;
      margin: 0 auto 10px;
    }
    .file-name {
      font-size: 14px;
      font-weight: 400;
      display: block;
      width: 100%;
      color: #2a2a2a;
      overflow: hidden;
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
    }
  }

  .ft-tips {
    padding: 0 10px;
    display: flex;
    align-items: center;
    flex-direction: column;
    border-left: 1px solid rgba(151, 151, 151, 0.37);
    .upload-file {
      font-size: 12px;
      font-weight: 400;
      color: #454545;
      margin-bottom: 20px;
      .reset-file {
        font-size: 12px;
        font-weight: 600;
        color: #dc3838;
        margin-left: 5px;
        text-decoration: dashed;
      }
    }
    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #2a2a2a;
    }
  }
}
.footer-btn {
  margin: 10px auto 0;
  display: flex;
  justify-content: center;
}
::v-deep .ant-upload-list-text-container {
  background: #f5f5f5;
  margin-top: 5px;
  font-size: 14px;
  line-height: 30px;
}
::v-deep .ant-upload-list {
  max-height: 200px;
  overflow: auto;
}
.download {
  cursor: pointer;
  color: #2867c7;
}
</style>
