"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseModel = void 0;
const typeorm_1 = require("typeorm");
let BaseModel = class BaseModel {
    // public levelType :string ; //工程级别类型： 工程项目  单项   单位
    // public bizId : string; // 工程项目树所选择的id：工程项目id/单项id/单位id
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description) {
        this.sequenceNbr = sequenceNbr;
        this.recUserCode = recUserCode;
        this.recStatus = recStatus;
        this.recDate = recDate;
        this.extend1 = extend1;
        this.extend2 = extend2;
        this.extend3 = extend3;
        this.description = description;
    }
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], BaseModel.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "rec_user_code" }),
    __metadata("design:type", String)
], BaseModel.prototype, "recUserCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true,
        name: "rec_status",
        default: "A"
    }),
    __metadata("design:type", String)
], BaseModel.prototype, "recStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "rec_date" }),
    __metadata("design:type", String)
], BaseModel.prototype, "recDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseModel.prototype, "extend1", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseModel.prototype, "extend2", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseModel.prototype, "extend3", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseModel.prototype, "description", void 0);
BaseModel = __decorate([
    (0, typeorm_1.Entity)(),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, String])
], BaseModel);
exports.BaseModel = BaseModel;
//# sourceMappingURL=BaseModel.js.map