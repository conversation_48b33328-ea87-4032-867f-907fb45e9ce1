"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeLibrary = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 定额册
 */
let BaseDeLibrary = class BaseDeLibrary extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_major", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "libraryMajor", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "sort_no", nullable: true }),
    __metadata("design:type", Number)
], BaseDeLibrary.prototype, "sortNo", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "sort", nullable: true }),
    __metadata("design:type", Number)
], BaseDeLibrary.prototype, "sort", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "release_date", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "releaseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "ss_province", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "ssProvince", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "ss_city", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "ssCity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_standard_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "deStandardId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "remark", nullable: true }),
    __metadata("design:type", String)
], BaseDeLibrary.prototype, "remark", void 0);
BaseDeLibrary = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_library" })
], BaseDeLibrary);
exports.BaseDeLibrary = BaseDeLibrary;
//# sourceMappingURL=BaseDeLibrary.js.map