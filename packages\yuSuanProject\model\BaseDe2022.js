"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDe2022 = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
/**
 * 定额表
 */
let BaseDe2022 = class BaseDe2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_name", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "qf_code", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "rateCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "project_type", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "projectType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_identify", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "deIdentify", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_zj", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "isZj", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "price", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "total", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "total", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "rgf", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "rgf", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "clf", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "clf", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "jxf", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "jxf", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "glf", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "glf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quantity", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "key_words1", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "keyWords1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "key_words2", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "keyWords2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "key_words3", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "keyWords3", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "max_value", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "maxValue", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "min_value", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "minValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "num_unit", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "numUnit", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "max_value2", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "maxValue2", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "min_value2", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "minValue2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "num_unit2", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "numUnit2", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "max_value3", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "maxValue3", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "min_value3", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "minValue3", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "num_unit3", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "numUnit3", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "classify_level7", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classifyLevel7", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "classify_level6", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classifyLevel6", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "classify_level5", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classifyLevel5", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "classify_level4", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classifyLevel4", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "classify_level3", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classifyLevel3", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "classify_level2", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classifyLevel2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "classify_level1", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classifyLevel1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "remark", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "status", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rate_name", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "rateName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sort_no", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "sortNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zjcs_class_code", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "zjcsClassCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zjcs_class_name", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "zjcsClassName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sgzzcslb", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "sgzzcslb", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "value", nullable: true }),
    __metadata("design:type", Number)
], BaseDe2022.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "charge_name", nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "chargeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'classlevel_split_concat', nullable: true }),
    __metadata("design:type", String)
], BaseDe2022.prototype, "classlevelSplitConcat", void 0);
BaseDe2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_2022" })
], BaseDe2022);
exports.BaseDe2022 = BaseDe2022;
//# sourceMappingURL=BaseDe2022.js.map