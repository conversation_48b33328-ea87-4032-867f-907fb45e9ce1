"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeRcjRelation2022 = exports.BaseDeRcjRelation = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * base 定额人材机关联表
 */
let BaseDeRcjRelation = class BaseDeRcjRelation extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "quota_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "quotaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "res_qty", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "resQty", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "materialCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "materialName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "kind", nullable: true }),
    __metadata("design:type", Number)
], BaseDeRcjRelation.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rcj_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation.prototype, "rcjId", void 0);
BaseDeRcjRelation = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_rcj_relation" })
], BaseDeRcjRelation);
exports.BaseDeRcjRelation = BaseDeRcjRelation;
let BaseDeRcjRelation2022 = class BaseDeRcjRelation2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "quota_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "quotaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "res_qty", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "resQty", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "materialCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "materialName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "kind", nullable: true }),
    __metadata("design:type", Number)
], BaseDeRcjRelation2022.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rcj_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjRelation2022.prototype, "rcjId", void 0);
BaseDeRcjRelation2022 = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_rcj_relation_2022" })
], BaseDeRcjRelation2022);
exports.BaseDeRcjRelation2022 = BaseDeRcjRelation2022;
//# sourceMappingURL=BaseDeRcjRelation.js.map