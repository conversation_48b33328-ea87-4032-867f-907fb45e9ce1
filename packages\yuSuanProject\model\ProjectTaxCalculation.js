"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectTaxCalculation = void 0;
const BaseModel_1 = require("./BaseModel");
class ProjectTaxCalculation extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, constructId, unitId, taxCalculationMethod, taxReformDocumentsId, taxPayingRegion, additionalTaxRate, outputTaxRate, simpleRate, taxRate) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.constructId = constructId;
        this.unitId = unitId;
        this.taxCalculationMethod = taxCalculationMethod;
        this.taxReformDocumentsId = taxReformDocumentsId;
        this.taxPayingRegion = taxPayingRegion;
        this.additionalTaxRate = additionalTaxRate;
        this.outputTaxRate = outputTaxRate;
        this.simpleRate = simpleRate;
        this.taxRate = taxRate;
    }
}
exports.ProjectTaxCalculation = ProjectTaxCalculation;
//# sourceMappingURL=ProjectTaxCalculation.js.map