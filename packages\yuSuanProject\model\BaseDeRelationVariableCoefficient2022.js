"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeRelationVariableCoefficient2022 = void 0;
const typeorm_1 = require("typeorm");
/**
 * 22定额父级定额关联表
 */
let BaseDeRelationVariableCoefficient2022 = class BaseDeRelationVariableCoefficient2022 {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "id" }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "groupid", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "groupid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "variable_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "variableName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "variable_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "variableCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "formula", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "formula", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "default", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "default", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "value", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_effect_range", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "deEffectRange", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "if_editable", nullable: true }),
    __metadata("design:type", Number)
], BaseDeRelationVariableCoefficient2022.prototype, "ifEditable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "remark", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableCoefficient2022.prototype, "remark", void 0);
BaseDeRelationVariableCoefficient2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_relation_variable_coefficient_2022" })
], BaseDeRelationVariableCoefficient2022);
exports.BaseDeRelationVariableCoefficient2022 = BaseDeRelationVariableCoefficient2022;
//# sourceMappingURL=BaseDeRelationVariableCoefficient2022.js.map