"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstructProject = void 0;
const BaseModel_1 = require("./BaseModel");
/**
 * 工程项目
 */
class ConstructProject extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, rcjKindSort, loadPriceCache, rcjWjcState, recStatus, recDate, extend1, extend2, extend3, description, ssProvince, ssProvinceName, ssCity, ssCityName, constructName, constructCode, projectStatus, fileCode, path, createDate, oflateOpenDate, biddingType, coverUrl, importUrl, reportUrl, gfId, awfId, rgfId, qdStandardId, deStandardId, deStandardReleaseYear, projectOverview, fddbr, constructionUnit, singleProjects, unitProject, bzUnitProject, unitProjectArray, constructProjectRcjs, constructProjectJBXX, organizationInstructions, mainSetting, projectTaxCalculation, xmlFactory, projectRcjsLoading, constructProjectRcjAvgRule, projectAttrRelateMergeScheme, rgfInMeasureAndRPriceInMechanicalAction, deGlTcFlag, securityFee, feeCalculateBaseList, mainRcjShowFlag, standardConversionShowFlag, exportConfig, optionLock) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        /**
         * true（锁定快速创建）
         */
        this.optionLock = true;
        this.ssProvince = ssProvince;
        this.ssProvinceName = ssProvinceName;
        this.ssCity = ssCity;
        this.ssCityName = ssCityName;
        this.constructName = constructName;
        this.constructCode = constructCode;
        this.projectStatus = projectStatus;
        this.fileCode = fileCode;
        this.path = path;
        this.createDate = createDate;
        this.oflateOpenDate = oflateOpenDate;
        this.biddingType = biddingType;
        this.coverUrl = coverUrl;
        this.importUrl = importUrl;
        this.reportUrl = reportUrl;
        this.gfId = gfId;
        this.awfId = awfId;
        this.rgfId = rgfId;
        this.qdStandardId = qdStandardId;
        this.deStandardId = deStandardId;
        this.deStandardReleaseYear = deStandardReleaseYear;
        this.projectOverview = projectOverview;
        this.fddbr = fddbr;
        this.constructionUnit = constructionUnit;
        this.singleProjects = singleProjects;
        this.unitProject = unitProject;
        this.bzUnitProject = bzUnitProject;
        this.unitProjectArray = unitProjectArray;
        this.constructProjectRcjs = constructProjectRcjs;
        this.constructProjectJBXX = constructProjectJBXX;
        this.organizationInstructions = organizationInstructions;
        this.mainSetting = mainSetting;
        this.projectTaxCalculation = projectTaxCalculation;
        this.xmlFactory = xmlFactory;
        this.projectRcjsLoading = projectRcjsLoading;
        this.constructProjectRcjAvgRule = constructProjectRcjAvgRule;
        this.projectAttrRelateMergeScheme = projectAttrRelateMergeScheme;
        this.rgfInMeasureAndRPriceInMechanicalAction = rgfInMeasureAndRPriceInMechanicalAction;
        this.deGlTcFlag = deGlTcFlag;
        this.securityFee = securityFee;
        this.feeCalculateBaseList = feeCalculateBaseList;
        this.mainRcjShowFlag = mainRcjShowFlag;
        this.standardConversionShowFlag = standardConversionShowFlag;
        this.exportConfig = exportConfig;
        this.optionLock = optionLock;
        this.rcjKindSort = rcjKindSort;
        this.loadPriceCache = loadPriceCache;
        this.rcjWjcState = rcjWjcState;
    }
}
exports.ConstructProject = ConstructProject;
//# sourceMappingURL=ConstructProject.js.map