"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitProject = void 0;
const BaseModel_1 = require("./BaseModel");
class UnitProject extends BaseModel_1.BaseModel {
    constructor(lockPriceFlag, sequenceNbr, fbArrangeList, unitRcjKindSort, rcjWjcState, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, upCode, upName, uptotal, average, fbfxhj, fbfxrgf, fbfxclf, fbfxjxf, fbfxlr, fbfxglf, csxhj, csxrgf, csxclf, csxjxf, csxglf, csxlr, djcsxhj, djcsxrgf, djcsxclf, djcsxjxf, djcsxglf, djcsxlr, zjcsxhj, zjcsxrgf, zjcsxclf, zjcsxjxf, zjcsxglf, zjcsxlr, qtxmhj, qtxmrgf, qtxmclf, qtxmjxf, qtxmglf, gfee, safeFee, safeFl, sbf, sqgczj, jxse, xxse, zzsynse, fjse, sj, xxsefl, fjsffl, fyZb, constructMajorType, spId, status, createDate, updateDate, delFlag, tenantId, sortNo, constructId, biddingType, importUrl, reportStorageUrls, reportUrl, mainDeLibrary, secondInstallationProjectName, rgfId, fbfxzcf, fbfxzgj, djcsxzcf, zjcsxzcf, qtxmzlje, qtxmzygczgj, qtxmzcbfwf, qtxmjrg, unitcost, sbfsj, sbfsjjg, gczj, gczjsbsj, gczjsbsjjg, feeBuild, feeFiles, itemBillProjects, measureProjectTables, djMeasureProjectTableArray, zjMeasureProjectTableArray, awfMeasureProjectTableArray, otherProjects, otherProjectDayWorks, otherProjectProvisionals, otherProjectServiceCosts, otherProjectClZgjs, otherProjectSbZgjs, otherProjectZygcZgjs, otherProjectZyclSbs, otherProjectJgclSbs, otherProjectQzAndSuoPeis, gfees, safeFees, constructProjectRcjs, rcjDetailList, unitJBXX, unitGCTZ, organizationInstructions, unitCostCodePrices, unitCostSummarys, inputTaxDetails, projectTaxCalculation, fixationSecurityFee, cgCostMathCache, cyCostMathCache, azCostMathCache, conversionRuleOperationRecordList, listFeatureList, unitRcjsLoading, feeCalculateBaseList, mainMaterialSetting, rcjClassificationTableList, conversionRuleSeqNbrSortIndex, lastMaterialReplaceInfo, screenCondition, defaultConcersions, colorList, checkColorList, rcjCache) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.upCode = upCode;
        this.upName = upName;
        this.uptotal = uptotal;
        this.average = average;
        this.fbfxhj = fbfxhj;
        this.fbfxrgf = fbfxrgf;
        this.fbfxclf = fbfxclf;
        this.fbfxjxf = fbfxjxf;
        this.fbfxlr = fbfxlr;
        this.fbfxglf = fbfxglf;
        this.csxhj = csxhj;
        this.csxrgf = csxrgf;
        this.csxclf = csxclf;
        this.csxjxf = csxjxf;
        this.csxglf = csxglf;
        this.csxlr = csxlr;
        this.djcsxhj = djcsxhj;
        this.djcsxrgf = djcsxrgf;
        this.djcsxclf = djcsxclf;
        this.djcsxjxf = djcsxjxf;
        this.djcsxglf = djcsxglf;
        this.djcsxlr = djcsxlr;
        this.zjcsxhj = zjcsxhj;
        this.zjcsxrgf = zjcsxrgf;
        this.zjcsxclf = zjcsxclf;
        this.zjcsxjxf = zjcsxjxf;
        this.zjcsxglf = zjcsxglf;
        this.zjcsxlr = zjcsxlr;
        this.qtxmhj = qtxmhj;
        this.qtxmrgf = qtxmrgf;
        this.qtxmclf = qtxmclf;
        this.qtxmjxf = qtxmjxf;
        this.qtxmglf = qtxmglf;
        this.gfee = gfee;
        this.safeFee = safeFee;
        this.safeFl = safeFl;
        this.sbf = sbf;
        this.sqgczj = sqgczj;
        this.jxse = jxse;
        this.xxse = xxse;
        this.zzsynse = zzsynse;
        this.fjse = fjse;
        this.sj = sj;
        this.xxsefl = xxsefl;
        this.fjsffl = fjsffl;
        this.fyZb = fyZb;
        this.constructMajorType = constructMajorType;
        this.spId = spId;
        this.status = status;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.delFlag = delFlag;
        this.tenantId = tenantId;
        this.sortNo = sortNo;
        this.constructId = constructId;
        this.biddingType = biddingType;
        this.importUrl = importUrl;
        this.reportStorageUrls = reportStorageUrls;
        this.reportUrl = reportUrl;
        this.mainDeLibrary = mainDeLibrary;
        this.secondInstallationProjectName = secondInstallationProjectName;
        this.rgfId = rgfId;
        this.fbfxzcf = fbfxzcf;
        this.fbfxzgj = fbfxzgj;
        this.djcsxzcf = djcsxzcf;
        this.zjcsxzcf = zjcsxzcf;
        this.qtxmzlje = qtxmzlje;
        this.qtxmzygczgj = qtxmzygczgj;
        this.qtxmzcbfwf = qtxmzcbfwf;
        this.qtxmjrg = qtxmjrg;
        this.unitcost = unitcost;
        this.sbfsj = sbfsj;
        this.sbfsjjg = sbfsjjg;
        this.gczj = gczj;
        this.gczjsbsj = gczjsbsj;
        this.gczjsbsjjg = gczjsbsjjg;
        this.feeBuild = feeBuild;
        this.feeFiles = feeFiles;
        this.itemBillProjects = itemBillProjects;
        this.measureProjectTables = measureProjectTables;
        this.djMeasureProjectTableArray = djMeasureProjectTableArray;
        this.zjMeasureProjectTableArray = zjMeasureProjectTableArray;
        this.awfMeasureProjectTableArray = awfMeasureProjectTableArray;
        this.otherProjects = otherProjects;
        this.otherProjectDayWorks = otherProjectDayWorks;
        this.otherProjectProvisionals = otherProjectProvisionals;
        this.otherProjectServiceCosts = otherProjectServiceCosts;
        this.otherProjectClZgjs = otherProjectClZgjs;
        this.otherProjectSbZgjs = otherProjectSbZgjs;
        this.otherProjectZygcZgjs = otherProjectZygcZgjs;
        this.otherProjectZyclSbs = otherProjectZyclSbs;
        this.otherProjectJgclSbs = otherProjectJgclSbs;
        this.otherProjectQzAndSuoPeis = otherProjectQzAndSuoPeis;
        this.gfees = gfees;
        this.safeFees = safeFees;
        this.constructProjectRcjs = constructProjectRcjs;
        this.rcjDetailList = rcjDetailList;
        this.unitJBXX = unitJBXX;
        this.unitGCTZ = unitGCTZ;
        this.organizationInstructions = organizationInstructions;
        this.unitCostCodePrices = unitCostCodePrices;
        this.unitCostSummarys = unitCostSummarys;
        this.inputTaxDetails = inputTaxDetails;
        this.projectTaxCalculation = projectTaxCalculation;
        this.fixationSecurityFee = fixationSecurityFee;
        this.cgCostMathCache = cgCostMathCache;
        this.cyCostMathCache = cyCostMathCache;
        this.azCostMathCache = azCostMathCache;
        this.conversionRuleOperationRecordList = conversionRuleOperationRecordList;
        this.listFeatureList = listFeatureList;
        this.unitRcjsLoading = unitRcjsLoading;
        this.feeCalculateBaseList = feeCalculateBaseList;
        this.mainMaterialSetting = mainMaterialSetting;
        this.rcjClassificationTableList = rcjClassificationTableList;
        this.conversionRuleSeqNbrSortIndex = conversionRuleSeqNbrSortIndex;
        this.lastMaterialReplaceInfo = lastMaterialReplaceInfo;
        this.screenCondition = screenCondition;
        this.defaultConcersions = defaultConcersions;
        this.colorList = colorList;
        this.checkColorList = checkColorList;
        this.rcjCache = rcjCache;
        this.lockPriceFlag = lockPriceFlag;
        this.fbArrangeList = fbArrangeList;
        this.unitRcjKindSort = unitRcjKindSort;
        this.unitRcjKindSort = rcjWjcState;
    }
}
exports.UnitProject = UnitProject;
//# sourceMappingURL=UnitProject.js.map