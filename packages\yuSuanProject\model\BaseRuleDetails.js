"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRuleDetails = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 规则明细
 */
let BaseRuleDetails = class BaseRuleDetails extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_code", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "relationGroupCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "kind", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_id", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "relationGroupId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_name", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "relationGroupName", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "relation_group_cnt", nullable: true }),
    __metadata("design:type", Number)
], BaseRuleDetails.prototype, "relationGroupCnt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_rule", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "relationGroupRule", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_details_id", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "fileDetailsId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_code", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "relationCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "relation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "math", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "math", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "top_group_type", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "topGroupType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rcj_id", nullable: true }),
    __metadata("design:type", String)
], BaseRuleDetails.prototype, "rcjId", void 0);
BaseRuleDetails = __decorate([
    (0, typeorm_2.Entity)({ name: "base_rule_details" })
], BaseRuleDetails);
exports.BaseRuleDetails = BaseRuleDetails;
//# sourceMappingURL=BaseRuleDetails.js.map