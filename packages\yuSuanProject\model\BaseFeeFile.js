"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseFeeFile2022 = exports.BaseFeeFile = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 单位取费文件
 */
let BaseFeeFile = class BaseFeeFile extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_code" }),
    __metadata("design:type", String)
], BaseFeeFile.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_name" }),
    __metadata("design:type", String)
], BaseFeeFile.prototype, "qfName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "del_flag" }),
    __metadata("design:type", String)
], BaseFeeFile.prototype, "delFlag", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseFeeFile.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", Number)
], BaseFeeFile.prototype, "sortNo", void 0);
BaseFeeFile = __decorate([
    (0, typeorm_1.Entity)()
], BaseFeeFile);
exports.BaseFeeFile = BaseFeeFile;
/**
 * 单位取费文件
 */
let BaseFeeFile2022 = class BaseFeeFile2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_code" }),
    __metadata("design:type", String)
], BaseFeeFile2022.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_name" }),
    __metadata("design:type", String)
], BaseFeeFile2022.prototype, "qfName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "del_flag" }),
    __metadata("design:type", String)
], BaseFeeFile2022.prototype, "delFlag", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseFeeFile2022.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", Number)
], BaseFeeFile2022.prototype, "sortNo", void 0);
BaseFeeFile2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_fee_file_2022" })
], BaseFeeFile2022);
exports.BaseFeeFile2022 = BaseFeeFile2022;
//# sourceMappingURL=BaseFeeFile.js.map