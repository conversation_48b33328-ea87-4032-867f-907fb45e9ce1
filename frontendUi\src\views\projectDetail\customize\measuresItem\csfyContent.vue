<!--
 * @Author: wangru
 * @Date: 2023-08-04 10:39:12
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-04 15:50:54
 *自动记取总价措施
-->
<template>
  <a-spin :spinning="loadingModal" tip="数据加载中..." wrapperClassName="spin-yyy">
    <div>
      <div class="content">
        <p class="selectContent" v-if="projectStore.currentTreeInfo.libraryCode == '2022-SZGC-DEK'">
          <span>主专业：</span>
          <a-select
            v-model:value="zzy"
            style="width: 430px"
            :options="zzyList"
            :field-names="{
              label: 'value',
              value: 'value',
            }"
            placeholder="请选择"></a-select>
        </p>
        <!-- <p
          class="selectContent"
          v-if="
            classList &&
            classList.filter(x => x.zjcsClassCode === '16').length > 0
          "
        >
          <span>高台增加费高度</span>
          <a-select
            v-model:value="increaseFeeHeight"
            style="width: 230px"
            :options="increaseFeeHeightList"
            :field-names="{
              label: 'addRateHeight',
              value: 'addRateHeight',
            }"
            placeholder="请选择"
          ></a-select>
        </p> -->
        <p class="selectContent" v-if="state.csfyCalculateBaseAreaView">
          <a-radio-group v-model:value="state.csfyCalculateBaseArea" name="radioGroup">
            <a-radio value="zhangcheng">张承地区</a-radio>
            <a-radio value="other">其他地区</a-radio>
          </a-radio-group>
        </p>

        <div class="table-content">
          <div style="display: flex; justify-content: space-between">
            <vxe-table
              align="center"
              :column-config="{ resizable: true }"
              :row-config="{ isHover: true }"
              :data="classList.filter((x, index) => index > 0)"
              height="420"
              style="width: 65%">
              <vxe-column field="qdName" width="30%" title="总价措施名称"></vxe-column>
              <vxe-column field="type" width="30%" title="对应当前单位措施清单">
                <template #default="{ row, index }">
                  <vxe-select
                    v-if="row.qdName === '安全文明施工费'"
                    v-model="row.sequenceNbr"
                    transfer>
                    <vxe-option
                      v-for="(item, index) in awfQdList"
                      :key="index"
                      :value="item.sequenceNbr"
                      :label="item.qdCode + ' ' + item.qdName"></vxe-option>
                  </vxe-select>
                  <vxe-select v-else v-model="row.sequenceNbr" transfer>
                    <vxe-option
                      v-for="(item, index) in otherQdList"
                      :key="index"
                      :value="item.sequenceNbr"
                      :label="item.qdCode + ' ' + item.qdName"></vxe-option>
                  </vxe-select>
                </template>
              </vxe-column>
              <vxe-column field="isCheck" width="40%" title="是否计取">
                <template #default="{ row }">
                  <vxe-checkbox
                    v-model="row.isCheck"
                    :checked-value="1"
                    :unchecked-value="0"
                    :disabled="!!row.fixedAWFee"
                    @change="jqChange(row, $event)"></vxe-checkbox>
                </template>
              </vxe-column>
            </vxe-table>
            <div style="width: 33%; height: 400px">
              <p class="title">同专业计取</p>
              <vxe-table
                :column-config="{ resizable: true }"
                :tree-config="{
                  transform: true,
                  rowField: 'id',
                  parentField: 'parentId',
                  expandAll: true,
                  showLine: true,
                }"
                :data="treeData"
                border="none"
                :show-header="false"
                :row-config="{
                  keyField: 'id',
                }"
                :checkbox-config="{
                  checkRowKeys: checkRowKeys,
                  checkMethod: checkMethod,
                }"
                @checkbox-change="selectChangeEvent"
                align="left"
                :scroll-x="{
                  enabled: true,
                }"
                height="95%">
                <vxe-column
                  type="checkbox"
                  title="ID"
                  tree-node
                  :edit-render="{ autofocus: '.vxe-input--inner' }"
                  show-overflow>
                  <template #default="{ row }">
                    <span>{{ row.name }}</span>
                  </template>
                </vxe-column>
                <!-- <vxe-column field="name" title="Name"></vxe-column> -->
              </vxe-table>
            </div>
          </div>
        </div>
      </div>
      <p class="checks">
        <a-radio-group v-model:value="dataStatus" @change="changeCheck">
          <a-radio value="all">全部</a-radio>
          <a-radio value="part">取消全部</a-radio>
        </a-radio-group>
        <a-checkbox-group v-model:value="state.checkedList" :options="plainOptions" />
        <span style="margin-left: 15px">
          <a-tooltip placement="top" color="white">
            <template #title>
              <div style="color: #000000; font-size: 12px" class="tooltip">
                <p>按照实体中人工费与机械费之和乘以相应系数计算</p>
              </div>
            </template>
            计算基数选择
            <icon-font type="icon-bangzhu" style="margin: 0 0px 0 5px"></icon-font>
          </a-tooltip>
          :
          <!-- 计算基数选择： -->
          <a-select
            v-model:value="csfyCalculateBaseCode"
            style="width: 230px"
            :class="
              csfyCalculateBaseCode && csfyCalculateBaseCode !== 'RGF_DEJ+JXF_DEJ' ? 'redColor' : ''
            "
            :options="caculateBaseList"
            :field-names="{
              label: 'name',
              value: 'code',
            }"
            placeholder="请选择"></a-select>
        </span>
      </p>
      <p class="btns">
        <a-button type="primary" @click="close" ghost>取消</a-button>
        <a-button type="primary" :loading="loading" @click="awfCostMath()">确定</a-button>
      </p>
    </div>
  </a-spin>
</template>
<script setup>
import { ref, watch, reactive, onMounted, onActivated } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
import api from '../../../../api/projectDetail.js';
import csProject, { constructLevelTreeStructureList } from '@/api/csProject.js';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import infoMode from '../../../../plugins/infoMode';
import feePro from '@/api/feePro';
let locationModel = ref(false);
let detailModel = ref(false);
const plainOptions = ['施工期在取暖天数50%以内', '施工期在雨季天数50%以内'];
const state = reactive({
  indeterminate: true,
  checkAll: false,
  checkedList: [],
  csfyCalculateBaseArea: 'other', //记取地区
  csfyCalculateBaseAreaView: true, //为true显示计取地区false就不显示了
});
const emits = defineEmits(['updateData', 'close']);
const unitIdList = ref([]);
const projectStore = projectDetailStore();
const treeData = ref([]);
const expandedKeys = ref(['0-0-0', '0-0-1']);
const selectedKeys = ref(['0-0-0', '0-0-1']);
const checkedKeys = ref(['0-0-0', '0-0-1']);
const classList = ref([]);
const awfQdList = ref([]);
const otherQdList = ref([]);
const zzyList = ref([]);
const zzy = ref();
const increaseFeeHeightList = ref([]);
const csfyCalculateBaseCode = ref(null); //计算基数设置
const caculateBaseList = ref([]);
const route = useRoute();
let checkRowKeys = ref([]); // 默认勾选指定行
// let increaseFeeHeight = ref(''); // 高台增加非高度
let loading = ref(false); // 确定按钮是否点击状态
let cacheData = ref(); //缓存数据
const dataStatus = ref(null);

watch(expandedKeys, () => {
  console.log('expandedKeys', expandedKeys);
});
watch(selectedKeys, () => {
  console.log('selectedKeys', selectedKeys);
});
watch(checkedKeys, () => {
  console.log('checkedKeys', checkedKeys);
});
onMounted(async () => {
  await getMainFeeFile();
  zjcsCostMathCache();
  getTreeList();
  getZzyList();
});

const changeCheck = () => {
  if (dataStatus.value === 'all') {
    classList.value.forEach(item => {
      item.isCheck = 1;
    });
  } else {
    classList.value.forEach(item => {
      item.isCheck = 0;
    });
  }
};

const zjcsCostMathCache = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('总价措施传参', apiData);
  api.zjcsCostMathCache(apiData).then(res => {
    console.log('总价措施缓存数据', res);
    cacheData.value = res && res.result;
    zjcsClassList();
  });
};

let firstSingle = null;
const getTreeListCePing = async () => {
  const res = await constructLevelTreeStructureList(projectStore.currentTreeGroupInfo.constructId);
  let treeData = res.result;
  getParentItem(projectStore.currentTreeInfo, treeData);
};

const getParentItem = (tar, treeList) => {
  //获取目标平铺树结构最外层单项父级
  let parent = treeList?.find(i => i.id === tar.parentId);
  parent && parent?.levelType !== 1 ? getParentItem(parent, treeList) : (firstSingle = { ...tar });
};
let feeFileId = '';
const getMainFeeFile = async () => {
  const res = await api.getMainFeeFile({
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  });
  if (res.code === 200) {
    feeFileId = res.result?.feeFileId;
  }
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    projectStore.currentTreeInfo.levelType === 1
      ? projectStore.currentTreeInfo?.id
      : projectStore.currentTreeGroupInfo?.constructId;
  if (projectStore.currentTreeInfo.levelType === 2) {
    apiData.singleId = projectStore.currentTreeInfo?.id; //单项ID
  }
  if (projectStore.currentTreeInfo.levelType === 3) {
    apiData.singleId = projectStore.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = projectStore.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
const updateZzy = async () => {
  let apiData = {
    // sequenceNbr: row.sequenceNbr,
    name: '二级取费专业',
    context: zzy.value,
    levelType: projectStore.currentTreeInfo.levelType,
    feeFileId: projectStore.feeWithDrawalInfo?.key || feeFileId,
  };
  apiData = getParamsData(apiData);
  console.log('修改费率说明-----获取', 'apiData', apiData);
  const res = await feePro.saveFeeDescription(apiData);
  if (res.status === 200) {
    projectStore.SET_IS_REFRESH_PROJECT_TREE(true);
  }
};
const getZzyList = async () => {
  let apiData = {
    levelType: projectStore.currentTreeInfo.levelType,
    feeFileId: projectStore.feeWithDrawalInfo?.key || feeFileId,
  };
  if (
    apiData.levelType === 3 &&
    projectStore.currentTreeInfo.parentId !== projectStore.currentTreeGroupInfo.constructId
  ) {
    await getTreeListCePing();
    apiData.firstSingle = firstSingle?.id;
  }
  apiData = getParamsData(apiData);
  feePro.getFeeCollectionData(apiData).then(res => {
    const info = res.result?.unitFeeDescriptionList?.find(item => item.name === '二级取费专业');
    info.optionList = info.optionList?.map(listData => {
      return {
        value: Object.values(listData)[0],
      };
    });
    zzyList.value = info?.optionList || [];
    zzy.value = info?.context;
  });
};

const getFinallyData = () => {
  if (cacheData.value) {
    if (cacheData.value.heatingFee) {
      state.checkedList.push('施工期在取暖天数50%以内');
    }
    if (cacheData.value.rainySeasonConstruction) {
      state.checkedList.push('施工期在雨季天数50%以内');
    }
    // increaseFeeHeight.value = cacheData.value.increaseFeeHeight;
    csfyCalculateBaseCode.value = cacheData.value.csfyCalculateBaseCode;
    state.csfyCalculateBaseArea = cacheData.value.csfyCalculateBaseArea;
    state.csfyCalculateBaseAreaView = cacheData.value?.csfyCalculateBaseAreaView;
    // 对比cacheData.value.data和cacheData.value.data数据的ischeck
    console.log('cacheData.value.data', classList.value, cacheData.value.data);
    if (cacheData.value.data && cacheData.value.data.length > 0) {
      cacheData.value.data.map(item => {
        classList.value &&
          classList.value.map(i => {
            if (i.qdName === item.qdName) {
              i.isCheck = item.isCheck;
              i.sequenceNbr = item.sequenceNbr;
            }
            if (
              i.qdName !== '安全文明施工费' &&
              !otherQdList.value.find(a => a.sequenceNbr === i.sequenceNbr)
            ) {
              i.sequenceNbr = null;
            }
          });
      });
    }
  } //  else {
  // gtfResource();
  jsjsResource();
  // }
};

const zjcsClassList = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.zjcsClassList(apiData).then(res => {
    if (res.status === 200 && res.result) {
      classList.value =
        res.result.zjcsClassList &&
        res.result.zjcsClassList.map((item, index) => {
          if (index === 0) {
            if (res.result.awfQd && res.result.awfQd[0]?.zjcsClassCode === item.zjcsClassCode) {
              return {
                ...item,
                sequenceNbr: res.result.awfQd[0].sequenceNbr,
              };
            }
          }
          const obj = res.result.zjcsClassQd.find(qd => qd.zjcsClassCode === item.zjcsClassCode);
          if (obj) {
            return { ...item, sequenceNbr: obj.sequenceNbr };
          } else {
            return item;
          }
        });
      awfQdList.value = res.result.awfQd;
      //此处bug-暂时这样修改
      awfQdList.value.map(item => {
        item.qdCode === null ? (item.qdCode = '') : '';
        item.qdName === null ? (item.qdName = '') : '';
      });
      otherQdList.value = res.result.zjcsClassQd;
      console.log('总价措施项目分类列表', res, classList.value);

      getFinallyData();
    }
  });
};

// 高台施工增加高度列表
// const gtfResource = () => {
//   let apiData = {
//     constructId: projectStore.currentTreeGroupInfo?.constructId,
//     singleId: projectStore.currentTreeGroupInfo?.singleId,
//     unitId: projectStore.currentTreeInfo?.id,
//   };
//   api.gtfResource(apiData).then(res => {
//     if (res.status === 200 && res.result) {
//       increaseFeeHeightList.value = res.result;
//       res.result.forEach(item => {
//         if (item.defaultFlag === 1) {
//           increaseFeeHeight.value = item.addRateHeight;
//         }
//       });
//     }
//   });
// };
//计算基数列表
const jsjsResource = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: 'csfy',
  };
  api.queryCalculateBaseDropDownListNew(apiData).then(res => {
    console.log('queryCalculateBaseDropDownList', apiData, res);
    if (res.status === 200 && res.result) {
      caculateBaseList.value = res.result;
      res.result.forEach(item => {
        if (item.code === 'RGF_DEJ+JXF_DEJ') item.name = item.name + '(默认)';
        if (item.defaultFlag === 1) {
          csfyCalculateBaseCode.value = item.csfyCalculateBaseCode;
        }
      });
    }
  });
};

const getTreeList = () => {
  constructLevelTreeStructureList(route.query.constructSequenceNbr).then(res => {
    console.log('详情res', res);
    if (res.status === 200) {
      checkRowKeys.value = [];
      unitIdList.value = [];
      let list =
        res.result &&
        res.result.filter(
          x =>
            x.levelType !== 3 ||
            (x.levelType === 3 &&
              x.constructMajorType === projectStore.currentTreeInfo?.constructMajorType &&
              x.deStandardReleaseYear === projectStore.currentTreeInfo?.deStandardReleaseYear)
        );
      list.forEach(item => {
        if (item.id === projectStore.currentTreeInfo?.id) {
          checkRowKeys.value.push(item.id);
          unitIdList.value.push(item.id);
        }
      });
      treeData.value = list.filter(
        i => !(i.levelType === 2 && !list.find(a => a.parentId === i.id))
      );
    }
  });
};

const checkMethod = ({ row }) => {
  if (row.id === projectStore.currentTreeInfo?.id) {
    return false;
  } else if (classList.value && classList.value.length > 0) {
    return true;
  } else {
    return false;
  }
};
const selectChangeEvent = ({ checked, row, $table }) => {
  unitIdList.value = [];
  console.log('row', $table.getCheckboxRecords());
  let tempList = $table.getCheckboxRecords();
  tempList.forEach(item => {
    if (item.levelType === 3) {
      unitIdList.value.push(item.id);
    }
  });
};

const updeteRule = row => {
  console.log('row111111111111', row);
};
let loadingModal = ref(false);
const awfCostMath = async () => {
  if (!classList.value) {
    close();
    return;
  }
  // loading.value = true;
  //增加全局loading
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    unitIdList: JSON.parse(JSON.stringify(unitIdList.value)),
    data: JSON.parse(JSON.stringify(classList.value)),
    heatingFee: state.checkedList.includes('施工期在取暖天数50%以内'),
    rainySeasonConstruction: state.checkedList.includes('施工期在雨季天数50%以内'),
    // increaseFeeHeight: increaseFeeHeight.value,
    csfyCalculateBaseCode: csfyCalculateBaseCode.value,
    csfyCalculateBaseArea: state.csfyCalculateBaseArea,
  };
  let isFlag = classList.value.every(item => !item.isCheck);
  if (isFlag) {
    message.warning('请至少选择一项进行计取');
    loading.value = false;
    return;
  }
  loadingModal.value = true;
  if (projectStore.currentTreeInfo.libraryCode == '2022-SZGC-DEK') {
    await updateZzy();
  }
  console.log('apiData', apiData);
  api.awfCostMath(apiData).then(res => {
    console.log('res一键记取', res);
    if (res.status === 200 && res.result) {
      // loading.value = false;
      loadingModal.value = false;
      emits('updateData');
    }
  });
};

const close = () => {
  emits('close');
};
const jqChange = (target, eve) => {
  console.log(target);
  if (eve.value === 0 && target.qdName === '安全文明施工费') {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-qiangtixing',
      infoText: '安全生产、文明施工费为不可竞争费用，是否确定取消自动计算？',
      confirm: () => {
        infoMode.hide();
      },
    });
  }
};
</script>
<style lang="scss" scoped>
.content {
  .selectContent {
    span {
      margin-right: 10px;
    }
  }
  .table-content {
    background-color: rgba(250, 250, 250, 1);
    height: 100%;
  }
}

.title {
  height: 36px;
  background: rgba(234, 234, 234, 0.3);
  border: 1px solid rgba(234, 234, 234, 1);
  line-height: 36px;
  font-size: 14px;
  text-indent: 1rem;
  margin: 0;
}
.checks {
  height: 36px;
  background: rgba(234, 234, 234, 0.3);
  border: 1px solid rgba(234, 234, 234, 1);
  line-height: 36px;
  font-size: 14px;
  margin: 10px 0 0;
  padding-left: 10px;
}
.btns {
  width: 10%;
  display: flex;
  margin: 5px auto;
  justify-content: space-around;
}

::v-deep .table-content .ant-btn-primary {
  margin: 10px !important;
}
::v-deep .ant-btn-primary {
  margin: 10px 42% !important;
}

::v-deep .ant-tree {
  height: calc(100% - 36px);
  background: rgba(234, 234, 234, 0.3);
  border: 1px solid rgba(234, 234, 234, 1);
}
.tooltip {
  p {
    margin: 0;
  }
}
::v-deep(.redColor) {
  color: red !important;
}
</style>
