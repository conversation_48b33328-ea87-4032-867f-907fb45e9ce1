"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RcjStandardModel = exports.RcjNode = void 0;
const node_1 = require("../kernel/node");
class RcjNode extends node_1.Node {
    static create(attrs, marks = [], children = []) {
        return new RcjNode(attrs, marks, children);
    }
    constructor(attrs, marks = [], children = []) {
        super(attrs, marks, children);
        this.validate(attrs);
    }
    validate(_attrs) {
        return true;
    }
    get rcjId() {
        return this.attrs.rcjId;
    }
    get sequenceNbr() {
        return this.attrs.sequenceNbr;
    }
}
exports.RcjNode = RcjNode;
/**
 * 人材机基础模型
 */
class RcjStandardModel extends node_1.StandardModel {
    constructor(attrs, marks = [], children = []) {
        super(attrs, marks, children);
    }
    //根据sequenceNbr 获取人材机
    getRcjById(id) {
        return this.getNodeById(id);
    }
    //添加人材机 指定父节点进行添加
    addRcj(rcj, parentNode) {
        this.addNode(rcj, parentNode);
    }
    //删除人材机
    removeRcj(id) {
        this.removeNode(id);
    }
    //添加人材机 指定父节点进行添加
    addRcjAt(rcj, parent, index) {
        this.addNodeAt(rcj, parent, index);
    }
}
exports.RcjStandardModel = RcjStandardModel;
//# sourceMappingURL=rcj_model.js.map