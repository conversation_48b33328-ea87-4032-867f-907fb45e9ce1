"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseListJobContentQuota = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 清单指引
 */
let BaseListJobContentQuota = class BaseListJobContentQuota extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code_list", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "libraryCodeList", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "list_code", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "listCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "list_id", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "listId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "job_content", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "jobContent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quota_id", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "quotaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quota_code", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "quotaCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quota_name", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "quotaName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "price", nullable: true }),
    __metadata("design:type", Number)
], BaseListJobContentQuota.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sort_no", nullable: true }),
    __metadata("design:type", Number)
], BaseListJobContentQuota.prototype, "sortNo", void 0);
BaseListJobContentQuota = __decorate([
    (0, typeorm_2.Entity)({ name: "base_list_job_content_quota" })
], BaseListJobContentQuota);
exports.BaseListJobContentQuota = BaseListJobContentQuota;
//# sourceMappingURL=BaseListJobContentQuota.js.map