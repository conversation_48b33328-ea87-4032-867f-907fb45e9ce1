"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MeasureProjectTable = void 0;
const BaseModel_1 = require("./BaseModel");
class MeasureProjectTable extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, parentId, kind, dispNo, displayStatu, displaySign, optionMenu, name, zjfPrice, zjfTotal, price, total, rfee, totalRfee, cfee, totalCfee, jfee, totalJfee, managerFee, totalManagerFee, profitFee, totalProfitFee, zcfee, totalZcfee, fxCode, projectAttr, unit, quantity, costMajorName, costFileCode, measureType, constructionMeasureType, totalRgf, totalClf, totalJxf, totalGlf, totalRgdj, totalLr, glfLrDanJia, xmLever, itemCategory, fbId, creator, updater, deptId, status, createDate, updateDate, tenantId, projectType, istb, stlId, zhygfl, libraryCode, fixRate, rateName, rateCode, projectPrice, projectScale, unitId, spId, constructId, formulaRemark, glfRate, lrRate, dingEGroupId, specialQd, quotaId, formula, biddingType, quantityExpressionNbr, quantityExpression, unitCoefficient, standardId, adjustmentCoefficient, isCostDe, isStandard, zjcsClassCode, isAutoCost, isSupplement, rcjFlag, children, isEmpData, matchStatus, costDeMatchType, lockPriceFlag) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.parentId = parentId;
        this.sequenceNbr = sequenceNbr;
        this.kind = kind;
        this.dispNo = dispNo;
        this.displayStatu = displayStatu;
        this.displaySign = displaySign;
        this.optionMenu = optionMenu;
        this.name = name;
        this.zjfPrice = zjfPrice;
        this.zjfTotal = zjfTotal;
        this.price = price;
        this.total = total;
        this.rfee = rfee;
        this.totalRfee = totalRfee;
        this.cfee = cfee;
        this.totalCfee = totalCfee;
        this.jfee = jfee;
        this.totalJfee = totalJfee;
        this.managerFee = managerFee;
        this.totalManagerFee = totalManagerFee;
        this.profitFee = profitFee;
        this.totalProfitFee = totalProfitFee;
        this.zcfee = zcfee;
        this.totalZcfee = totalZcfee;
        this.fxCode = fxCode;
        this.projectAttr = projectAttr;
        this.unit = unit;
        this.quantity = quantity;
        this.costMajorName = costMajorName;
        this.costFileCode = costFileCode;
        this.measureType = measureType;
        this.constructionMeasureType = constructionMeasureType;
        this.totalRgf = totalRgf;
        this.totalClf = totalClf;
        this.totalJxf = totalJxf;
        this.totalGlf = totalGlf;
        this.totalRgdj = totalRgdj;
        this.totalLr = totalLr;
        this.glfLrDanJia = glfLrDanJia;
        this.xmLever = xmLever;
        this.itemCategory = itemCategory;
        this.fbId = fbId;
        this.creator = creator;
        this.updater = updater;
        this.deptId = deptId;
        this.status = status;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.tenantId = tenantId;
        this.projectType = projectType;
        this.istb = istb;
        this.stlId = stlId;
        this.zhygfl = zhygfl;
        this.libraryCode = libraryCode;
        this.fixRate = fixRate;
        this.rateName = rateName;
        this.rateCode = rateCode;
        this.projectPrice = projectPrice;
        this.projectScale = projectScale;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
        this.formulaRemark = formulaRemark;
        this.glfRate = glfRate;
        this.lrRate = lrRate;
        this.dingEGroupId = dingEGroupId;
        this.specialQd = specialQd;
        this.quotaId = quotaId;
        this.formula = formula;
        this.biddingType = biddingType;
        this.quantityExpressionNbr = quantityExpressionNbr;
        this.quantityExpression = quantityExpression;
        this.unitCoefficient = unitCoefficient;
        this.standardId = standardId;
        this.adjustmentCoefficient = adjustmentCoefficient;
        this.isCostDe = isCostDe;
        this.isStandard = isStandard;
        this.zjcsClassCode = zjcsClassCode;
        this.isAutoCost = isAutoCost;
        this.isSupplement = isSupplement;
        this.rcjFlag = rcjFlag;
        this.children = children;
        this.isEmpData = isEmpData;
        this.matchStatus = matchStatus;
        this.costDeMatchType = costDeMatchType;
        this.lockPriceFlag = lockPriceFlag;
    }
}
exports.MeasureProjectTable = MeasureProjectTable;
//# sourceMappingURL=MeasureProjectTable.js.map