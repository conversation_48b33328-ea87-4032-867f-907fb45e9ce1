"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectServiceCost = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectServiceCost extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, dispNo, sortNo, fxName, amount, xmje, serviceContent, rate, fwje, unitId, spId, constructId, dataType, parentId) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.dispNo = dispNo;
        this.sortNo = sortNo;
        this.fxName = fxName;
        this.amount = amount;
        this.xmje = xmje;
        this.serviceContent = serviceContent;
        this.rate = rate;
        this.fwje = fwje;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
        this.dataType = dataType;
        this.parentId = parentId;
    }
}
exports.OtherProjectServiceCost = OtherProjectServiceCost;
//# sourceMappingURL=OtherProjectServiceCost.js.map