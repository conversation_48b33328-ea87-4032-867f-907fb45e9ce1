"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRuleDetailFull = void 0;
const typeorm_1 = require("typeorm");
let BaseRuleDetailFull = class BaseRuleDetailFull {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relationGroupCode", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_id" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relationGroupId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_name" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relationGroupName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_cnt" }),
    __metadata("design:type", Number)
], BaseRuleDetailFull.prototype, "relationGroupCnt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_rule" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relationGroupRule", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_details_id" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "fileDetailsId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relationCode", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relation", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "math", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_id" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "deId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "default_value" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "defaultValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "default_value_max" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "defaultValueMax", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "default_value_min" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "defaultValueMin", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rule_range" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "ruleRange", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_de_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relationDeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_de_id" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "relationDeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "data_format" }),
    __metadata("design:type", Number)
], BaseRuleDetailFull.prototype, "dataFormat", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sort_no" }),
    __metadata("design:type", Number)
], BaseRuleDetailFull.prototype, "sortNo", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rec_user_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "recUserCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rec_status" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "recStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rec_date" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "recDate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "extend1", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "extend2", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "extend3", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "agency_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "agencyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "product_code" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "productCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "top_group_type" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "topGroupType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rcj_id" }),
    __metadata("design:type", String)
], BaseRuleDetailFull.prototype, "rcjId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sort_no_global' }),
    __metadata("design:type", Number)
], BaseRuleDetailFull.prototype, "sortNoGlobal", void 0);
BaseRuleDetailFull = __decorate([
    (0, typeorm_1.Entity)({
        name: "base_rule_details_full",
    })
], BaseRuleDetailFull);
exports.BaseRuleDetailFull = BaseRuleDetailFull;
//# sourceMappingURL=BaseRuleDetailFull.js.map