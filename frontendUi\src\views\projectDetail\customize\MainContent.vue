<!--
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-10 10:40:54
-->
<template>
  <div class="tab-menus">
    <!-- {{currentInfo}} -->
    <tab-menu @getActiveKey="getActiveKey" ref="tabMenuRef"></tab-menu>
    <div class="other-menus">
      <div
        v-if="
          ((componentId === 'subItemProject' || componentId === 'measuresItem') &&
            currentInfo?.kind) ||
          componentId === 'summaryExpense' ||
          (componentId === 'humanMachineSummary' &&
            [8, 10].includes(+projectStore.asideMenuCurrentInfo?.key))
        "
        class="tab-menus-btnList">
        <!-- 编辑区切换项目特征等单行展示、多行展示，默认多行展 -->
        <a-button
          v-if="componentId === 'subItemProject' || componentId === 'measuresItem'"
          type="text"
          @click="rowHeightSwitching()">
          <icon-font type="icon-kongzhihanggao"></icon-font>
          {{ projectStore.rowHeight ? '缩放' : '展开' }}
        </a-button>

        <template
          v-if="
            componentId === 'subItemProject' &&
            (currentInfo.kind === '01' || currentInfo.kind === '02')
          ">
          <a-button type="text" @click="moveDeData(1, 'level')" :disabled="!currentInfo.isUpFb">
            <icon-font
              type="icon-shengji"
              :class="!currentInfo.isUpFb ? 'disabled-icon' : ''"></icon-font>
            升级
          </a-button>
          <a-button type="text" @click="moveDeData(2, 'level')" :disabled="!currentInfo.isDownFb">
            <icon-font
              type="icon-jiangji"
              :class="!currentInfo.isDownFb ? 'disabled-icon' : ''"></icon-font>
            降级
          </a-button>
        </template>
        <a-button
          v-if="currentInfo?.kind !== '0'"
          type="text"
          @click="moveDeData(1, 'move')"
          :disabled="moveBtnDisabled(1)">
          <icon-font
            type="icon-shangyi"
            :class="moveBtnDisabled(1) ? 'disabled-icon' : ''"></icon-font>
          上移
        </a-button>
        <a-button
          v-if="currentInfo?.kind !== '0'"
          type="text"
          @click="moveDeData(2, 'move')"
          :disabled="moveBtnDisabled(2)">
          <icon-font
            type="icon-xiayi"
            :class="moveBtnDisabled(2) ? 'disabled-icon' : ''"></icon-font>
          下移
        </a-button>
      </div>
      <div class="extend-content"></div>
      <a-button v-if="showPageBtn" type="text" @click="openColumnSetting">
        <icon-font type="icon-xianshilieshezhi"></icon-font>
      </a-button>
    </div>
  </div>
  <div class="main-content">
    <split
      :ratio="!asideMenuRef?.isContract ? '1/7' : `47/${winWidth}`"
      :minHorizontalTop="asideMenuRef?.isContract ? 36 : 188"
      :maxHorizontalTop="
        projectStore.tabSelectName !== '分部分项' ? 188 : asideMenuRef?.isContract ? 36 : 600
      "
      :isDrop="projectStore.tabSelectName === '分部分项' && !asideMenuRef?.isContract"
      :onlyPart="isDisplay === 'none' ? 'Bootom' : 'all'"
      style="height: 100%">
      <template #one>
        <AsideMenuList
          style="height: 100%"
          ref="asideMenuRef"
          :title="asideTitle"
          :menuList="asideMenuList"
          :treeData="asideMenuList"
          :isTreeData="isTreeData"
          :isHumanData="isHumanData"
          :isDisplay="isDisplay"
          @currentMenu="currentMenu"
          @updateMenuList="updateMenuList"
          v-model:updateStatus="updateStatus"
          @setMainFile="setMainFile" />
      </template>
      <template #two>
        <div class="content" ref="content" style="height: 100%">
          <keep-alive>
            <component
              :is="components.get(componentId)"
              :key="componentId"
              :componentId="componentId"
              @updateMenuList="updateMenuList"
              ref="childComponentRef"
              @vue:updated="onMountedChange"
              @getCurrentInfo="getCurrentInfo"
              @getMoveInfo="getMoveInfo"></component>
          </keep-alive>
          <combined-search></combined-search>
        </div>
      </template>
    </split>
    <!-- :style="`height:${contentHeight}px`" -->
    <pro-common-model
      :currentInfo="currentInfo"
      @updateMenuList="updateMenuList"></pro-common-model>
  </div>
</template>

<script setup>
import {
  defineAsyncComponent,
  markRaw,
  onBeforeUnmount,
  ref,
  provide,
  onMounted,
  createApp,
  watch,
  inject,
  reactive,
  nextTick,
  getCurrentInstance,
  onUnmounted,
  onActivated,
  computed,
} from 'vue';
import TabMenu from './TabMenu.vue';
import AsideMenuList from './AsideMenuList.vue';
import csProject from '../../../api/csProject';
import { projectDetailStore } from '../../../store/projectDetail';
import CombinedSearch from '@/views/projectDetail/customize/measuresItem/combinedSearch.vue';
import api from '@/api/projectDetail.js';
import proCommonModel from './proCommonModel.vue';
import { getComponents } from './mainContentComponents.js';
import jiesuanApi from '@/api/jiesuanApi.js';
const projectStore = projectDetailStore();
const props = defineProps(['projectComponentType']);
import split from '@/components/split/index.vue';
let childComponentRef = ref();
let childComponentRefMap = ref({});

const openColumnSetting = () => {
  let currentRef = childComponentRefMap.value[componentId.value] || childComponentRef.value;
  if (currentRef?.showPageColumnSetting) currentRef.showPageColumnSetting();
};
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;

const providedMethod = inject('providedMethod');
const shouldRenderComponent = ref(true);

const components = getComponents(props.projectComponentType || projectStore.type);
// let componentId = ref("feeWithDrawalTable");
let componentId = ref('basicInfo'); //根据tab栏选中的tab加载对应页面
const content = ref();
let contentHeight = ref();
const activeKey = ref(1);
let isTreeData = ref(false);
let isHumanData = ref(false); // 是否为人材机调整左侧数据
let isDisplay = ref('block');
let asideMenuList = ref([]);
let asideTitle = ref('项目概况');
// let asideTitle = ref('');
let tabSelectName = ref();
let featureData = ref([]);
let updateStatus = ref(false);
let currentInfo = ref(); // 分部分项措施项目点击的当前数据
let moveInfo = ref(); // 费用汇总上移下移信息
provide('mainData', {
  currentInfo: computed(() => currentInfo.value),
  componentId: computed(() => componentId.value),
});
const showPageBtn = computed(() => {
  let arr = ['CostAnalysis', 'subItemProject', 'measuresItem', 'humanMachineSummary'];
  if (componentId.value === 'CostAnalysis' && projectStore.currentTreeInfo.levelType === 3) {
    return false;
  }
  return arr.includes(componentId.value);
});
const moveBtnDisabled = type => {
  if (type === 1) {
    return (
      currentInfo.value?.isFirst ||
      moveInfo.value?.isCurrent === 0 ||
      [94, 95].includes(currentInfo.value?.kind)
    );
  }
  if (type === 2) {
    return (
      currentInfo.value?.isLast ||
      moveInfo.value?.isLast ||
      [94, 95].includes(currentInfo.value?.kind)
    );
  }
};
onActivated(() => {});

// onMounted(() => {
//   alert('1234')

// }),
const isShowMoveBtn = () => {
  if (projectStore.type === 'jieSuan' && currentInfo.value?.originalFlag) {
    return false;
  }
  return currentInfo.value?.kind !== '0';
};
bus.on('vertical-transport', ({ event, name }) => {
  // if (name === 'subItemProject') copyAndPaste(event);
});

watch(
  () => componentId.value,
  val => {
    projectStore.SET_COMPONENT_ID(val);
  },
  { deep: true, immediate: true }
);

const onMountedChange = () => {
  if (
    childComponentRef.value &&
    componentId.value === 'subItemProject' &&
    !projectStore.subItemProjectAutoPosition
  ) {
    projectStore.subItemProjectAutoPosition = childComponentRef.value;
  }
  // if (
  //   childComponentRef.value &&
  //   componentId.value === 'CostAnalysis' &&
  //   !costAnalysisComponentRef.value
  // ) {
  //   costAnalysisComponentRef.value = childComponentRef.value;
  // }
};
watch(
  () => childComponentRef.value,
  (val, oldValue) => {
    if (val) {
      childComponentRefMap.value[componentId.value] = val;
      console.log(
        '🚀 ~ openColumnSetting ~ childComponentRefMap.value:',
        childComponentRefMap.value
      );
    }
    if (val && componentId.value === 'subItemProject' && !projectStore.subItemProjectAutoPosition) {
      projectStore.subItemProjectAutoPosition = val;
    }

    if (
      val &&
      componentId.value === 'measuresItem' &&
      !projectStore.measuresItemProjectAutoPosition
    ) {
      projectStore.measuresItemProjectAutoPosition = val;
    }
    if (val && componentId.value === 'qtxmStatistics' && !projectStore.otherProjectAutoPosition) {
      projectStore.otherProjectAutoPosition = val;
    }

    if (val && componentId.value === 'qtxmJrg' && !projectStore.dailyWorkProjectAutoPosition) {
      projectStore.dailyWorkProjectAutoPosition = val;
    }

    if (val && componentId.value === 'qtxmZcbfwf' && !projectStore.serviceProjectAutoPosition) {
      projectStore.serviceProjectAutoPosition = val;
    }

    if (
      val &&
      componentId.value === 'humanMachineSummary' &&
      !projectStore.summaryProjectAutoPosition
    ) {
      projectStore.summaryProjectAutoPosition = val;
    }
    if (
      val &&
      componentId.value === 'feeWithDrawalTable' &&
      !projectStore.feeWithDrawalAutoPosition
    ) {
      projectStore.feeWithDrawalAutoPosition = val;
    }
    if (val && componentId.value === 'CostAnalysis' && !projectStore.costAnalysisComponentRef) {
      projectStore.costAnalysisComponentRef = val;
    }
  },
  { deep: true }
);

let tabMenuRef = ref(null);
watch(
  () => tabMenuRef.value,
  val => {
    projectStore.tabMenuRef = val;
  },
  { deep: true }
);
let asideMenuRef = ref(null);
watch(
  () => asideMenuRef.value,
  val => {
    projectStore.asideMenuRef = val;
  },
  { deep: true }
);
let winWidth = ref();
watch(
  () => asideMenuRef.value?.isContract,
  () => {
    if (!asideMenuRef.value?.isContract) {
      winWidth.value = window.innerWidth;
    }
  }
);
watch(
  () => projectStore.subCurrentInfo,
  value => {
    currentInfo.value = value;
  }
);
watch(
  () => [projectStore.standardGroupOpenInfo.isOpen, projectStore.tabSelectName],
  value => {
    // debugger;
    if (projectStore.standardGroupOpenInfo.isOpen && projectStore.tabSelectName === '分部分项') {
      getActiveKey('4', '分部分项');
    }
  }
);
//编制说明
let bzsm = ref({});

// watch(
//   () => projectStore.currentTreeInfo,
//   () => {
//     // if (
//     // 	projectStore.tabSelectName !== '造价分析' &&
//     // 	projectStore.tabSelectName !== '费用汇总'
//     // ) {
//     //造价分析页面不需要加载侧边栏
//     if (projectStore.currentTreeInfo.levelType !== 2) {
//       if (
//         projectStore.currentTreeInfo.levelType === 3 &&
//         tabSelectName.value === '费用汇总'
//       ) {
//         componentId.value = 'summaryExpense';
//         isDisplay.value = 'none';
//       } else {
//         // activeKey.value = 1; //侧边栏切换tab栏默认切换成选择第一项
//         getMenuData(); //根据选中tab栏获取项目概况侧边栏
//       }
//     }
//   }
// );

const handleCopyEvent = event => {
  const activeComponent = components.get(componentId.value);
  if (activeComponent) {
    // 调用活动组件的复制操作
    // childComponentRef.value.copyAndPaste(event);
    bus.emit('handleCopyEvent', { event, name: componentId.value });
  }
};
onMounted(() => {
  window.addEventListener('keydown', handleCopyEvent); // 修改捕获阶段触发事件
  contentHeight.value = content.value.clientHeight;
  queryUnit();
});

const queryUnit = () => {
  let apiData = {
    dictCode: 'UNIT_DICT',
  };
  csProject.otherProjectSysDictionary(apiData).then(res => {
    let list = [];
    let unitListString = [];
    if (res.status === 200) {
      res.result &&
        res.result.map(item => {
          list.push(item.entryValue);
        });
      unitListString = list ? String(list) : '';
      projectStore.SET_UNITLISTSTRING(unitListString);
    }
  });
};

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleCopyEvent);
});
const getActiveKey = (key, tabName) => {
  console.log('getActiveKey', key, tabName, projectStore.standardGroupOpenInfo);
  // debugger;
  tabSelectName.value = tabName;
  asideTitle.value = tabName;
  projectStore.SET_TAB_SELECT_NAME(tabName);
  // debugger;
  if (tabSelectName.value === '造价分析') {
    projectStore.SET_Cost_Analysis_Code(key);
  } else {
    projectStore.SET_Cost_Analysis_Code(null);
  }
  if (tabSelectName.value === '费用汇总') {
    componentId.value = 'summaryExpense';
    isDisplay.value = 'none';
  }

  if (tabSelectName.value === '人材机汇总' || tabSelectName.value === '人材机调整') {
    componentId.value = 'humanMachineSummary';
  }
  // if (key === 4) {
  //   componentId.value = 'subItemProject';
  // }
  activeKey.value = key;
  if (projectStore.currentTreeInfo.levelType !== 2) {
    if (projectStore.currentTreeInfo.levelType === 3 && tabSelectName.value === '费用汇总') {
      componentId.value = 'summaryExpense';
      isDisplay.value = 'none';
    } else {
      getMenuData(); //根据选中tab栏获取项目概况侧边栏
    }
  } else {
    if (tabSelectName.value !== '造价分析') {
      getMenuData(); //根据选中tab栏获取项目概况侧边栏
    } else {
      componentId.value = 'CostAnalysis';
      isDisplay.value = 'none';
    }
  }
  currentContent(tabSelectName);
};

const updateMenuList = () => {
  updateStatus.value = true;
  getMenuData(1);
};

const getCurrentInfo = value => {
  currentInfo.value = value;
};

const getMoveInfo = value => {
  console.log('111111111111', value);
  currentInfo.value = null;
  moveInfo.value = value;
};

const getMenuData = (status = 0) => {
  //新建单位工程跳转到详情页currentTreeGroupInfo没有赋值

  if (
    //projectStore.currentTreeInfo?.levelType === 3 &&
    !projectStore.currentTreeGroupInfo
  ) {
    let obj = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.levelType === 3 ? projectStore.currentTreeInfo?.id : '',
      singleId: '',
    };
    // projectStore.SET_CURRENT_TREE_GROUP_INFO(obj);
  }
  let apiData = {
    type: 1, // 编制
    levelType: projectStore.currentTreeInfo?.levelType,
    code: activeKey.value,
    unitId: projectStore.currentTreeInfo?.levelType < 3 ? '' : projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId:
      projectStore.currentTreeInfo?.levelType === 1
        ? ''
        : projectStore.currentTreeInfo?.levelType === 2
          ? projectStore.currentTreeInfo?.id
          : projectStore.currentTreeGroupInfo?.singleId,
  };
  if (!apiData.levelType) {
    //levelType不存在不掉接口，这个是偶现，一次性调好多次
    return;
  }
  console.log('00000000000', projectStore.type);
  let apiName = csProject.getMenuData;
  if (projectStore.type === 'jieSuan') {
    apiName = jiesuanApi.jSGetMenuData;
  }
  console.log('apiName', apiName);
  apiName(apiData).then(res => {
    console.log('getMenuData--------------', res, apiData);
    if (res.status === 200) {
      if (res.result && res.result.itemList) {
        isTreeData.value = false;
        isHumanData.value = false;
        let tempList = [];
        res.result.itemList.forEach(item => {
          let obj = {
            name: Object.values(item)[0],
            key: Object.keys(item)[0],
            defaultFeeFlag: Object.values(item)[1] ? Object.values(item)[1] : '0',
            frequencyList: [],
          };
          if (
            projectStore.standardGroupOpenInfo.isOpen &&
            projectStore.tabSelectName === '人材机汇总'
          ) {
            const deleteList = [7, 8, 9, 10];
            if (!deleteList.includes(Number(obj.key))) {
              tempList.push(obj);
            }
          } else {
            tempList.push(obj);
          }
        });
        if (projectStore.tabSelectName === '人材机汇总') {
          tempList.map(item => {
            if (item.name.trim() === '主要材料表') {
              item.name = '主要材料、设备表';
            }
            if (item.name.trim() === '承包人供应材料和设备') {
              item.name = '承包人主要材料和设备';
            }
            item.id = 'rcjhz' + '-' + projectStore.currentTreeInfo?.id + '-' + item.key;
          });
        }
        console.log('tempList', tempList);
        if (projectStore.tabSelectName === '人材机调整') {
          if (
            projectStore.currentTreeInfo.levelType === 3 &&
            projectStore.currentTreeInfo?.originalFlag
          ) {
            isHumanData.value = true;
          }
          tempList.map(item => {
            if (item.name.trim() === '主要材料表') {
              item.name = '主要材料、设备表';
            }
          });
        }
        asideMenuList.value = tempList;
      }
      if (res.result && res.result.treeModel) {
        isTreeData.value = true;
        isHumanData.value = false;
        let treeModel = res.result.treeModel;
        // if (projectStore.tabSelectName === '其他项目') {
        //   treeModel.childTreeModel = treeModel.childTreeModel.filter(
        //       i => i.sequenceNbr !== 'qtxm09'
        //   );
        // }
        asideMenuList.value = [treeModel];
      }
      //如果返回为空，列表展示空
      if (!(res.result && res.result.itemList) && !(res.result && res.result.treeModel)) {
        asideMenuList.value = [];
        updateStatus.value = false;
      }
      if (!status) {
        currentContent(); //加载对应页面
      }
      if (projectStore.standardGroupOpenInfo.isOpen && projectStore.tabSelectName === '分部分项') {
        // debugger;
        projectStore.SET_ASIDE_MENU_CURRENT_INFO(asideMenuList.value[0]);
      }
      //项目概况侧边栏第一项设置为选中
      // projectStore.SET_Fee_With_Drawal_Info(asideMenuList.value[0]);
      // currentMenu(asideMenuList.value[0]);
    }
  });
};
const setMainFile = seq => {
  asideMenuList.value.map(i => {
    i.defaultFeeFlag = i.key === seq ? '1' : '0';
  });
  console.log('setMainFile', seq, asideMenuList.value);
};
//点击tab获取相应页面
const currentContent = () => {
  isDisplay.value = 'block';
  shouldRenderComponent.value = true;
  switch (tabSelectName.value) {
    case '造价分析':
      componentId.value = 'CostAnalysis';
      isDisplay.value = 'none';
      break;
    case '取费表':
      componentId.value = 'feeWithDrawalTable';
      projectStore.SET_Fee_With_Drawal_Info(asideMenuList.value[0]);
      // currentMenu(asideMenuList.value[0]);
      break;
    case '费用汇总':
      isDisplay.value = 'none';
      componentId.value = 'summaryExpense';
      break;
    case '项目概况':
    case '工程概况':
      componentId.value = 'basicInfo';
      break;
    case '其他项目':
      componentId.value = 'qtxmStatistics';
      break;
    case '人材机汇总':
      componentId.value = 'humanMachineSummary';
      break;
    case '分部分项':
      componentId.value = 'subItemProject';
      break;
    case '措施项目':
      isDisplay.value = 'none';
      componentId.value = 'measuresItem';
      break;
  }
  shouldRenderComponent.value = false;
};
const currentMenu = item => {
  shouldRenderComponent.value = true;
  switch (item.key) {
    case '11':
      componentId.value = 'basicInfo';
      break;
    case '12':
      componentId.value = 'PreparationOfInstructions';
      break;
    case '13':
      componentId.value = 'engineerFeature';
      break;
    case 'qtxm00':
      componentId.value = 'qtxmStatistics';
      break;
    case 'qtxm01':
      componentId.value = 'qtxmZlje';
      break;
    case 'qtxm02':
      componentId.value = 'qtxmClzgj';
      break;
    case 'qtxm03':
      componentId.value = 'qtxmSbzgj';
      break;
    case 'qtxm04':
      componentId.value = 'qtxmZygczgj';
      break;
    case 'qtxm05':
      componentId.value = 'qtxmZcbfwf';
      break;
    case 'qtxm06':
      componentId.value = 'qtxmJrg';
      break;
    case 'qtxm07':
      componentId.value = 'qtxmZyclsb';
      break;
    case 'qtxm08':
      componentId.value = 'qtxmJgclsb';
      break;
    case 'qtxm09':
      componentId.value = 'qtxmQzysp';
      break;
  }
  shouldRenderComponent.value = false;
};
defineExpose({
  componentId,
  childComponentRef,
});

const moveDeData = (state, type) => {
  bus.emit('moveDeData', { state, type });
};
const rowHeightSwitching = () => {
  projectStore.SET_ROW_HEIGHT();
};
</script>
<style lang="scss" scoped>
.other-menus {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  white-space: nowrap;
}
.tab-menus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 42px;
  border-bottom: 2px solid #dcdfe6;
  padding-right: 10px;
  overflow-x: auto;
  overflow-y: hidden;
  &-btnList {
    .ant-btn {
      padding: 0 5px;
    }
  }
}
.main-content {
  display: flex;
  height: calc(
    100vh - var(--project-detail-header-height) - var(
        --project-detail-functional-area-height
      ) - var(--project-detail-footer) - var(--project-detail-main-content-tabs-menu-height)
  );
}
.content {
  flex: 1;
  max-height: 100%;
  overflow: hidden;
  background: #ffffff;
}
.disabled-icon {
  opacity: 0.5;
}
</style>
