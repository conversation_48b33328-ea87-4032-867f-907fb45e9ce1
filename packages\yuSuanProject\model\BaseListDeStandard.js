"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseListDeStandard = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 清单定额标准表
 */
let BaseListDeStandard = class BaseListDeStandard extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseListDeStandard.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "area_id" }),
    __metadata("design:type", String)
], BaseListDeStandard.prototype, "areaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseListDeStandard.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "release_year" }),
    __metadata("design:type", String)
], BaseListDeStandard.prototype, "releaseYear", void 0);
BaseListDeStandard = __decorate([
    (0, typeorm_1.Entity)({ name: "base_list_de_standard" })
], BaseListDeStandard);
exports.BaseListDeStandard = BaseListDeStandard;
//# sourceMappingURL=BaseListDeStandard.js.map