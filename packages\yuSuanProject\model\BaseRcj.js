"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRcj = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * base 人材机表
 */
let BaseRcj = class BaseRcj extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "level1", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "level1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "level2", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "level2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "level3", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "level3", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "level4", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "level4", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "level5", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "level5", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "level6", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "level6", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "level7", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "level7", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_code", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "materialCode", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "kind", nullable: true }),
    __metadata("design:type", Number)
], BaseRcj.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_name", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "materialName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "specification", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "specification", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "de_price", nullable: true }),
    __metadata("design:type", Number)
], BaseRcj.prototype, "dePrice", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "market_price", nullable: true }),
    __metadata("design:type", Number)
], BaseRcj.prototype, "marketPrice", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "tax_removal", nullable: true }),
    __metadata("design:type", Number)
], BaseRcj.prototype, "taxRemoval", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "level_mark", nullable: true }),
    __metadata("design:type", String)
], BaseRcj.prototype, "levelMark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_fyrcj", nullable: true }),
    __metadata("design:type", Number)
], BaseRcj.prototype, "isFyrcj", void 0);
BaseRcj = __decorate([
    (0, typeorm_2.Entity)({ name: "base_rcj" })
], BaseRcj);
exports.BaseRcj = BaseRcj;
//# sourceMappingURL=BaseRcj.js.map