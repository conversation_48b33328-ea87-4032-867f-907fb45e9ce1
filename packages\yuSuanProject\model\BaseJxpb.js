"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseJxpb = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * base 机械明细表
 */
let BaseJxpb = class BaseJxpb extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rcj_id", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "rcjId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "jx_code", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "jxCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "jx_name", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "jxName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_code", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "materialCode", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "kind", nullable: true }),
    __metadata("design:type", Number)
], BaseJxpb.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "material_name", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "materialName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "specification", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "specification", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit", nullable: true }),
    __metadata("design:type", String)
], BaseJxpb.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "res_qty", nullable: true }),
    __metadata("design:type", Number)
], BaseJxpb.prototype, "resQty", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "de_price", nullable: true }),
    __metadata("design:type", Number)
], BaseJxpb.prototype, "dePrice", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "market_price", nullable: true }),
    __metadata("design:type", Number)
], BaseJxpb.prototype, "marketPrice", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "tax_removal", nullable: true }),
    __metadata("design:type", Number)
], BaseJxpb.prototype, "taxRemoval", void 0);
BaseJxpb = __decorate([
    (0, typeorm_2.Entity)({ name: "base_jxpb" })
], BaseJxpb);
exports.BaseJxpb = BaseJxpb;
//# sourceMappingURL=BaseJxpb.js.map