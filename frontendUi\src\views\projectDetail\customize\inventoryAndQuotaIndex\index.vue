<!--
 * @Descripttion: 清单定额人材机索引
 * @Author: liuxia
 * @Date: 2023-05-25 17:52:23
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-01 14:32:09
-->
<template>
  <div class="inventory-dialog-wrap">
    <common-modal
      v-model:model-value="props.indexVisible"
      class-name="dialog-comm resizeClass"
      :title="dialogTitle"
      :mask="false"
      :lock-view="false"
      :lock-scroll="false"
      width="1040"
      height="75vh"
      min-height="350"
      min-width="900"
      :resize="true"
      :show-zoom="true"
      @cancel="close"
      @close="close"
      @resize="onResize"
    >
      <div class="dialog-wrap">
        <div class="contentCenter">
          <div class="left">
            <p class="head">
              <a-radio-group v-model:value="value1" button-style="solid" @change="radioChange">
                <a-radio-button value="qdzy">清单指引</a-radio-button>
                <a-radio-button value="dingE">定额</a-radio-button>
                <a-radio-button value="qingDan">清单</a-radio-button>
                <a-radio-button value="renCJ">人材机</a-radio-button>
              </a-radio-group>
            </p>
            <div class="search">
              <IndexSearch
                v-if="props.indexVisible"
                v-model:value="bdName"
                :type="value1"
                @search="onSearch"></IndexSearch>

              <a-select
                v-model:value="selectValue"
                style="width: 95%; margin: 0 8px 10px"
                :options="selectOptions"
                placeholder="请选择"
                :field-names="fieldnames"
                @change="handleChange"></a-select>
              <div class="tree">
                <a-tree
                  v-if="treeData && treeData.length"
                  ref="treeRef"
                  v-model:expanded-keys="expandedKeys"
                  v-model:selected-keys="selectedKeys"
                  :tree-data="treeData"
                  style="z-index: 10"
                  :field-names="{
                    title:
                      value1 === 'qingDan'
                        ? 'bdName'
                        : value1 === 'dingE'
                          ? 'name'
                          : 'materialName',
                    children: 'childrenList',
                    key: ['qingDan', 'qdzy'].includes(value1) ? 'sequenceNbr' : 'key',
                  }"
                  @select="selectChildren">
                  <!-- :height="treeHeight" -->
                  <template #icon="{ key }">
                    <template v-if="key !== '0-0'"></template>
                  </template>
                  <template v-if="value1 === 'qdzy'" #title="row">
                    <span @dblclick="handleDbClickQdzy">
                      {{ row?.bdCodeLevel04 }} {{ row?.bdName }}
                    </span>
                  </template>
                </a-tree>
              </div>
            </div>
          </div>
          <div class="right">
            <p class="btns">
              <a-radio-group
                v-show="value1 !== 'qdzy'"
                v-model:value="value2"
                button-style="solid"
                @change="changeRadio(value2)">
                <a-radio-button value="list">
                  {{ value1 === 'qingDan' ? '清单' : value1 === 'dingE' ? '定额' : '人材机' }}列表
                </a-radio-button>
                <a-radio-button v-if="value1 !== 'renCJ'" value="des">章节说明</a-radio-button>
                <a-radio-button v-if="value1 === 'dingE'" value="compute">计算规则</a-radio-button>
                <a-radio-button v-if="value1 === 'dingE'" value="work">工作内容</a-radio-button>
              </a-radio-group>
              <span v-show="value1 !== 'qdzy'">
                <a-button
                  type="primary"
                  class="btnNo1"
                  :disabled="
                    isInsertDisabled ||
                    (originInfo.isLocked && value1 === 'qingDan') ||
                    (store.standardGroupOpenInfo.isOpen && value1 === 'qingDan')
                  "
                  @click="updateCurrentInfo(1)">
                  插入
                </a-button>
                <a-button
                  type="primary"
                  :disabled="
                    isDisabled ||
                    (originInfo.isLocked && value1 === 'qingDan') ||
                    (store.standardGroupOpenInfo.isOpen && value1 === 'qingDan')
                  "
                  @click="updateCurrentInfo(2)">
                  替换
                </a-button>
              </span>

              <span v-show="value1 === 'qdzy'" class="qdzy-wrap">
                <a-button
                  type="primary"
                  class="qdzy-btns"
                  :disabled="isQdzyCrZm"
                  @click="updateCurrentInfo(1, 'zm')">
                  插入子目
                </a-button>
                <a-button
                  type="primary"
                  class="qdzy-btns"
                  :disabled="
                    isQdzyCrQd || store.standardGroupOpenInfo.isOpen || originInfo.isLocked
                  "
                  @click="updateCurrentInfo(1, 'qd')">
                  插入清单
                </a-button>
                <a-button
                  type="primary"
                  class="qdzy-btns"
                  :disabled="isQdzyTh || store.standardGroupOpenInfo.isOpen || originInfo.isLocked"
                  @click="updateCurrentInfo(1, 'th')">
                  替换清单
                </a-button>
              </span>
            </p>

            <div v-if="value1 !== 'qdzy'" class="table table-content" ref="tabContentRef">
              <split
                v-show="value2 === 'list'"
                horizontal
                :ratio="splitRatio"
                style="height: 100%"
                :horizontalBottom="5"
                :minHorizontalTop="minHorizontalTop"
                :only-part="value1 === 'renCJ' ? 'all' : 'Top'"
                mode="vertical"
                @onDragHeight="dragHeight">
                <template #one>
                  <s-table
                    ref="sTableRef"
                    size="small"
                    class="s-table"
                    :columns="showColumns"
                    row-key="sequenceNbr"
                    bordered
                    :delay="200"
                    :range-selection="true"
                    :custom-row="sTableCustomRows"
                    :row-selection="rowSelection"
                    :scroll="{ y: sTableHeight }"
                    :animate-rows="false"
                    :pagination="false"
                    :data-source="tableData"
                    :row-height="29"
                    @mouseup="cellMouseup"></s-table>
                </template>
                <template v-if="value1 === 'renCJ'" #two>
                  <div style="position: relative; height: 100%">
                    <Teleport v-if="tableData.length" to=".table-content .resizer">
                      <div class="contract-btn" @click.stop="toggleContract">
                        <DownOutlined v-if="isContract" />
                        <UpOutlined v-else />
                      </div>
                    </Teleport>
                    <s-table
                      v-if="sTableHeightBottom"
                      ref="rcjTableRef"
                      size="small"
                      class="s-table"
                      :columns="rcjColumns"
                      row-key="sequenceNbr"
                      bordered
                      :delay="200"
                      :scroll="{ y: sTableHeightBottom }"
                      :custom-row="rcjCustomRows"
                      :animate-rows="false"
                      :pagination="false"
                      :data-source="rcjTableData"
                      :row-height="29"></s-table>
                  </div>
                </template>
              </split>

              <div v-if="value2 !== 'list'" style="width: 100%; height: 100%">
                <div style="width: 100%; height: 100%" class="flex-center">
                  <iframe
                    v-if="desHtml.ossUrl"
                    :src="desHtml.ossUrl"
                    title="description"
                    width="100%"
                    height="100%"></iframe>
                  <img
                    v-if="!desHtml.ossUrl"
                    src="@/assets/img/data-null.png"
                    alt=""
                    class="noData" />
                  <!-- <p  class="noData">暂无数据</p> -->
                </div>
              </div>
            </div>

            <!-- 清单指引 -->
            <div v-if="value1 === 'qdzy'" class="table qdzy-table">
              <vxe-table
                ref="vexTable"
                border
                align="center"
                :column-config="{ resizable: true }"
                :data="qdzyTableData"
                :check-strictly="false"
                :row-class-name="rowClassName"
                height="100%"
                :row-config="{
                  isCurrent: true,
                }"
                :checkbox-config="{
                  showHeader: false,
                  visibleMethod: ({ row }) => {
                    return row.sequenceNbr;
                  },
                  checkMethod: ({ row }) => {
                    return row.quotaCode;
                  },
                }"
                :span-method="colspanMethod"
                :scroll-y="{ enabled: true, gt: 0, scrollToTopOnChange: true }"
                :tree-config="{
                  rowField: 'id',
                  parentField: 'parentId',
                  line: true,
                  showIcon: true,
                }"
                @cell-dblclick="cellDblClickQdzy"
                @checkbox-change="qdzyCheckChange">
                <vxe-column
                  align="left"
                  field="quotaCode"
                  type="checkbox"
                  tree-node
                  :width="columnWidth(100)"
                  title="选中"
                >
                  <template #default="{ row }">
                    <span v-if="!row.sequenceNbr" class="tree-title">{{ row.jobContent }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="quotaCode" :min-width="columnWidth(70)" title="定额号">
                </vxe-column>
                <vxe-column
                  field="quotaName"
                  align="left"
                  :width="columnWidth(250)"
                  title="定额名称"
                >
                  <template #default="{ row }">
                    <span v-if="row.sequenceNbr">{{ row.quotaName }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="unit" :width="columnWidth(80)" title="单位"> </vxe-column>
                <vxe-column field="price" title="单价"> </vxe-column>
                <template #empty>
                  <img src="@/assets/img/data-null.png" alt="" class="noData" />
                </template>
              </vxe-table>
            </div>
          </div>
        </div>
      </div>
    </common-modal>
    <common-modal
      v-model:model-value="unitVisible"
      title="单位换算系数"
      width="554px"
      class-name="dialog-comm">
      <div class="dialog-content">
        <div class="init-text">替换的资源和当前资源单位不同，是否继续替换？</div>
        <div class="init-text">
          单位换算系数&nbsp;
          <span>{{ originInfo.unit }}</span>
          <a-input
            v-model:value="conversionCoefficient"
            @keyup="
              conversionCoefficient = (conversionCoefficient.match(/\d{0,8}(\.\d{0,2}|100)?/) || [
                '',
              ])[0]
            " />
          <span>{{ currentInfo?.unit }}</span>
        </div>
      </div>
      <div class="footer-btn-list">
        <a-button @click="unitClose">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </div>
    </common-modal>

    <common-modal
      v-if="qdzyStatus"
      v-model:model-value="qdzyStatus"
      :title="qdzyDwtTitle"
      width="554px"
      destroy-on-close
      transfer
      class-name="dialog-comm">
      <div class="">
        <vxe-grid ref="qdzyDwRef" v-bind="gridOptions" v-on="gridEvents"></vxe-grid>
      </div>
      <div class="footer-btn-list">
        <a-button @click="qdzyStatus = false">取消</a-button>
        <a-button type="primary" @click="qdzyHandleOk">确定</a-button>
      </div>
    </common-modal>
  </div>
</template>

<script setup>
import csProject from '@/api/csProject';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { computed, nextTick, reactive, ref, shallowRef, toRaw, toRefs, watch } from 'vue';
import { useRoute } from 'vue-router';
import XEUtils from 'xe-utils';
import api from '../../../../api/projectDetail';
import { projectDetailStore } from '../../../../store/projectDetail';
import { stableHook } from '../subItemProject/stableHook.js';
import IndexSearch from './IndexSearch.vue';
import { columnWidth } from '@/hooks/useSystemConfig';

let treeHeight = ref(252); // 设置高度开启虚拟滚动，
const route = useRoute();
const value1 = ref('dingE');
const value2 = ref('list');
const props = defineProps([
  'indexVisible',
  'originInfo',
  'indexLoading',
  'defaultValue1',
  'checkBatchOption',
]);
const emits = defineEmits(['currentQdDeInfo', 'update:indexVisible', 'update:checkBatchOption']);
const store = projectDetailStore();
const path = require('path');
let qdzyStatus = ref(false);
let treeRef = ref();
const treeScrollTop = () => {
  console.log('跳转');
  if (selectedKeys.value.length && treeRef.value) {
    // 开启虚拟滚动才生效
    treeRef.value?.scrollTo({ key: selectedKeys.value[0], align: 'auto' });
  }
};
/**
 * 是否为空定额
 */
const isEmptyDE = computed(() => {
  const { bdCode, fxCode, kind, customParent } = props.originInfo || {};
  return kind === '04' && !bdCode && !fxCode;
});

/**
 * 清单索引当前行数据
 * 如果当前行是空定额，则需定位到对应父级清单的清单指引值
 */
const inventoryQuotaIndexOriginInfo = computed(() => {
  // return props.originInfo;
  const { customParent } = props.originInfo || {};
  if (isEmptyDE.value) {
    return customParent;
  }
  return props.originInfo;
});

const currentStandardId = computed(() => {
  // return props.originInfo?.standardId;
  if (isEmptyDE.value && emptyDEDefaultDEData.value) {
    return emptyDEDefaultDEData.value?.quotaId;
  } else {
    return props.originInfo?.standardId;
  }
});

const qdzyDwRef = ref(null);
let searchKey = ref();
let selectValue = ref();
let selectOptions = ref([]);
let expandedKeys = ref([]);
let queryForm = reactive({
  libraryCode: '',
  // classifyLevel0: '',
  classifyLevel1: '',
  classifyLevel2: '',
  classifyLevel3: '',
  classifyLevel4: '',
  bdName: '',
  page: 1,
  limit: 300000,
});

let qdzyDwtTitle = ref('清单定额索引'); //清单指引选择单位弹窗 + 清单插入多单位也用此弹窗
const gridOptions = reactive({
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    { field: 'dispNo', title: '序号' },
    { field: 'bdCode', title: '编码' },
    { field: 'bdName', title: '名称' },
    { field: 'unit', title: '单位' },
  ],
  data: [],
});

let { bdName } = { ...toRefs(queryForm) };

let renCJQueryForm = reactive({
  libraryCode: '',
  level1: '',
  level2: '',
  level3: '',
  level4: '',
  level5: '',
  level6: '',
  level7: '',
  materialName: '',
  startIndex: 1,
  pageNumber: 20,
});
const rcjCustomRows = record => {
  return {
    onDblclick: () => {
      singleInsertionDe(record);
    },
  };
};

const singleInsertionDe = row => {
  const handleCurrentData = {
    kind: '04',
    sequenceNbr: row.sequenceNbr,
    libraryCode: currentInfo.value.libraryCode,
    rcjFlag: 0,
    unit: row.unit,
  };
  emits('currentQdDeInfo', handleCurrentData);
};
const sTableCustomRows = record => {
  const { onClick } = customRow(record);
  return {
    onClick: e => {
      onClick(e);
      if (value1.value === 'renCJ') {
        getRcjData(record);
      }
    },
    onDblclick: e => {
      console.log('🚀 ~ e:', e);
      cellDBLClickEvent({ row: record });
    },
  };
};
let desHtml = reactive({ ossUrl: '' });
const selectedKeys = ref([]);
let currentTreeInfo = ref();
let treeData = shallowRef([]);
let currentInfo = ref();
// const showUnitTooltip = ref(false);
let vexTable = ref();
let sTableRef = ref();
let scrollSwitch = ref(false);
let dialogTitle = ref('清单定额索引');
let conversionCoefficient = ref('');
let unitVisible = ref(false);
let isSearch = ref(false); // 是否为点击搜索按钮查询数据
let selectUnit = ref(); // 多单位时选择单位
let optionType = ref(1); // 判断当前操作类型 1 插入 2 替换
let fieldnames = ref({
  label: 'libraryName',
  value: 'libraryCode',
});
let qdzyHandleType = ref(); // 插入的 类型，zm:'插入子目'， qd:'插入清单' th: '替换清单'
let qdzyPostData = shallowRef(); // 插入的 类型，zm:'插入子目'， qd:'插入清单' th: '替换清单'
// let isAddOrReplace = ref(false); //执行插入/替换操作不切换跳转

let tableData = shallowRef([]);
let sTableHeight = ref(300);
let sTableHeightBottom = ref(200);
let rightTotalHeight = ref(400);
let tabContentRef = ref();
const dragHeight = h => {
  sTableHeight.value = h - 32;
  sTableHeightBottom.value = rightTotalHeight.value - sTableHeight.value;
};
const setStableHeight = () => {
  console.log('🚀 ~ setStableHeight ~ tabContentRef:');
  if (!tabContentRef.value) return;
  const height = tabContentRef.value?.offsetHeight;
  rightTotalHeight.value = height - 32;
  if (value1.value === 'renCJ') {
    sTableHeight.value = rightTotalHeight.value / 2;
    sTableHeightBottom.value = rightTotalHeight.value - sTableHeight.value;
  } else {
    sTableHeight.value = rightTotalHeight.value;
  }
};

const showColumns = computed(() => {
  let list = [
    {
      title: '编码',
      dataIndex: 'bdCode',
      width: columnWidth(90),
      align: 'center',
      autoHeight: true,
    },
    {
      title: '名称',
      dataIndex: 'bdName',
      align: 'left',
      minWidth: columnWidth(200),
      autoHeight: true,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      align: 'center',
      width: columnWidth(50),
      autoHeight: true,
    },
  ];
  if (value1.value === 'renCJ') {
    list.splice(2, 0, {
      title: '规格型号',
      dataIndex: 'specification',
      width: columnWidth(100),
    });
  }
  if (value1.value === 'dingE') {
    list.push({
      title: '定额单价',
      dataIndex: 'price',
      align: 'right',
      width: columnWidth(70),
    });
  }
  if (store.deStandardReleaseYear === '12' && value1.value === 'renCJ') {
    list = [
      ...list,
      {
        title: '定额价',
        dataIndex: 'dePrice',
        align: 'right',
        width: columnWidth(60),
      },
      {
        title: '除税系数',
        dataIndex: 'taxRemoval',
        align: 'right',
        width: columnWidth(70),
      },
    ];
  }
  if (store.deStandardReleaseYear === '22' && value1.value === 'renCJ') {
    list.push({
      title: store.taxMade === 1 ? '不含税基期价' : '含税基期价',
      dataIndex: store.taxMade === 1 ? 'priceBaseJournal' : 'priceBaseJournalTax',
      align: 'right',
      width: columnWidth(80),
    });
  }
  return list;
});
let minHorizontalTop = ref(100);
let isContract = ref(true);
const toggleContract = () => {
  isContract.value = !isContract.value;
  minHorizontalTop.value = isContract.value ? 100 : 150;
  setTimeout(() => {
    sTableHeightBottom.value = isContract.value ? rightTotalHeight.value / 2 : 0;
    sTableHeight.value = !isContract.value
      ? rightTotalHeight.value - sTableHeightBottom.value
      : rightTotalHeight.value;
  }, 10);
};
const splitRatio = computed(() => {
  if (!isContract.value) {
    return '32/1';
  }
  return value1.value === 'renCJ' ? '4/3' : '1/1';
});
const rcjColumns = ref([
  { title: '编码', dataIndex: 'deCode', align: 'center', width: 130 },
  { title: '名称', dataIndex: 'deName', autoHeight: true },
  { title: '单位', dataIndex: 'unit', align: 'center', width: 110 },
  { title: '单价', dataIndex: 'price', align: 'center', width: 90 },
]);
let rcjTableData = ref([]);
const getRcjData = record => {
  const params = {
    constructId: route.query?.constructSequenceNbr,
    libraryCode: record.libraryCode,
    rcjCode: record.materialCode,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  console.log('🚀 ~ params:', params);
  api.queryDeByRcj(params).then(res => {
    console.log('🚀 ~ api.queryDeByRcj ~ res:', res);
    if (res.code === 200) {
      rcjTableData.value = res.result;
    }
  });
};

let selectState = reactive({
  selectedRowKeys: [],
});
let { cellMouseup, rowSelection, customRow } = stableHook(
  {
    selectState: selectState,
    stableRef: sTableRef,
    currentInfo: currentInfo,
    tableData: tableData,
    originalData: tableData,
  },
  'qddesy',
  msg => {}
);
watch(
  () => currentInfo.value,
  newVal => {
    if (newVal) {
      if (
        selectState.selectedRowKeys.includes(newVal.sequenceNbr) &&
        selectState.selectedRowKeys.length > 0
      ) {
        selectState.selectedRowKeys = Array.from(
          new Set([...selectState.selectedRowKeys, ...[newVal.sequenceNbr]])
        );
      } else {
        selectState.selectedRowKeys = [newVal.sequenceNbr];
      }
    }
  }
);
watch(
  () => [props.indexVisible, props.originInfo],
  ([newV, newI], [oldV, oldI]) => {
    if (
      !(
        (newV && oldV && value1.value === 'qdzy' && props.originInfo?.kind === '03') ||
        (newV && !oldV)
      )
    ) {
      return;
    }
    if (props.indexVisible) {
      queryForm = Object.assign(queryForm, {
        page: 1,
        limit: 300000,
      });
      // if (
      //   isAddOrReplace.value ||
      //   (value1.value === 'qingDan' && props.originInfo?.kind === '03')
      // ) {
      //   isAddOrReplace.value = false;
      //   return;
      // }
      fieldnames.value = {
        label: 'libraryName',
        value: 'libraryCode',
      };
      console.log(
        '🚀 ~ props.indexVisible:',
        props.originInfo,
        inventoryQuotaIndexOriginInfo.value
      );
      const { rcjFlag, kind } = inventoryQuotaIndexOriginInfo.value;
      value1.value = rcjFlag === 1 ? 'renCJ' : kind === '04' ? 'dingE' : 'qdzy';
      if (props.defaultValue1) {
        const map = {
          '04': 'dingE',
          '03': 'qingDan',
        };
        value1.value = map[props.defaultValue1] || value1.value;
      }
      if (value1.value === 'qdzy') {
        fieldnames.value = {
          label: 'name',
          value: 'releaseYear',
        };
        queryQdzy();
      } else if ((!props.defaultValue1 && kind === '04') || props.defaultValue1 === '04') {
        queryDeLibrary(value1.value === 'dingE');
      } else {
        queryQdLibrary();
      }
      // if (props.originInfo.standardId) {
      //   queryCurrentData();
      // }
      nextTick(() => {
        treeHeight.value = document.querySelector('.tree').offsetHeight;
        setStableHeight();
      });
    }
    if (!props.indexVisible) {
      // showUnitTooltip.value = false;
    }
  }
);

const isInsertDisabled = computed(() => {
  if (store.type === 'jieSuan') {
    if (store.stageCount) return true;
    if (store.currentTreeInfo.originalFlag && store.componentId === 'measuresItem') return true;
    if (
      value1.value !== 'qingDan' &&
      (props.originInfo?.lockPriceFlag || props.originInfo?.deLockPriceFlag || props.originalFlag)
    ) {
      // 结算 合同内锁定清单定额不能添加
      return true;
    }
  }
  if (store.standardGroupOpenInfo.isOpen && value1.value === 'qingDan') return true;
  const isData = selectState.selectedRowKeys.length > 0 || currentInfo.value;
  if (
    isData &&
    value1.value === 'qingDan' &&
    props.originInfo &&
    props.originInfo.optionMenu?.includes(2) &&
    !props.indexLoading
  ) {
    return false;
  } else if (
    isData &&
    value1.value !== 'qingDan' &&
    props.originInfo &&
    props.originInfo.optionMenu?.includes(3) &&
    !props.indexLoading
  ) {
    return false;
  }
  return true;
});

const isDisabled = computed(() => {
  if (store.type === 'jieSuan') {
    if (store.stageCount) return true;
    if (store.currentTreeInfo.originalFlag && store.componentId === 'measuresItem') return true;
    if (
      value1.value !== 'qingDan' &&
      (props.originInfo?.lockPriceFlag || props.originInfo?.deLockPriceFlag || props.originalFlag)
    ) {
      return true;
    }
  }
  if (store.standardGroupOpenInfo.isOpen && value1.value === 'qingDan') return true;
  const isOneData = selectState.selectedRowKeys.length === 1;
  if (
    isOneData &&
    value1.value === 'qingDan' &&
    props.originInfo?.kind === '03' &&
    ((props.originInfo.isSupplement === 0 && props.originInfo.standardId) ||
      props.originInfo.isSupplement === 1) &&
    !props.indexLoading &&
    !props.originInfo.isLocked
  ) {
    return false;
  } else if (
    isOneData &&
    value1.value === 'dingE' &&
    props.originInfo?.kind === '04' &&
    props.originInfo.rcjFlag === 0 &&
    ((props.originInfo.isSupplement === 0 && props.originInfo.standardId) ||
      props.originInfo.isSupplement === 1) &&
    !props.indexLoading
  ) {
    return false;
  } else if (
    isOneData &&
    value1.value === 'renCJ' &&
    props.originInfo?.kind === '04' &&
    props.originInfo.rcjFlag === 1 &&
    ((props.originInfo.isSupplement === 0 && props.originInfo.standardId) ||
      props.originInfo.isSupplement === 1) &&
    !props.indexLoading
  ) {
    return false;
  }
  return true;
});
const changeRadio = value => {
  value2.value = value;
  if (value1.value === 'dingE') {
    queryListByClassify();
  } else {
    likeQdByCodeOrName();
  }
};

const selectChildren = (selectedKeys, { node, event }) => {
  const oldcurrentTreeInfo = currentTreeInfo.value;
  currentTreeInfo.value = node;
  bdName.value = '';
  rcjTableData.value = [];
  setCurrentRowAndCheckbox(null);
  if (value1.value == 'qdzy') {
    if (oldcurrentTreeInfo?.sequenceNbr != currentTreeInfo.value?.sequenceNbr) {
      qdzyDeList.value = [];
      qdGuideDeList();
    }
    return;
  }

  if (value1.value === 'renCJ') {
    initRCJQuery();
  } else {
    initQuery();
  }
  let pathList = node.dataRef.path.split('/');
  queryForm.libraryCode = selectValue.value;
  renCJQueryForm.libraryCode = selectValue.value;
  pathList.forEach((item, index) => {
    if (index === 0 && value1.value === 'dingE') return;
    if (value1.value === 'qingDan') {
      queryForm[`classifyLevel${index + 1}`] = item;
    } else if (value1.value === 'dingE') {
      queryForm[`classifyLevel${index}`] = item;
    } else {
      renCJQueryForm[`level${index + 1}`] = item;
    }
  });
  currentInfo.value = null;
  console.log('===========', queryForm);
  if (value1.value === 'dingE') {
    queryListByClassify();
  } else if (value1.value === 'qingDan') {
    likeQdByCodeOrName();
  } else {
    isSearch.value = false;
    queryBaseRcjLikeName();
  }
};
const getChapterDate = () => {
  let current = currentInfo.value || currentTreeInfo.value;
  console.log(currentTreeInfo.value, 'currentTreeInfo.value', current);
  if (!current) {
    desHtml.ossUrl = '';
    return;
  }
  let apiData = {
    kind: value1.value === 'qingDan' ? '03' : '04',
    dataType: value2.value === 'des' ? 1 : value2.value === 'compute' ? 2 : 3,
    libraryCode: queryForm.libraryCode,
    selectedName: current?.name,
    classLevels: [
      current?.classifyLevel1,
      current?.classifyLevel2,
      current?.classifyLevel3,
      current?.classifyLevel4,
      current?.classifyLevel5,
      current?.classifyLevel6,
    ],
  };
  if (value1.value === 'qingDan') {
    const {
      bdCodeLevel01,
      bdCodeLevel02,
      bdCodeLevel03,
      bdCodeLevel04,
      bdNameLevel01,
      bdNameLevel02,
      bdNameLevel03,
      bdNameLevel04,
    } = current;
    apiData.bdCodeLevels = [bdCodeLevel01, bdCodeLevel02, bdCodeLevel03, bdCodeLevel04];
    apiData.bdNameLevels = [bdNameLevel01, bdNameLevel02, bdNameLevel03, bdNameLevel04];
  }
  console.log('章节说明参数', apiData);
  api.queryChapterDate(apiData).then(res => {
    console.log('🚀 ~ api.queryChapterDate ~ res:', res);
    if (res.status === 200) {
      desHtml.ossUrl = '';
      if (res.result.data) {
        const blob = new Blob([res.result.data], { type: 'text/html' });
        // 创建一个可访问 Blob 的 URL
        const url = URL.createObjectURL(blob);
        desHtml.ossUrl = url;
      }
    }
  });
};

const handleChange = value => {
  console.log('🚀 ~ handleChange ~ value:', value);
  queryForm.libraryCode = value;
  currentInfo.value = null;
  tableData.value = [];
  rcjTableData.value = [];

  switch (value1.value) {
    case 'dingE':
      deListByTree();
      break;
    case 'qingDan':
      queryQdList();
      break;
    case 'qdzy':
      qdzyGetTree();
      guideLibraryModel.value = selectOptions.value.find(item => item.releaseYear === value);
      break;
    default:
      getRcjTreeByCode();
      break;
  }
};

const onSearch = () => {
  tableData.value = [];
  if (value1.value === 'qdzy') {
    qdzyGetTree();
    return;
  }

  if (value1.value === 'renCJ') {
    initRCJQuery();
  } else {
    initQuery();
  }
  queryForm.libraryCode = selectValue.value;
  renCJQueryForm.libraryCode = selectValue.value;
  renCJQueryForm.materialName = queryForm.bdName;
  if (value1.value === 'dingE') {
    queryListByClassify();
  } else if (value1.value === 'qingDan') {
    likeQdByCodeOrName();
  } else {
    isSearch.value = true;
    queryAllBaseRcjLikeName();
  }
};

const initQuery = () => {
  scrollBeforeTop = 0;
  queryForm = Object.assign(queryForm, {
    // bdName: '',
    page: 1,
    limit: 300000,
  });
  let length = Object.keys(queryForm).length - 4;
  for (let i = 1; i <= length; i++) {
    queryForm[`classifyLevel${i}`] = '';
  }
  console.log('initQuery', queryForm);
  qdzyDeList.value = [];
};

const initRCJQuery = () => {
  scrollBeforeTop = 0;
  renCJQueryForm = Object.assign(renCJQueryForm, {
    level1: '',
    level2: '',
    level3: '',
    level4: '',
    level5: '',
    level6: '',
    level7: '',
    startIndex: 1,
    materialName: '',
  });
};

const queryQdLibrary = () => {
  let apiData = {
    qdStandardId: store.constructConfigInfo.qdStandardId,
  };
  api.queryQdLibrary(store.constructConfigInfo.qdStandardId).then(res => {
    console.log('res清单册', res);
    if (res.result) {
      selectOptions.value = res.result;
      selectValue.value = res.result[0].libraryCode;
      if (props.originInfo.standardId && props.originInfo.kind === '03') {
        queryQdById();
      } else {
        queryQdList();
      }
    }
  });
};

// 清单指引
let guideLibraryModel = ref();
const queryQdzy = async () => {
  const res = await csProject.listGuideLibrary({
    constructId: route.query?.constructSequenceNbr,
  });
  const deType = store.deStandardReleaseYear;
  console.log('🚀 清单指引', res, deType);
  selectOptions.value = res.result.filter(x => x.releaseYear === Number(deType));
  expandedKeys.value = [];
  selectedKeys.value = [];
  const selectData = res.result.find(item => item.releaseYear === Number(deType));
  selectValue.value = selectData.releaseYear;
  guideLibraryModel.value = selectData;

  queryDeZyById();
  // qdzyGetTree()
};

let qdzyDeList = shallowRef([]);

const isQdzyCrZm = computed(() => {
  if (jieSuanIsQdzyCrZm()) return true;
  return !qdzyDeList.value.length || [0].includes(+props.originInfo?.kind);
});

const jieSuanIsQdzyCrZm = () => {
  if (store.type === 'jieSuan') {
    if (props.originInfo?.lockPriceFlag || props.originInfo?.deLockPriceFlag) {
      return true;
    }
    if (props.originInfo?.originalFlag) return true;
    if (store.stageCount) return true;
    if (store.currentTreeInfo.originalFlag && store.componentId === 'measuresItem') return true;
  }
  return false;
};

const isQdzyCrQd = computed(() => {
  if (store.type === 'jieSuan') {
    if (store.stageCount) return true;
    if (store.currentTreeInfo.originalFlag && store.componentId === 'measuresItem') return true;
  }
  if (store.standardGroupOpenInfo.isOpen) return true;
  if (currentTreeInfo.value?.bdCodeLevel04 && props.originInfo.optionMenu?.includes(2)) {
    return false;
  } else {
    return true;
  }
});

const isQdzyTh = computed(() => {
  if (store.standardGroupOpenInfo.isOpen) return true;
  if (store.stageCount) return true; //分期不能操作
  // 沿用以前插入，增加了左侧当前树是清单层级
  if (
    currentTreeInfo.value?.bdCodeLevel04 &&
    props.originInfo.kind === '03' &&
    ((props.originInfo.isSupplement === 0 && props.originInfo.standardId) ||
      props.originInfo.isSupplement === 1) &&
    !props.indexLoading &&
    !props.originInfo.isLocked &&
    !props.originInfo?.originalFlag
  ) {
    return false;
  }

  return true;
});
let otherCheckList = ref([]);
const otherCheckChange = ({ row }) => {
  const list = vexTable.value.getCheckboxRecords(true);
  currentInfo.value = row;
  otherCheckList.value = XEUtils.clone(list, true);
};

// 清单指引选择列表
const qdzyCheckChange = ({}) => {
  let handleData = [];
  const list = vexTable.value.getCheckboxRecords(true);
  list.forEach(item => {
    if (item.children.length === 0) {
      handleData.push({
        ...toRaw(item),
      });
    }
  });
  qdzyDeList.value = handleData;
};

// 清单指引获取树
const qdzyGetTree = async (type = false) => {
  let postData = {
    qdCodeOrName: bdName.value,
    libraryCode: guideLibraryModel.value.qdLibraryCode,
  };
  const res = await csProject.qdLevelTree(postData);
  console.log('🚀清单指引获取树', res, postData);
  qdzyDeList.value = [];
  qdzyTableData.value = [];
  treeData.value = res.result.map(i => {
    if (!i.bdName) {
      i.bdName = i.bdNameLevel04;
    }
    return i;
  });

  if (type) {
    nextTick(() => {
      const expandedKey = findParentIds(treeData.value, currentTreeInfo.value?.sequenceNbr);
      expandedKeys.value = expandedKey;
      console.log('🚀 ~ nextTick ~ expandedKeys.value:', expandedKeys.value);
      selectedKeys.value = expandedKey.length ? [currentTreeInfo.value.sequenceNbr] : [];
    });
  } else {
    res.result.forEach(item => {
      if (item.bdName === store.currentTreeInfo.constructMajorType) {
        queryForm.classifyLevel1 = item.bdName;
        selectedKeys.value = [item.sequenceNbr];
        expandedKeys.value = [item.sequenceNbr];
        currentTreeInfo.value = item;
        item.childrenList.forEach(child => {
          if (child.bdName === store.currentTreeInfo.secondInstallationProjectName) {
            queryForm.classifyLevel2 = child.bdName;
            selectedKeys.value = [child.sequenceNbr];
          }
        });
      }
    });
  }

  if (res.result?.length) {
    qdGuideDeList();
  } else {
    qdzyTableData.value = [];
  }
  setTimeout(() => {
    treeScrollTop();
  }, 500);
};

let qdzyTableData = ref([]);
// 获取左侧清单指引列表数据
const qdGuideDeList = async () => {
  const guide = { ...toRaw(guideLibraryModel.value) };
  delete guide.dataRef;
  delete guide.parent;

  let postData = {
    guideLibraryModel: { ...guide },
    baseListModel: { ...toRaw(currentTreeInfo.value) },
  };
  if (!postData || !currentTreeInfo.value) {
    qdzyTableData.value = [];
    return;
  }
  const res = await csProject.qdGuideDeList(postData);
  const list = addIdAndParentId(res.result, 1);
  console.log('🚀 ~ qdGuideDeList ~ list:', list);
  qdzyTableData.value = list;
  setDefaultDingEByQDZYFirst(list);
  nextTick(() => {
    vexTable.value.setAllTreeExpand(true);
  });
};

// 空定额默认定额数据
let emptyDEDefaultDEData = ref();
/**
 * 空定额特殊处理
 * 从清单指引跳转定额时
 * 根据清单指引第一条定额设置默认定额章节
 */
const setDefaultDingEByQDZYFirst = (qdzyList = []) => {
  if (!isEmptyDE.value) return;
  for (let child of qdzyList) {
    for (let item of child.children) {
      if (item.quotaCode && item.quotaName) {
        emptyDEDefaultDEData.value = item;
        console.log(
          '🚀 ~ setDefaultDingEByQDZYFirst ~ emptyDEDefaultDEData.value:',
          emptyDEDefaultDEData.value
        );
        return;
      }
    }
  }
  console.log(
    '🚀 ~ setDefaultDingEByQDZYFirst ~ emptyDEDefaultDEData.value:',
    emptyDEDefaultDEData.value
  );
};

const addIdAndParentId = (data, parentId = null) => {
  return data.map((node, index) => ({
    id: `${parentId ? parentId + '-' : ''}${index + 1}`, // 构造唯一的id
    parentId,
    ...node,
    children:
      node.children && node.children.length > 0
        ? addIdAndParentId(node.children, `${parentId ? parentId + '-' : ''}${index + 1}`)
        : [],
  }));
};

/**
 * isDeDw 是否定额定位
 */
const queryDeLibrary = (isDeDw = false) => {
  console.log('store.currentTreeInfo', store.constructConfigInfo, store.currentTreeInfo);
  let apiData = {
    deStandardId: store.currentTreeInfo.deStandardId,
  };
  api.queryDeLibrary(store.currentTreeInfo.deStandardId).then(res => {
    console.log('🚀 ~ api.queryDeLibrary ~ res:', res);
    if (res.result) {
      selectOptions.value = res.result;
      if (value1.value === 'dingE' && isEmptyDE.value && emptyDEDefaultDEData.value) {
        // 切换到定额，并且是空定额
        queryDeById();
        return;
      }
      // debugger;
      if (
        props.originInfo.standardId &&
        props.originInfo.kind === '04' &&
        props.originInfo.rcjFlag === 0 &&
        props.originInfo.isCostDe !== 1 &&
        value1.value === 'dingE'
      ) {
        console.log('🚀 ~ api.queryDeLibrary ~ queryDeById:');
        queryDeById();
      } else if (
        props.originInfo.standardId &&
        props.originInfo.kind === '04' &&
        value1.value === 'renCJ' &&
        props.originInfo.rcjFlag === 1
      ) {
        queryRcjById();
      } else {
        selectValue.value = store.currentTreeInfo.libraryCode;
        if (value1.value === 'dingE') {
          deListByTree();
        } else {
          getRcjTreeByCode();
        }
      }
    }
  });
};

const queryQdList = () => {
  api.queryQdListTree(selectValue.value).then(res => {
    if (res.result) {
      console.log('🚀 ~ api.queryQdListTree ~ res:', JSON.parse(JSON.stringify(res.result)));
      treeData.value = getQdInitData(res.result);
      console.log('🚀 ~ api.queryQdListTree ~ res:', treeData.value);
      if (!currentStandardId.value && value1.value === 'qingDan') {
        res.result.forEach(item => {
          if (item.bdName === store.currentTreeInfo.constructMajorType) {
            queryForm.classifyLevel1 = item.bdName;
            selectedKeys.value = [item.sequenceNbr];
            expandedKeys.value = [item.sequenceNbr];
            item.childrenList.forEach(child => {
              if (child.bdName === store.currentTreeInfo.secondInstallationProjectName) {
                queryForm.classifyLevel2 = child.bdName;
                selectedKeys.value = [child.sequenceNbr];
                console.log('这儿进来不', expandedKeys.value);
              }
            });
            nextTick(() => {
              treeScrollTop();
            });
          }
        });
      } else {
        nextTick(() => {
          findQDAndLevel(treeData.value, currentInfo.value);
        });
      }
      if (props.originInfo.kind !== '03' && value1.value === 'qingDan') {
        queryForm.libraryCode = selectValue.value;
      }

      let likeQdByCodeTimer = setTimeout(() => {
        likeQdByCodeOrName();
      }, 100);
    }
  });
};

const deListByTree = () => {
  console.log('00000000000', selectValue.value);
  api
    .deListByTree({
      code: selectValue.value,
      deStandardReleaseYear: store?.deStandardReleaseYear,
    })
    .then(res => {
      console.log('定额数据机构', res);
      if (res.result) {
        treeData.value = getDeInitData([res.result]);
        const { standardId, kind, rcjFlag, isCostDe } = props.originInfo;
        if (
          standardId &&
          kind === '04' &&
          rcjFlag === 0 &&
          isCostDe !== 1 &&
          value1.value === 'dingE' &&
          currentInfo.value
        ) {
          // 原来的判断基础上添加空定额的时候也进来
          findDeAndLevel(treeData.value, currentInfo.value);
        } else if (isEmptyDE.value && value1.value === 'dingE' && currentInfo.value) {
          console.log('原来的判断基础上添加空定额的时候也进来');
          findDeAndLevel(treeData.value, currentInfo.value);
        } else {
          selectedKeys.value = [treeData.value[0].key];
          queryForm.libraryCode = selectValue.value;
          treeData.value.forEach(item => {
            queryForm.classifyLevel0 = item.name;
            selectedKeys.value = [item.key];
            expandedKeys.value = [item.key];
            item.childrenList.forEach(child => {
              if (
                child.name.includes(store.currentTreeInfo.secondInstallationProjectName) ||
                child.name.includes(store.currentTreeInfo.majorName)
              ) {
                queryForm.classifyLevel1 = child.name;
                selectedKeys.value = [child.key];
                expandedKeys.value.push(child.key);
              }
            });
          });
        }
        console.log(selectedKeys.value, 'selectedKeys.value');
        nextTick(() => {
          treeScrollTop();
        });
        queryListByClassify();
      }
    });
};

const getRcjTreeByCode = () => {
  const params = {
    constructId: route.query?.constructSequenceNbr,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
    libraryCode: selectValue.value,
  };
  api.getRcjTreeByCode(params).then(res => {
    if (res.status === 200) {
      treeData.value = getRCJInitData(res.result);
      console.log('rcjTreeByCode-人材机左侧树数据：', treeData.value);
      if (props.originInfo.rcjFlag !== 1) {
        const first = res.result[0] || {};
        selectedKeys.value = [first.key];
        renCJQueryForm.libraryCode = selectValue.value;
        renCJQueryForm.level1 = first.path; // 如果是多级别，需分割处理
      } else {
        findRCJAndLevel(treeData.value, currentInfo.value);
      }
      nextTick(() => {
        treeScrollTop();
      });
      queryBaseRcjLikeName();
    }
  });
};

let scrollBeforeTop = 0;
const getScroll = type => {
  if (value1.value === 'qingDan') return;
  if (Math.ceil(type.scrollTop + type.$event.target.clientHeight) >= type.scrollHeight) {
    if (scrollSwitch.value) {
      const { scrollTop } = vexTable.value.getScroll();
      scrollBeforeTop = scrollTop;
      if (value1.value === 'dingE') {
        console.log('不进来么定额分页');
        queryForm.page++;
        queryListByClassify();
      } else {
        renCJQueryForm.startIndex++;
        if (isSearch.value) {
          queryAllBaseRcjLikeName();
        } else {
          queryBaseRcjLikeName();
        }
      }
    }
  }
};

const likeQdByCodeOrName = () => {
  if (value2.value === 'list') {
    queryForm.bdName = bdName.value;
    api.likeQdByCodeOrName(JSON.parse(JSON.stringify(queryForm))).then(res => {
      if (res.result) {
        console.log('🚀 ~ api.likeQdByCodeOrName ~ res.result:', res.result);
        tableData.value = res.result;
        if (currentStandardId.value) {
          let currentObj = tableData.value.filter(
            x =>
              x.bdName === currentInfo.value?.bdNameLevel04 &&
              x.bdCode === currentInfo.value?.bdCodeLevel04
          )[0];
          // vexTable.value.setCurrentRow(currentObj);
          setCurrentRowAndCheckbox(currentObj);
        } else {
          let obj = tableData.value.filter(
            x => x.sequenceNbr === currentInfo.value?.sequenceNbr
          )[0];
          if (!obj) {
            setCurrentRowAndCheckbox(null);
            // currentInfo.value = null;
            // vexTable.value.clearCurrentRow();
          }
        }
      }
    });
  } else if (value2.value === 'des') {
    // desHtml.ossUrl = '';
    getChapterDate();
  }
};
const setCurrentRowAndCheckbox = row => {
  if (row) {
    currentInfo.value = row;
    otherCheckList.value = [row];
    vexTable.value?.setCurrentRow(currentObj);
    getRcjData(row);
  } else {
    sTableRef.value?.clearAllSelectedRange();
    vexTable.value?.clearCurrentRow();
    currentInfo.value = null;
    otherCheckList.value = [];
    selectState.selectedRowKeys = [];
  }
};
const queryListByClassify = () => {
  if (value2.value === 'list') {
    queryForm.bdName = bdName.value;
    let postData = {
      ...toRaw(queryForm),
    };
    if (store.currentTreeInfo) {
      postData.constructId = route.query?.constructSequenceNbr;
      postData.spId = store.currentTreeInfo?.parentId;
      postData.upId = store.currentTreeInfo.id;
    }

    api.queryListByClassify(postData).then(res => {
      console.log('定额列表数据', res);
      if (res.status === 200) {
        if (postData.page === 1) {
          tableData.value = [];
        }
        let tempList = res.result.data.map(item => {
          return {
            ...item,
            bdName: item.deName,
            bdCode: item.deCode,
          };
        });
        tableData.value = tableData.value.concat(tempList);
        const standardId = currentStandardId.value;
        if (standardId) {
          let currentObj = tableData.value.filter(
            x => x.bdName === currentInfo.value?.deName && x.bdCode === currentInfo.value?.deCode
          )[0];
          setCurrentRowAndCheckbox(currentObj);
        } else {
          let obj = tableData.value.filter(
            x => x.sequenceNbr === currentInfo.value?.sequenceNbr
          )[0];
          if (!obj) {
            setCurrentRowAndCheckbox(null);
          }
        }
        setScrollTop();
        if (Math.ceil(res.result.total / queryForm.limit) > queryForm.page) {
          scrollSwitch.value = true;
        } else {
          scrollSwitch.value = false;
        }
      }
    });
  } else if (value2.value !== 'list') {
    getChapterDate();
  }
};

const queryBaseRcjLikeName = () => {
  scrollSwitch.value = false;
  renCJQueryForm.materialName = bdName.value;
  let apiData = {
    baseRcj: JSON.parse(JSON.stringify(renCJQueryForm)),
    page: renCJQueryForm.startIndex,
    limit: renCJQueryForm.pageNumber,
    constructId: route.query?.constructSequenceNbr,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  api.queryBaseRcjLikeName(apiData).then(res => {
    if (res.status === 200) {
      if (renCJQueryForm.startIndex === 1) {
        tableData.value = [];
      }
      let tempList = res.result.list.map(item => {
        return {
          ...item,
          bdName: item.materialName,
          bdCode: item.materialCode,
        };
      });
      tableData.value = tableData.value.concat(tempList);
      if (currentStandardId.value) {
        let currentObj = tableData.value.filter(
          x =>
            x.bdName === currentInfo.value?.materialName &&
            x.bdCode === currentInfo.value?.materialCode
        )[0];
        // vexTable.value.setCurrentRow(currentObj);
        setCurrentRowAndCheckbox(currentObj);
      } else {
        let obj = tableData.value.filter(x => x.sequenceNbr === currentInfo.value?.sequenceNbr)[0];
        if (!obj) {
          setCurrentRowAndCheckbox(null);
          // currentInfo.value = null;
          // vexTable.value.clearCurrentRow();
        }
      }
      setScrollTop();
      if (Math.ceil(res.result.total / renCJQueryForm.pageNumber) > renCJQueryForm.startIndex) {
        scrollSwitch.value = true;
      } else {
        scrollSwitch.value = false;
      }
    }
  });
};
// 设置滚动条位置
const setScrollTop = () => {
  if (!currentInfo.value) return;
  setTimeout(() => {
    console.log('滚动到', currentInfo.value);
    sTableRef.value?.scrollTo({ rowKey: currentInfo.value?.sequenceNbr });
  }, 10);
};
const queryAllBaseRcjLikeName = () => {
  scrollSwitch.value = false;
  renCJQueryForm.materialName = bdName.value;
  let apiData = {
    baseRcj: JSON.parse(JSON.stringify(renCJQueryForm)),
    page: renCJQueryForm.startIndex,
    limit: renCJQueryForm.pageNumber,
    constructId: route.query?.constructSequenceNbr,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  api.queryAllBaseRcjLikeName(apiData).then(res => {
    console.log('搜索查询接口');
    if (res.status === 200) {
      if (renCJQueryForm.startIndex === 1) {
        tableData.value = [];
      }
      let tempList = res.result.list.map(item => {
        return {
          ...item,
          bdName: item.materialName,
          bdCode: item.materialCode,
        };
      });
      tableData.value = tableData.value.concat(tempList);
      let obj = tableData.value.filter(x => x.sequenceNbr === currentInfo.value?.sequenceNbr)[0];
      if (!obj) {
        setCurrentRowAndCheckbox(null);
      }
      setScrollTop();
      if (Math.ceil(res.result.total / renCJQueryForm.pageNumber) > renCJQueryForm.startIndex) {
        scrollSwitch.value = true;
      } else {
        scrollSwitch.value = false;
      }
    }
  });
};

const getQdInitData = tree => {
  return tree.map(item => {
    item.key = item.bdName + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.bdName;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getQdInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.bdName + Math.ceil(Math.random() * 10000 + 1),
            path: (item.path ? item.path : item.bdName) + '/' + n.bdName,
          }))
        )
      : null;
    return item;
  });
};

const getDeInitData = tree => {
  return tree.map(item => {
    item.key = item.name + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.name;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getDeInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.name + Math.ceil(Math.random() * 10000 + 1),
            path: (item.path ? item.path : item.name) + '/' + n.name,
          }))
        )
      : null;
    return item;
  });
};

const getRCJInitData = tree => {
  return tree.map(item => {
    item.key = item.materialName + Math.ceil(Math.random() * 10000 + 1);
    item.path = item.path ? item.path : item.materialName;
    if (!item.path || item.path.split('/').length < 2) {
      expandedKeys.value.push(item.key);
    }
    item.childrenList = item.childrenList?.length
      ? getRCJInitData(
          item.childrenList.map(n => ({
            ...n,
            key: n.materialName + Math.ceil(Math.random() * 10000 + 1),
            path: (item.path ? item.path : item.materialName) + '/' + n.materialName,
          }))
        )
      : null;
    return item;
  });
};

// 反向查找清单指定数据并展开选中
const findQDAndLevel = (tree, targetData, level = 1) => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (
      targetData &&
      targetData[`bdCodeLevel0${level}`] &&
      node.bdCode === targetData[`bdCodeLevel0${level}`]
    ) {
      expandedKeys.value.push(node.sequenceNbr);
      selectedKeys.value = [node.sequenceNbr];
    }

    if (node.childrenList) {
      const result = findQDAndLevel(node.childrenList, currentInfo.value, level + 1);
      if (result) {
        return result;
      }
    }
  }

  return null;
};

const findParentIds = (tree, sequenceNbr) => {
  let result = [];
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].sequenceNbr === sequenceNbr) {
      result.push(tree[i].sequenceNbr);
      break;
    }
    if (tree[i].childrenList) {
      result = result.concat(findParentIds(tree[i].childrenList, sequenceNbr));
      if (result.length > 0) {
        result.push(tree[i].sequenceNbr);
        break;
      }
    }
  }
  return result;
};

// 反向查找定额指定数据并展开选中
const findDeAndLevel = (tree, targetData, level = 1) => {
  let isNext = level === 1 ? true : false; // 在原来基础上添加，如果当前数据符合的时候，在对其childrenList进行轮训继续查找，否则跳出，避免当前层级已经查找还继续查找当前层级的其他childrenList
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (
      targetData &&
      targetData[`classifyLevel${level}`] &&
      node.name === targetData[`classifyLevel${level}`]
    ) {
      console.log('反向定位数据', node.key, 'level', level);
      expandedKeys.value.push(node.key);
      selectedKeys.value = [node.key];
      level++;
      isNext = true;
    }

    if (node.childrenList && isNext) {
      isNext = false;
      const result = findDeAndLevel(node.childrenList, currentInfo.value, level);
      if (result) {
        console.log('反向定位数据', result);
        return result;
      }
    }
  }

  return null;
};

// 反向查找人材机指定数据并展开选中
const findRCJAndLevel = (tree, targetData, level = 1) => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (
      targetData &&
      targetData[`level${level}`] &&
      node.materialName === targetData[`level${level}`]
    ) {
      expandedKeys.value.push(node.key);
      selectedKeys.value = [node.key];
    }
    if (node.childrenList) {
      const result = findRCJAndLevel(node.childrenList, currentInfo.value, level + 1);
      if (result) {
        console.log('反向定位数据', result);
        return result;
      }
    }
  }

  return null;
};

const colspanMethod = ({ row, _rowIndex, columnIndex }) => {
  if (row.parentId == 1) {
    if (columnIndex === 0) {
      return { rowspan: 1, colspan: 5 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
};

let likeQdByCodeTimer = ref(null);
const radioChange = XEUtils.debounce(e => {
  bdName.value = null;
  treeData.value = [];
  rcjTableData.value = [];
  value2.value = 'list';
  setCurrentRowAndCheckbox(null);
  initQuery();
  let fieldNames = {
    label: 'libraryName',
    value: 'libraryCode',
  };
  // bdName.value = '';
  // showUnitTooltip.value = false;
  clearTimeout(likeQdByCodeTimer.value);
  likeQdByCodeTimer.value = null;

  switch (e.target.value) {
    case 'qingDan':
      setStableHeight();
      queryQdLibrary();
      break;
    case 'qdzy':
      queryQdzy();
      fieldNames = {
        label: 'name',
        value: 'releaseYear',
      };
      break;
    default:
      setStableHeight();
      queryDeLibrary();
      break;
  }
  fieldnames.value = fieldNames;
}, 200);

// 列表配置虚拟滚动，不影响原有逻辑，所以enabled动态
const tableScrollY = computed(() => {
  return {
    scrollToTopOnChange: true,
    enabled: value1.value === 'qingDan' ? true : false,
  };
});

// 选中单条清单定额数据
const currentChangeEvent = ({ row }) => {
  currentInfo.value = row;
  // if (showUnitTooltip.value) {
  //   showUnitTooltip.value = false;
  // }
};

const cellDBLClickEvent = async ({ row }) => {
  console.log(value1.value, row);
  if (value1.value === 'qingDan' && props.originInfo.isLocked) return;
  if (value1.value === 'qdzy') return;

  // currentInfo.value = row;
  if (!isInsertDisabled.value && !props.indexLoading) {
    updateCurrentInfo(1, '', [], true);
  }
};

// 清单指引点击保存
const qdzyHandleOk = () => {
  const data = qdzyDwRef.value.getCurrentRecord();
  if (!data?.unit) {
    message.error('请先选择单位');
    return;
  }
  if (value1.value === 'qingDan') {
    const obj = {
      ...currentInfo.value,
      unit: data.unit,
    };
    gridOptions.data = [];
    qdzyStatus.value = false;
    if (optionType.value === 1) {
      emits('currentQdDeInfo', obj);
    } else {
      emits('currentInfoReplace', obj);
    }
    return;
  }
  if (value1.value === 'qdzy') {
    currentTreeInfo.value.unit = data.unit;
    qdzyHandlePostData();
  }
};

const qdzyHandlePostData = () => {
  let postData = {
    ...qdzyPostData.value,
    unit: currentTreeInfo.value.unit,
  };
  console.log('🚀 ~ qdzyHandlePostData ~ postData:', postData);

  if (qdzyHandleType.value === 'th') {
    emits('currentInfoReplace', {
      qdzyReplace: true,
      ...postData,
    });
  } else {
    emits('currentQdDeInfo', { ...postData });
  }

  gridOptions.data = [];
  qdzyStatus.value = false;
};
watch(
  () => props.checkBatchOption,
  (val, old) => {
    if (val && !old) {
      // 监听清单是否在批量插入状态，如果是则执行批量插入
      qingDanBatchInsert();
    }
  },
  {
    deep: true,
  }
);
const qingDanBatchInsert = () => {
  if (otherCheckList.value.length === 0) return;
  let optionRow = {
    ...otherCheckList.value[0],
    isBatchInsert: otherCheckList.value.length > 1,
  };
  const unit = Array.isArray(optionRow.unitList)
    ? optionRow.unitList
    : optionRow.unitList?.split('/');
  optionRow.unitList = unit;
  emits('update:checkBatchOption', false);
  if (unit && unit.length > 1) {
    otherCheckList.value.shift();
    openMultiUnitDialog(optionRow, unit);
  } else {
    // 标准组价清单定额索引弹框不可以插入清单;
    if (store.standardGroupOpenInfo.isOpen && value1.value === 'qingDan') return;
    otherCheckList.value.shift();
    emits('currentQdDeInfo', optionRow);
  }
};
/**
 *
 * @param {*} type 调用接口的类型
 * @param {*} status 插入的 类型，zm:'插入子目'， qd:'插入清单' th: '替换清单'
 * qdzyDeData 清单指引，双击定额，闯进来的
 * isDblClick 是否是双击，非清单指引表格中使用到

 */
const updateCurrentInfo = (type, status = '', qdzyDeData = [], isDblClick = false) => {
  otherCheckList.value = tableData.value.filter(item =>
    selectState.selectedRowKeys.includes(item.sequenceNbr)
  );
  console.log('🚀 ~ otherCheckList.value:', otherCheckList.value);
  if (!currentInfo.value && otherCheckList.value.length) {
    currentInfo.value = otherCheckList.value[otherCheckList.value.length - 1];
  }
  optionType.value = type;
  // isAddOrReplace.value = true;
  if (value1.value === 'qingDan') {
    currentInfo.value.kind = '03';
  } else if (value1.value !== 'qdzy') {
    currentInfo.value.kind = '04';
    if (value1.value === 'dingE') {
      currentInfo.value.rcjFlag = 0;
    } else {
      currentInfo.value.rcjFlag = 1;
    }
  }
  if (type === 2 && value1.value === 'renCJ') {
    if (currentInfo.value.unit !== props.originInfo.unit) {
      unitVisible.value = true;
      return;
    }
  }

  // 清单指引替换
  if (value1.value === 'qdzy') {
    qdzyHandleType.value = status;
    const unitList = currentTreeInfo.value.unit?.split('/');
    let handleQdzyCurrentData = { ...currentInfo.value };

    // 清单指引插入
    handleQdzyCurrentData.deArray = [];

    if (status === 'zm') {
      handleQdzyCurrentData.kind = '04';
    }

    if (['qd', 'th'].includes(status)) {
      handleQdzyCurrentData.baseListModel = {
        ...toRaw(currentTreeInfo.value),
      };
    }

    let QDZYDELIST = qdzyDeData.length ? qdzyDeData : qdzyDeList.value;
    QDZYDELIST.forEach(item => {
      if (item.parentId !== 1) {
        handleQdzyCurrentData.deArray.push({
          ...toRaw(item),
        });
      }
    });

    qdzyPostData.value = handleQdzyCurrentData;
    console.log(
      '🚀 ~ updateCurrentInfo ~ handleQdzyCurrentData:',
      handleQdzyCurrentData,
      currentTreeInfo.value
    );

    if (unitList?.length > 1 && status !== 'zm') {
      //清单指引，当前行多个单位， 选择单位
      openMultiUnitDialog(currentTreeInfo.value, unitList);
      return;
    }

    qdzyHandlePostData();
    return;
  }
  if (!isDblClick && otherCheckList.value.length) {
    otherCheckList.value = otherCheckList.value.map(item => {
      item.kind = currentInfo.value.kind;
      item.rcjFlag = value1.value === 'renCJ' ? 1 : 0;
      if (['dingE'].includes(value1.value)) {
        item.quotaId = item.sequenceNbr;
      }
      return toRaw(item);
    });
  }
  if (value1.value !== 'qingDan') {
    if (type === 1) {
      let handleCurrentData = { ...currentInfo.value };
      if (!isDblClick && otherCheckList.value.length && value1.value === 'dingE') {
        // 定额批量插入
        handleCurrentData = {
          kind: '04',
          deArray: toRaw(otherCheckList.value),
          unit: props.originInfo.unit,
        };
      }
      if (!isDblClick && ['renCJ'].includes(value1.value) && otherCheckList.value.length) {
        handleCurrentData = {
          ...otherCheckList.value[0],
          isBatchInsert: otherCheckList.value.length > 1,
        };
        // 批量插入，清除队列中当前插入的数据
        otherCheckList.value.shift();
      }
      emits('currentQdDeInfo', handleCurrentData);
    } else {
      emits('currentInfoReplace', currentInfo.value);
    }
    // allInit();
    return;
  }
  // 清单指引插入
  let optionRow = currentInfo.value;
  if (!isDblClick && otherCheckList.value.length > 0) {
    optionRow = {
      ...otherCheckList.value[0],
      isBatchInsert: otherCheckList.value.length > 1,
    };
  }
  const unit = Array.isArray(optionRow.unitList)
    ? optionRow.unitList
    : optionRow.unitList?.split('/');
  optionRow.unitList = unit;
  if (unit && unit.length > 1) {
    // showUnitTooltip.value = true;
    if (!isDblClick) {
      // 批量插入，清除队列中当前插入的数据
      otherCheckList.value.shift();
    }
    openMultiUnitDialog(optionRow, unit);
  } else {
    if (type === 1) {
      // 标准组价清单定额索引弹框不可以插入清单;
      if (store.standardGroupOpenInfo.isOpen && value1.value === 'qingDan') return;
      if (!isDblClick) {
        // 批量插入，清除队列中当前插入的数据
        otherCheckList.value.shift();
      }
      emits('currentQdDeInfo', optionRow);
    } else {
      emits('currentInfoReplace', optionRow);
    }
    // allInit();
  }
};

const openMultiUnitDialog = (qdRow, unitList) => {
  let dwList = [];
  const { bdCode, bdName, bdNameLevel04, bdCodeLevel04 } = qdRow;
  const code = bdCode || bdCodeLevel04;
  const name = bdName || bdNameLevel04;
  qdzyDwtTitle.value = `选择清单单位[${code}]${name}`;
  unitList.forEach((item, k) => {
    dwList.push({
      dispNo: k + 1,
      bdCode: code,
      bdName: name,
      unit: item,
    });
  });
  currentInfo.value = qdRow;
  gridOptions.data = dwList;
  qdzyStatus.value = true;
};

const handleDbClickQdzy = event => {
  event.preventDefault();
  if (!isQdzyCrQd.value) {
    updateCurrentInfo(1, 'qd');
  }
};

const cellDblClickQdzy = ({ row }) => {
  if (jieSuanIsQdzyCrZm()) return;
  if (![0].includes(+props.originInfo?.kind) && row?.quotaCode) {
    // delete row.children;
    updateCurrentInfo(1, 'zm', [{ ...row, children: toRaw(row.children) }]);
  }
};
const onResize = XEUtils.debounce(() => {
  setStableHeight();
}, 500);

const close = () => {
  allInit();
  emits('update:indexVisible', false);
};
const unitChange = (dataRef, e) => {
  dataRef.unit = e.target.value;
};

// const selectHandler = dataRef => {
//   if (!selectUnit.value) {
//     return message.warning('请选择单位');
//   }
//   showUnitTooltip.value = false;
//   const obj = {
//     ...dataRef,
//     unit: selectUnit.value,
//   };
//   if (optionType.value === 1) {
//     emits('currentQdDeInfo', obj);
//   } else {
//     emits('currentInfoReplace', obj);
//   }
// };
const allInit = () => {
  initQuery();
  bdName.value = '';
  treeData.value = [];
  value1.value = 'dingE';
  value2.value = 'list';
  tableData.value = [];
  expandedKeys.value = [];
  selectedKeys.value = [];
  currentTreeInfo.value = null;
  currentInfo.value = null;
  conversionCoefficient.value = '';
  selectState.selectedRowKeys = [];
};

const queryCurrentData = () => {
  if (props.originInfo.kind === '03') {
    queryQdById();
  } else if (props.originInfo.rcjFlag === 0) {
    queryDeById();
  } else {
    queryRcjById();
  }
};
// 查询当前清单数据用于反向定位
const queryQdById = () => {
  api
    .queryQdById({
      standardId: props.originInfo.standardId,
      libraryCode: props.originInfo.libraryCode,
    })
    .then(res => {
      if (res.status === 200) {
        if (res.result) {
          selectValue.value = res.result.libraryCode;
          currentInfo.value = res.result;
          console.log('🌶index.vue|2280====>', 1);
          queryForm = {
            ...queryForm,
            libraryCode: res.result.libraryCode,
            classifyLevel1: res.result.bdNameLevel01,
            classifyLevel2: res.result.bdNameLevel02,
            classifyLevel3: res.result.bdNameLevel03,
          };
        }
        queryQdList();
      }
    });
};

const queryDeZyById = (type = false) => {
  const { standardId, libraryCode } = inventoryQuotaIndexOriginInfo.value;
  const postData = {
    constructId: route.query?.constructSequenceNbr,
    standardId,
    libraryCode,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
  };
  if (!postData.standardId) {
    qdzyGetTree(false);
    return;
  }

  csProject.queryDeZyById(postData).then(res => {
    console.log('🚀 ~ ccc:', res, props.originInfo, postData);
    if (res.status === 200) {
      if (res.result) {
        selectValue.value = res.result.guideLibrary.releaseYear;
        guideLibraryModel.value = res.result.guideLibrary;
        currentTreeInfo.value = res.result.baseQdLine;
        qdzyGetTree(true);
      }
    }
  });
};
// 查询当前定额数据用于反向定位
const queryDeById = () => {
  const standardId = currentStandardId.value;
  let libraryCode = props.originInfo.libraryCode;
  if (isEmptyDE.value && emptyDEDefaultDEData.value) {
    libraryCode = emptyDEDefaultDEData.value?.libraryCode;
  }
  const postData = {
    constructId: route.query?.constructSequenceNbr,
    unitId: store.currentTreeInfo?.id,
    singleId: store.currentTreeGroupInfo?.singleId,
    standardId,
    libraryCode,
  };
  console.log('🚀 ~ queryDeById ~ postData:', postData);
  api.queryDeById(postData).then(res => {
    console.log('🚀 询当前定额数据用于反向定位:', res);
    if (res.status === 200) {
      if (res.result) {
        console.log('🚀 ~ queryDeById ~ selectValue.value:', selectOptions.value);

        if (+store.deStandardReleaseYear === 22 && !standardId) {
          // 空定额，并且是22的
          selectValue.value = selectOptions.value[0]?.libraryCode;
        } else {
          selectValue.value = res.result.libraryCode;
        }
        currentInfo.value = res.result;
        console.log('🌶index.vue|2351====>', 2);
        queryForm = {
          ...queryForm,
          libraryCode: res.result.libraryCode,
          classifyLevel1: res.result.classifyLevel1,
          classifyLevel2: res.result.classifyLevel2,
          classifyLevel3: res.result.classifyLevel3,
          classifyLevel4: res.result.classifyLevel4,
        };
      }
      deListByTree();
    }
  });
};

// 查询当前人材机数据用于反向定位
const queryRcjById = () => {
  api
    .queryRcjById({
      standardId: props.originInfo.standardId,
      libraryCode: props.originInfo.extend1,
      constructId: route.query?.constructSequenceNbr,
      unitId: store.currentTreeInfo?.id,
      singleId: store.currentTreeGroupInfo?.singleId,
    })
    .then(res => {
      console.log('查询当前人材机数据用于反向定位', props.originInfo, res);
      if (res.status === 200) {
        if (res.result) {
          selectValue.value = res.result?.libraryCode;
          currentInfo.value = res.result;
          console.log('🌶index.vue|2382====>', 3);
          renCJQueryForm = {
            ...renCJQueryForm,
            libraryCode: res.result.libraryCode,
            level1: res.result.level1,
            level2: res.result.level2 || '',
            level3: res.result.level3 || '',
            level4: res.result.level4 || '',
            level5: res.result.level5 || '',
            level6: res.result.level6 || '',
            level7: res.result.level7 || '',
          };
        }
        getRcjTreeByCode();
      }
    });
};

const unitClose = () => {
  unitVisible.value = false;
  conversionCoefficient.value = null;
};

const handleOk = () => {
  if (conversionCoefficient.value === '0') {
    return message.warning('转换系数不可为0');
  }

  if (value1.value === 'qdzy') {
    // 清单指引替换的
    currentTreeInfo.value.unit = conversionCoefficient.value;
  } else {
    currentInfo.value.conversionCoefficient = conversionCoefficient.value;
  }

  console.log('66666666666', currentInfo.value);
  unitVisible.value = false;
  emits('currentInfoReplace', currentInfo.value);
  // allInit();
};

const rowClassName = ({ row }) => {
  if (row.parentId == 1) {
    return 'row-tree-title';
  }
};
</script>

<style lang="scss" scoped>
@use '../subItemProject/s-table.scss';
.table-content {
  :deep(.sub) {
  }
  :deep(.resizer) {
    position: relative;
    background: #c3ddf7;
    &:hover {
      background: #287cfa;
    }
  }
}
.contract-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 7px;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 6;
  width: 32px;
  height: 9px;
  cursor: pointer;
  transition: all 0.3s;
  color: #fff;
  background: #287cfa;
}
.table-content {
  height: calc(100% - 44px);
  :deep(.surely-table-header),
  :deep(.surely-table-header-cell) {
    height: 32px !important;
  }
  :deep(.surely-table-vertical-scroll),
  :deep(.surely-table-body-container) {
    border-right: 1px solid rgb(185, 185, 185);
  }
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.noData {
  margin: 0 auto;
  display: block;
  width: 250px;
  height: auto;
}
.qdzy-wrap {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  .qdzy-btns {
    margin-left: 15px;
  }
}

.dialog-wrap {
  // width: 1000px;
  width: 100%;
  height: 100%;
}
.contentCenter {
  display: flex;
  justify-content: space-around;
  color: #b9b9b9;
  // margin-top: 10px;
  height: calc(100% - 10px);
  .left {
    width: 35%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .search {
      width: 100%;
      flex: 1;
      border: 1px solid #b9b9b9;
      //overflow: hidden;
      overflow-y: scroll; // 解决闪烁问题
      height: 100%;
      .tree {
        width: calc(100% - 10px);
        height: calc(90% - 75px);
        zoom: 0.9;
        position: relative;
        z-index: 1;
        // border: 1px solid #dcdfe6;
        margin-left: 10px;
        overflow: hidden;
        :deep(.ant-tree) {
          height: 100%;
        }
        &:hover {
          overflow: auto;
        }
      }
    }
  }
  .right {
    width: 62%;
    display: flex;
    flex-direction: column;
    .btns {
      display: flex;
      width: 100%;
      justify-content: space-between;
      .btnNo1 {
        margin: 0 15px;
      }
    }
    .table {
      flex: 1;
      :deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis) {
        height: 22px !important;
      }
    }
  }
}
.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.dialog-content {
  padding: 46px;
  text-align: center;
}
.init-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  .icon {
    margin-right: 5px;
  }
  span {
    color: #1e90ff;
    margin: 0 2px;
  }
  .ant-input {
    width: 105px;
    margin-left: 5px;
  }
}
.init-text:nth-last-of-type(1) {
  margin-top: 25px;
}
.footer-btn-list {
  padding-bottom: 20px;
  margin-top: 20px;
}
</style>
<style lang="scss">
.qdzy-table {
  border: 1px solid rgba(185, 185, 185, 1);
  overflow: hidden;
  .tree-title {
    font-weight: 600;
    color: rgba(42, 42, 42, 1);
    font-size: var(--project-detail-table-font-size);
  }
  .vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
    display: flex;
    justify-content: flex-end;
    // .vxe-checkbox--icon{
    //   color:rgba(185, 185, 185, 1);
    // }
  }
  .row-tree-title .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
    display: flex;
    justify-content: flex-start;
  }
  .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
    color: rgba(185, 185, 185, 1);
  }
  .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon {
    color: var(--vxe-primary-color);
  }
  .vxe-table {
    .vxe-tree--btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      left: -4px;
    }
    .row-tree-title {
      background-color: rgba(232, 239, 255, 1);
    }
    .vxe-tree--line {
      /* 修改连接线的颜色 */
      border-left: 1px solid #87b2f2;
      border-bottom: 1px solid #87b2f2;
    }
    .vxe-icon-caret-down,
    .vxe-icon-caret-right {
      width: 12px;
      height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 8px;
      border-radius: 50%;
      position: relative;
      top: -1px;
      left: 2px;
      border: 1px solid #87b2f2;
      color: #87b2f2;
      font-size: 12px;
    }

    .vxe-cell--tree-node {
      .rotate90 {
        transform: rotate(0);
      }
      .vxe-icon-caret-right:before {
        content: '+';
        display: contents;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(50%, 50%);
      }
      &.is--active {
        .vxe-icon-caret-right:before {
          content: '-';
        }
      }
    }

    .multiple-check {
      background: #a6c3fa !important;
      // background-color: hsl(200, 50%, 50%);
      // border-bottom:1px solid white ;
    }
  }

  .vxe-table .index-bg {
    background-color: #ffffff;
  }

  .vxe-table--render-default.is--tree-line .vxe-body--row .vxe-body--column {
    background-image:
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1)),
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1));
  }
  .vxe-table--render-default.is--tree-line .vxe-header--column {
    background-image:
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1)),
      linear-gradient(rgba(185, 185, 185, 1), rgba(185, 185, 185, 1));
  }
  .vxe-table--body {
    // border-collapse: collapse;
  }
}
</style>
