const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const OtherProjectDayWorkRcjConstant = require("../enum/OtherProjectDayWorkRcjConstant");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Snowflake} = require("../utils/Snowflake");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {Service} = require("../../../core");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {jrg} = require("../jsonData/jrg.json");
const { getConnection ,getRepository,getManager  } =require('typeorm');
class OtherProjectDayWorkService extends Service{



    //数据行 类型 汇总行
    static datyTypeHuiZong = 0;
    //数据行 类型 标题行
    static datyTypeBiaoTi = 1;

    //数据行 类型 数据行
    static datyTypeShuJu = 2;


    //默认数量
    static defaultAmount = 1;

    //默认项目价值
    static defaultXmje = 0;

    //默认费率
    static defaultRate = 0;

    //计算保留小数位
    static decimalPlaces = 2;


    constructor(ctx) {
        super(ctx);
    }

    getDefaultOtherProjectDayWork(){
        let array = new Array();
        for (let i in jrg) {
            let obj = new OtherProjectDayWork();
            ConvertUtil.setDstBySrc(jrg[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            array.push(obj);
        }
        return array;
    }
    getOtherProjectDayWork(args){
        return  PricingFileFindUtils.getOtherProjectDayWork(args.constructId,args.singleId, args.unitId);
    }



    //计日工操作 操作
    async otherProjectDayWork(arg){

        //操作 类型  1:插入 2:粘贴 3删除 4 修改
        let operateType = arg.operateType;

        switch (operateType) {
            case 1:
                this.addProjectDayWork(arg);
                break;
            case 2:
                this.pasteProjectDayWork(arg);
                break;
            case 3:
                this.delectProjectDayWork(arg);
                break;
            case 4:
                this.updateProjectDayWork(arg);
                break;
            /*case 5:
                this.select(arg);
                break;*/
        }
    }




    //插入
    addProjectDayWork(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        let dataType = arg.dataType;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectDayWorks;

        let number;
        if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
            number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
            if(number==0){
                number++;
            }
        }else {
            number = 0;
        }
        let otherProjectDayWork = new OtherProjectDayWork();
        otherProjectDayWork.sequenceNbr = Snowflake.nextId();

        otherProjectDayWork.total = OtherProjectDayWorkService.defaultXmje;
        otherProjectDayWork.jxTotal = OtherProjectDayWorkService.defaultXmje;

        otherProjectDayWork.csTotal = OtherProjectDayWorkService.defaultXmje;
        otherProjectDayWork.dataType = dataType;

        if (dataType === OtherProjectDayWorkService.datyTypeShuJu){
            otherProjectDayWork.tentativeQuantity = Number(0);
            otherProjectDayWork.csPrice = OtherProjectDayWorkService.defaultXmje;
            otherProjectDayWork.price = OtherProjectDayWorkService.defaultXmje;
            if (!ObjectUtils.isEmpty(targetSequenceNbr)) {
                let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
                otherProjectDayWork.rcjFlag = projectService.rcjFlag;

                switch (otherProjectDayWork.rcjFlag){
                    case OtherProjectDayWorkRcjConstant.cl :
                        otherProjectDayWork.taxRemoval =11.28;
                        break;
                    case OtherProjectDayWorkRcjConstant.jx :
                        otherProjectDayWork.taxRemoval =8.66;
                        break;
                    default:
                        otherProjectDayWork.taxRemoval =OtherProjectDayWorkService.defaultXmje;
                };


                if (projectService.dataType === OtherProjectDayWorkService.datyTypeBiaoTi){
                    otherProjectDayWork.parentId = targetSequenceNbr;
                    number = number +1;
                }else {
                    otherProjectDayWork.parentId = projectService.parentId;

                }
            }
        }

        list.splice(number+1,0,otherProjectDayWork);
    }



    //粘贴
    pasteProjectDayWork(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectDayWorks;

        let projectDayWorkList = arg.projectDayWorkList;
        if(ObjectUtils.isEmpty(projectDayWorkList)){
            return ResponseData.fail('粘贴数据不能为空');
        }

        let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        /*if(ObjectUtils.isEmpty(projectService)){
            return ResponseData.fail('插入时所选参照目标数据不存在');
        }*/

        //数据转换
        let array = new Array();
        let nextId = Snowflake.nextId();
        for (let i = 0; i < projectDayWorkList.length; i++) {
            let otherProjectDayWork = new OtherProjectDayWork();
            ConvertUtil.setDstBySrc(projectDayWorkList[i],otherProjectDayWork);
            if (i===0){
                otherProjectDayWork.sequenceNbr = nextId;
            }else {
                otherProjectDayWork.sequenceNbr = Snowflake.nextId();
                otherProjectDayWork.parentId = nextId;
            }
            array.push(otherProjectDayWork);
        }

        if (array[0].dataType ===OtherProjectDayWorkService.datyTypeBiaoTi  && !ObjectUtils.isEmpty(projectService) && projectService.dataType ===OtherProjectDayWorkService.datyTypeShuJu){
            return ResponseData.fail('无法在数据行上粘贴标题行数据');
        }

        //复制对象是标题行
        if (array[0].dataType ===OtherProjectDayWorkService.datyTypeBiaoTi){
            let number;
            if (!ObjectUtils.isEmpty(projectService)) {
                number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
                let ts = list.filter(item => item.parentId === targetSequenceNbr);
                number = number + ts.length + 1;

            }else {
                number = 0;
            }
            list.splice(number,0,...array);

        }else {
            //复制对象是数据行
            if(ObjectUtils.isEmpty(projectService)){
                return ResponseData.fail('插入时所选参照目标数据不存在');
            }
            if (projectService.dataType === OtherProjectDayWorkService.datyTypeBiaoTi){
                array[0].parentId = targetSequenceNbr;
            }else {
                array[0].parentId = projectService.parentId;
            }

            let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);

            list.splice(number +1,0,array[0]);

        }

        this.updateBiaotiTotal(unit);
        this.updateHuiZongTotal(unit);
        // this.updateOtherProjectDayWork(arg);
        this.service.yuSuanProject.otherProjectService.updateAllOtherProjects(arg);

    }



    //删除
    delectProjectDayWork(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectDayWorks;
        let projectService = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (projectService.dataType === OtherProjectDayWorkService.datyTypeShuJu){
            let number = list.findIndex(obj => obj['sequenceNbr'] === targetSequenceNbr);
            list.splice(number,1);
        }else {
            let ts = list.filter(item => item.sequenceNbr !== targetSequenceNbr).filter(item => item.parentId !== targetSequenceNbr);
            unit.otherProjectDayWorks = ts;
        }

        this.updateBiaotiTotal(unit);
        this.updateHuiZongTotal(unit);
        // this.updateOtherProjectDayWork(arg);
        this.service.yuSuanProject.otherProjectService.updateAllOtherProjects(arg);

    }



    //编辑
    updateProjectDayWork(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;

        let worksName = arg.projectDayWork.worksName;
        let specification = arg.projectDayWork.specification;
        let quantitativeExpression = arg.projectDayWork.quantitativeExpression;
        let taxRemoval = arg.projectDayWork.taxRemoval;
        let price = arg.projectDayWork.price;
        let dispNo = arg.projectDayWork.dispNo;
        let unitBj = arg.projectDayWork.unit;
        let tentativeQuantity = arg.projectDayWork.tentativeQuantity;
        let description = arg.projectDayWork.description;



        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectDayWorks;
        let projectDayWork = list.find(obj => obj['sequenceNbr'] === targetSequenceNbr);
        if (!ObjectUtils.isEmpty(worksName)){
            projectDayWork.worksName = worksName;
        }

        if (!ObjectUtils.isEmpty(specification)){
            projectDayWork.specification = specification;
        }

        if (!ObjectUtils.isEmpty(quantitativeExpression)){
            projectDayWork.quantitativeExpression = quantitativeExpression;
            projectDayWork.tentativeQuantity = eval(quantitativeExpression).toFixed(6);
        }

        if (!ObjectUtils.isEmpty(taxRemoval)){
            projectDayWork.taxRemoval = taxRemoval;
        }

        if (!ObjectUtils.isEmpty(price)){
            projectDayWork.price = price;
        }

        if (!ObjectUtils.isEmpty(dispNo)){
            projectDayWork.dispNo = dispNo;
        }

        if (!ObjectUtils.isEmpty(unitBj)){
            projectDayWork.unit = unitBj;
        }

        if (!ObjectUtils.isEmpty(tentativeQuantity)){
            projectDayWork.tentativeQuantity = tentativeQuantity;
        }

        if (!ObjectUtils.isEmpty(description)){
            projectDayWork.description = description;
        }


        //计算
        //计算暂列金额  【金额】=【项目价值】*【数量】*【费率】%

        /*projectDayWork.total = NumberUtil.multiplyToString(NumberUtil.multiply(projectDayWork.xmje,projectDayWork.amount),
            NumberUtil.multiply(projectDayWork.rate,0.01),OtherProjectDayWorkService.decimalPlaces);*/
        if (projectDayWork.dataType === OtherProjectDayWorkService.datyTypeShuJu ) {
            projectDayWork.total = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(projectDayWork.tentativeQuantity, projectDayWork.price, OtherProjectDayWorkService.decimalPlaces));

            let taxRemovalDecimal = NumberUtil.multiplyToString(projectDayWork.taxRemoval, 0.01);
            projectDayWork.jxTotal = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(projectDayWork.total, taxRemovalDecimal, OtherProjectDayWorkService.decimalPlaces));
            projectDayWork.jxTaxAmount = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(projectDayWork.total,  taxRemovalDecimal, OtherProjectDayWorkService.decimalPlaces));
            projectDayWork.csPrice = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(projectDayWork.price, NumberUtil.subtract(1, taxRemovalDecimal), OtherProjectDayWorkService.decimalPlaces));
            projectDayWork.csTotal = NumberUtil.removeExtraZerosAndDot(NumberUtil.multiplyToString(projectDayWork.total, NumberUtil.subtract(1, taxRemovalDecimal), OtherProjectDayWorkService.decimalPlaces));
        }

        this.updateBiaotiTotal(unit);
        this.updateHuiZongTotal(unit);
        //this.updateOtherProjectDayWork(arg);
        this.service.yuSuanProject.otherProjectService.updateAllOtherProjects(arg);

    }


    //计算标题行数据
    updateBiaotiTotal(unit){
       /* let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);*/
        let list = unit.otherProjectDayWorks;

        let otherProjectDayWorks = list.filter(item => item.dataType === OtherProjectDayWorkService.datyTypeBiaoTi );
        for (let otherProjectDayWork1 of otherProjectDayWorks) {
            let otherProjectDayWorkList = list.filter(item => item.parentId === otherProjectDayWork1.sequenceNbr);
            let total = 0;
            let jxTotal = 0;
            let jxTaxAmount = 0;
            let csTotal = 0;
            for (let otherProjectDayWork2 of otherProjectDayWorkList) {
                total =  NumberUtil.add(total,otherProjectDayWork2.total);
                jxTotal =  NumberUtil.add(jxTotal,otherProjectDayWork2.jxTotal);
                jxTaxAmount =  NumberUtil.add(jxTaxAmount,otherProjectDayWork2.jxTotal);
                csTotal =  NumberUtil.add(csTotal,otherProjectDayWork2.csTotal);
            }
            otherProjectDayWork1.total = NumberUtil.removeExtraZerosAndDot(total.toFixed(2));
            otherProjectDayWork1.jxTotal =  NumberUtil.removeExtraZerosAndDot(jxTotal.toFixed(2));
            otherProjectDayWork1.jxTaxAmount =  NumberUtil.removeExtraZerosAndDot(jxTaxAmount.toFixed(2));
            otherProjectDayWork1.csTotal =  NumberUtil.removeExtraZerosAndDot(csTotal.toFixed(2));
        }
    }

    //计算汇总行数据
    updateHuiZongTotal(unit){
        /*let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);*/
        let otherProjects = unit.otherProjects
        let otherProjectDayWorkOfOther = otherProjects.find(item =>item.type ==='JRG'&& item.calculationBase ==="JRGMX");
        let list = unit.otherProjectDayWorks;

        let otherProjectDayWorks = list.filter(item => item.dataType === OtherProjectDayWorkService.datyTypeHuiZong );
        for (let otherProjectDayWork1 of otherProjectDayWorks) {
            let otherProjectDayWorkList = list.filter(item => item.dataType === OtherProjectDayWorkService.datyTypeBiaoTi);
            let total = 0;
            let jxTotal = 0;
            let csTotal = 0;
            let jxTaxAmount = 0;
            for (let otherProjectDayWork2 of otherProjectDayWorkList) {
                total =  NumberUtil.add(total,otherProjectDayWork2.total);
                jxTotal =  NumberUtil.add(jxTotal,otherProjectDayWork2.jxTotal);
                jxTaxAmount =  NumberUtil.add(jxTotal,otherProjectDayWork2.jxTaxAmount);
                csTotal =  NumberUtil.add(csTotal,otherProjectDayWork2.csTotal);
            }
            otherProjectDayWork1.total = NumberUtil.removeExtraZerosAndDot(total.toFixed(2));
            otherProjectDayWork1.jxTotal = NumberUtil.removeExtraZerosAndDot(jxTotal.toFixed(2));
            otherProjectDayWork1.jxTaxAmount = NumberUtil.removeExtraZerosAndDot(jxTotal.toFixed(2));
            otherProjectDayWork1.csTotal = NumberUtil.removeExtraZerosAndDot(csTotal.toFixed(2));

            if (otherProjectDayWorkOfOther){
                otherProjectDayWorkOfOther.jxTotal =  otherProjectDayWork1.jxTotal;
                otherProjectDayWorkOfOther.jxTaxAmount = otherProjectDayWork1.jxTotal
            }

        }
    }


    //汇总到 其他项目汇总表
    updateOtherProjectDayWork(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        let list = unit.otherProjectDayWorks;
        let otherProjectDayWorks = list.filter(item => item.dataType === OtherProjectDayWorkService.datyTypeBiaoTi );
        let total = 0;
        let jxTotal = 0;
        let csTotal = 0
        for (let otherProjectDayWorks1 of otherProjectDayWorks) {
            total =  NumberUtil.add(total,otherProjectDayWorks1.total);
            jxTotal =  NumberUtil.add(jxTotal,otherProjectDayWorks1.jxTotal);
            csTotal =  NumberUtil.add(csTotal,otherProjectDayWorks1.csTotal);
        }


        let otherProjects = unit.otherProjects;
        let t = otherProjects.find(j => j.calculationBase === OtherProjectCalculationBaseConstant.jrg);

        t.total = NumberUtil.multiplyToString(total,t.amount,OtherProjectDayWorkService.decimalPlaces);
        t.jxTotal = NumberUtil.multiplyToString(jxTotal,t.amount,OtherProjectDayWorkService.decimalPlaces);
        t.csTotal = NumberUtil.multiplyToString(csTotal,t.amount,OtherProjectDayWorkService.decimalPlaces);
    }



    /*select(arg){
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let targetSequenceNbr = arg.targetSequenceNbr;
        let dataType = arg.dataType;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let list = unit.otherProjectDayWorks;

        let number;
    }*/

}
OtherProjectDayWorkService.toString = () => '[class OtherProjectDayWorkService]';
module.exports = OtherProjectDayWorkService;