"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseUnitProjectType2022 = exports.BaseUnitProjectType = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
/**
 * 工程专业
 */
let BaseUnitProjectType = class BaseUnitProjectType extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "unit_project_name" }),
    __metadata("design:type", String)
], BaseUnitProjectType.prototype, "unitProjectName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "del_flag" }),
    __metadata("design:type", String)
], BaseUnitProjectType.prototype, "delFlag", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseUnitProjectType.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseUnitProjectType.prototype, "sortNo", void 0);
BaseUnitProjectType = __decorate([
    (0, typeorm_1.Entity)()
], BaseUnitProjectType);
exports.BaseUnitProjectType = BaseUnitProjectType;
let BaseUnitProjectType2022 = class BaseUnitProjectType2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "unit_project_name" }),
    __metadata("design:type", String)
], BaseUnitProjectType2022.prototype, "unitProjectName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "del_flag" }),
    __metadata("design:type", String)
], BaseUnitProjectType2022.prototype, "delFlag", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BaseUnitProjectType2022.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseUnitProjectType2022.prototype, "sortNo", void 0);
BaseUnitProjectType2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_unit_project_type_2022" })
], BaseUnitProjectType2022);
exports.BaseUnitProjectType2022 = BaseUnitProjectType2022;
//# sourceMappingURL=BaseUnitProjectType.js.map