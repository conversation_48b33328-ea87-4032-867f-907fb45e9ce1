"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectZygcZgj = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectZygcZgj extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, sortNo, name, content, unit, amount, price, total, taxRemoval, jxTotal, csPrice, csTotal, unitId, spId, constructId, dispNo, parentId) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.sortNo = sortNo;
        this.name = name;
        this.content = content;
        this.unit = unit;
        this.amount = amount;
        this.price = price;
        this.total = total;
        this.taxRemoval = taxRemoval;
        this.jxTotal = jxTotal;
        this.csPrice = csPrice;
        this.csTotal = csTotal;
        this.description = description;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
        this.dispNo = dispNo;
        this.parentId = parentId;
    }
}
exports.OtherProjectZygcZgj = OtherProjectZygcZgj;
//# sourceMappingURL=OtherProjectZygcZgj.js.map