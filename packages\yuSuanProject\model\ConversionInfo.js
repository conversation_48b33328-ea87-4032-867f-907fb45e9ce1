"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversionInfo = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 换算信息表
 */
let ConversionInfo = class ConversionInfo extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "sequence_nbr", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "conversion_string", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "conversionString", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "conversion_explain", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "conversionExplain", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "source", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "source_code", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "sourceCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rule_id", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "ruleId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_id", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "deId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "agency_code", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "agencyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "product_code", nullable: true }),
    __metadata("design:type", String)
], ConversionInfo.prototype, "productCode", void 0);
ConversionInfo = __decorate([
    (0, typeorm_2.Entity)({ name: "conversion_info" })
], ConversionInfo);
exports.ConversionInfo = ConversionInfo;
//# sourceMappingURL=ConversionInfo.js.map