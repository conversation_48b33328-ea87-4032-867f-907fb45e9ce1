"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeBs = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
/**
 * base 泵送费基数费用定额
 */
let BaseDeBs = class BaseDeBs extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: 'classify_level1', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "classifyLevel1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'classify_level2', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "classifyLevel2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'classify_level3', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "classifyLevel3", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'classify_level4', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "classifyLevel4", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'de_id', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "deId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'de_code', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'de_name', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unit', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'bs_type', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "bsType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'up_floor', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "upFloor", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'yk_high', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "ykHigh", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'library_code', nullable: true }),
    __metadata("design:type", String)
], BaseDeBs.prototype, "libraryCode", void 0);
BaseDeBs = __decorate([
    (0, typeorm_1.Entity)({ name: 'base_de_bs' })
], BaseDeBs);
exports.BaseDeBs = BaseDeBs;
//# sourceMappingURL=BaseDeBs.js.map