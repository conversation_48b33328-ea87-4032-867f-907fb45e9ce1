<!--
 * @Descripttion: 
 * @Author: kong<PERSON>qiang
 * @Date: 2023-08-15 16:00:27
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-23 16:29:46
-->
<template>
  <div class="menu">
    <div
      class="menu-item"
      v-for="(item, index) in asideData.menu"
      @click="open(item)"
      :key="index"
    >
      <div class="menu-item-box">
        <div class="icon">
          <img
            :src="item.icon"
            alt=""
          />
        </div>
        <div class="label">
          {{ item.title }}
        </div>
      </div>
    </div>
    <div
      class="popover"
      v-if="openPro"
      ref="target"
    >
      <p
        class="openPro"
        @click="openLocalPro"
      >打开本地项目</p>
      <!-- <p @click="openOlinePro">打开线上项目</p> -->
    </div>
    <new-project-model
      v-model:visible="newProVisible"
      :showType="asideData.propTitle"
    ></new-project-model>
    <new-audit-model
      v-model:visible="newAuditVisible"
      :showType="asideData.propTitle"
      @comparativeMatching="comparativeMatching"
    ></new-audit-model>
    <ImportProject
      v-model:visible="importVisible"
      @onSuccess="handleFileSuccess"
    ></ImportProject>
    <ImportSettleProject
      v-model:visible="importSettleVisible"
      @onSuccess="handleFileSuccess"
    ></ImportSettleProject>
    <ImportGsProject
      v-model:visible="importGsVisible"
      @onSuccess="handleGsSuccess"
    ></ImportGsProject>
    <ImportGljProject
      v-model:visible="importGljVisible"
      @onSuccess="handleGljSuccess"
    ></ImportGljProject>
    <edit-project-structure
      v-model:visible="editVisible"
      :config="editConfig"
      @success="editSuccess"
      @editClose="editClose"
      :qdStandardId="propStandardId"
    ></edit-project-structure>
    <!-- zip,rar项目校验 -->
    <check-file
      ref="checkFileRef"
      @onSuccess="checkFileSuccess"
    ></check-file>
    <compareMatch
      v-model:visible="compareMatchVisible"
      :sequenceNbr="newAuditConstructId"
      :original="0"
    >
    </compareMatch>
  </div>
</template>

<script setup>
import {
  onMounted,
  reactive,
  defineEmits,
  getCurrentInstance,
  ref,
  defineAsyncComponent,
  nextTick,
} from 'vue';
import csProject from '../../../api/csProject';
import gsCsProject from '../../../gaiSuanProject/api/csProject';
import newProjectModel from '../../../components/SelfModel/newProjectModel.vue';
import ImportProject from '../aside/ImportProject.vue';
import ImportSettleProject from '../aside/ImportSettleProject.vue';
import ImportGsProject from '../aside/ImportGsProject.vue';
import ImportGljProject from '../aside/ImportGljProject.vue';
import { proModelStore } from '@/store/proModel.js';
import { getUrl } from '@/utils/index';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '../../../api/main';
import { onClickOutside } from '@vueuse/core';
import feePro from '@/api/feePro';
import { message } from 'ant-design-vue';

const editProjectStructure = defineAsyncComponent(() =>
  import('@/components/editProjectStructure/index.vue')
);
const checkFile = defineAsyncComponent(() => {
  return import('@/components/fileSection/checkFile.vue');
});
const proStore = proModelStore();
import { projectDetailStore } from '@/store/projectDetail';
const projectStore = projectDetailStore();
const newProVisible = ref(false); //新建预算项目弹框
const newAuditVisible = ref(false); //新建审核项目弹框
const importSettleVisible = ref(false);
const target = ref(null);
const emit = defineEmits(['isModeShow']);
const openPro = ref(false);
const importVisible = ref(false);
const importGsVisible = ref(false);
const importGljVisible = ref(false);
const newAuditConstructId = ref(null);
const compareMatchVisible = ref(false);
const editVisible = ref(false);
import { checkisOnline } from '@/utils/publicInterface';

const globalProperties =
  getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const locationUrl = getUrl('location.png');
const asideData = reactive({
  value: '河北省',
  size: '',
  locationList: [
    {
      sequenceNbr: 130000,
      value: '河北省',
    },
  ], //位置获取的列表
  menu: [
    {
      icon: getUrl('newCsProject/open.png'),
      title: '打开项目',
      id: 'open',
    },
    // {
    //   icon: getUrl('newCsProject/gsNewPro.png'),
    //   title: '新建概算项目',
    //   id: 'newGS',
    // },
    // {
    //   icon: getUrl('newCsProject/gsNewPro.png'),
    //   title: '新建工料机项目',
    //   id: 'newGLJ',
    // },
    {
      icon: getUrl('newCsProject/newPro.png'),
      title: '新建预算项目',
      id: 'new',
    },
    // {
    //   icon: getUrl('newCsProject/newSH.png'),
    //   title: '新建审核项目',
    //   id: 'newshenhe',
    // },
    // {
    //   icon: getUrl('newCsProject/newSettle.png'),
    //   title: '新建结算项目',
    //   id: 'settle',
    // },
    {
      icon: getUrl('newCsProject/exportPro.png'),
      title: '导入项目',
      id: 'import',
    },
  ],
  propTitle: '',
  storeIsShow: false,
});
onMounted(() => {
  // 获取地址列表
  // {"recStatus": "A"}
  if ($ipc) {
    areaDropdownList();
  } else {
    csProject.getLocationList({}).then(function (response) {
      if (response.status === 200) {
        response.result.map(item => {
          if (item.name == '河北省' && item.sequenceNbr == 130000) {
            asideData.locationList = [
              { value: item.name, sequenceNbr: item.sequenceNbr },
            ];
            console.log('asideData.locationList', asideData.locationList);
          }
        });
      }
    });
  }
});

let editConfig = reactive({
  showMenu: false, //隐藏编辑按钮
  constructObj: null, //项目树
  isEdit: true, // 是否可以编辑工程专业
});

let ConstructId = ref(null); //导入之后的工程项目id

const checkFileRef = ref();
// 导入投标文件，新建投标，并且选择电子标成功
let propStandardId = ref(null); //导入项目选择的定额标准
const handleFileSuccess = (
  { list, isZip, importUrl, sequenceNbr, deStandardId },
  type = ''
) => {
  if (type === 'newJS') return;
  ConstructId.value = isZip ? sequenceNbr : list[0].id;
  editConfig.constructObj = list;
  propStandardId.value = deStandardId;
  if (isZip) {
    checkFileRef.value?.open(list, importUrl, sequenceNbr);
  } else {
    editVisible.value = true;
  }
};
const handleGsSuccess = () => {
  importGsVisible.value = false;
  proStore.SET_Refresh(true);
};
const handleGljSuccess = () => {
  importGljVisible.value = false;
  proStore.SET_Refresh(true);
};

// type noBack : 不用返回，back:需要打开上一个页面
const editClose = type => {
  if (type !== 'noBack') {
    csProject.deleteProject({ sequenceNbr: ConstructId.value }).then(res => {
      console.log('删除', res);
    });
  }
};

/**
 * 校验文件成功返回列表
 * @param {*} list
 */
const checkFileSuccess = ({ list }) => {
  checkFileRef.value?.close();
  editConfig.constructObj = list.fileLevelTreeNodes;
  editVisible.value = true;
};

// 编辑结构成功
const editSuccess = constructSequenceNbr => {
  importVisible.value = false;
  importSettleVisible.value = false;
  editVisible.value = false;
  proStore.SET_Refresh(true);
  handleOk(false);
};

//关闭查看所有项目弹窗
const handleOk = ismyModel => {
  asideData.queryAll = ismyModel;
  // store.commit('SET_ShowProList', true); 没有声明使用，暂且注释掉
  // store.commit('SET_ShowNewPro', true);
  // !ismyModel?store.commit('SET_ShowProList',true):null;
};

const open = item => {
  console.log(projectStore.loginUserInfo);
  if (!projectStore.loginUserInfo || !projectStore.loginUserInfo?.userInfo) {
    message.warning('请先登录再进行操作');
    return;
  }
  if (item && item.id == 'return') {
    //返回首页
  } else if (item && item.id == 'walkout') {
    //退出登录
  } else if (item && item.id == 'open') {
    // debugger;
    openPro.value = true;
    //打开项目
  } else if (item && item.id == 'import') {
    importVisible.value = true;
    //导入项目
  } else if (item && item.id == 'new') {
    newProVisible.value = true;
    asideData.propTitle = item.title;
  } else if (item && item.id == 'newshenhe') {
    newAuditVisible.value = true;
    asideData.propTitle = item.title;
    // 新建概算
  } else if (item && item.id == 'settle') {
    importSettleVisible.value = true;
    //新建结算项目
  } else if (item && item.id == 'newGS') {
    importGsVisible.value = true;
    //新建工料机项目
  } else if (item && item.id == 'newGLJ') {
    importGljVisible.value = true;
  }

  // else {
  //   openPro.value = false;
  //   openModel(item); //打开弹框
  // }
};
const openModel = item => {
  asideData.propTitle = item.title;
};

const areaDropdownList = () => {
  $ipc.invoke(ipcApiRoute.areaDropdownList).then(result => {
    if (result.status === 200) {
      result.result.map(item => {
        if (item.name === '河北省' && item.sequenceNbr === 130000) {
          asideData.locationList = [
            { value: item.name, sequenceNbr: item.sequenceNbr },
          ];
          console.log('asideData.locationList', asideData.locationList);
        }
      });
    }
  });
};

let openLocalProStatus = ref(false);
const openLocalPro = async () => {
  //打开本地项目
  console.log('点击打开本地项目');
  openPro.value = false;

  if (openLocalProStatus.value) {
    message.info('已打开系统弹窗');
    return;
  }
  openLocalProStatus.value = true;
  try {
    projectStore.maingWinLoading = true;
    let res = await feePro.openLocalFile();
    proStore.SET_Refresh(true);
    closeOperate;
  } catch (err) {
    closeOperate();
  } finally {
    closeOperate();
  }
  // setTimeout(() => {
  //   feePro
  //     .openLocalFile()
  //     .then(res => {
  //       if (res?.status === 200) {
  //         document.getElementById('body-loading').style.display = 'flex';
  //       }
  //       console.log('打开本地项目', res);
  //     })
  //     .finally(res => {
  //       console.log('🚀 ~ feePro.openLocalFile ~ res:', res);
  //       openLocalProStatus.value = false;
  //       document.getElementById('body-loading').style.display = 'none';
  //       projectStore.maingWinLoading = false;
  //     });
  // }, 0);
};
const closeOperate = () => {
  openLocalProStatus.value = false;
  // document.getElementById('body-loading').style.display = 'none';
  projectStore.maingWinLoading = false;
};
const openOlinePro = async () => {
  // 判断查看文件的时候有没有网;
  const isOnline = await checkisOnline(true);
  if (isOnline) {
    openPro.value = false;
    proStore.SET_IS_Click_Open(true);
  }
};
onClickOutside(target, () => {
  // 参数1：监听那个元素
  // 参数2：点击了该元素外的其他地方触发的函数
  if ((openPro.value = true)) {
    openPro.value = false;
  }
});

const comparativeMatching = val => {
  console.log(val, '打开对比匹配');
  newAuditConstructId.value = val;
  compareMatchVisible.value = true;
};
</script>
<style lang="scss" scoped>
.menu {
  display: flex;
  flex-direction: row;
  align-items: center;
  &-item:hover {
    background: linear-gradient(
      180deg,
      rgba(53, 121, 222, 0.08) 0%,
      #1e53a5 100%
    );
  }
  &-item {
    width: 219px;
    height: 146px;
    display: flex;
    cursor: pointer;
    flex-direction: row;
    align-items: center;
    &-box {
      width: 219px;
      text-align: center;
      color: white;
      .label {
        margin-top: 8px;
      }
    }
  }
}
.popover {
  z-index: 1;
  width: 160px;
  // height: 90px;
  text-align: center;
  padding: 15px;
  background: #ffffff;
  position: absolute;
  border-radius: 16px;
  top: 95%;
  left: 2%;
  box-shadow: 2px 0px 15px 5px rgba(0, 0, 0, 0.2);
  font-weight: 800;
  p {
    cursor: pointer;
  }
  .openPro {
    padding-bottom: 5px;
    // border-bottom: 1px solid #f2f2f2;
    margin-bottom: 0;
  }
}
.popover::before {
  content: '';
  width: 0;
  height: 0;
  border: 30px solid;
  position: absolute;
  top: -40px;
  left: 39px;
  transform: rotate(90deg);
  border-color: transparent #ffffff transparent transparent;
}
</style>
