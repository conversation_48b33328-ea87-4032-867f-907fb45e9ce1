/*
 * @Descripttion: 费用汇总
 * @Author: wangru
 * @Date: 2024-01-30 10:12:01
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-24 10:15:51
 */

import { ref, toRaw, reactive, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import feePro from '@/api/feePro';
import ysshcsProject from '@/api/shApi';
import jieSuanApi from '@/api/jiesuanApi';
import { message, Modal } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
export const summaryExpenseJs = ({
  pageType, // pageType-------预算审核还是审核
  upTable,
  comModel,
  textValue,
  oldValue,
  comArea,
  tableData,
  taxMode,
  totalFee,
  isCurrent,
  loading,
  deleteInfo,
  currentInfo,
  setMoveInfoObj,
}) => {
  let API = {
    ys: feePro,
    yssh: ysshcsProject,
    jieSuan: feePro,
  };
  const store = projectDetailStore();
  const keyDownOperate = event => {
    console.log('upTable.value', upTable.value);
    if (!upTable.value || store.tabSelectName !== '费用汇总') return;
    if (event.ctrlKey && event.code == 'KeyC') {
      copyItem(upTable.value.getCurrentRecord());
    }
    if (event.ctrlKey && event.code == 'KeyV') {
      getCurrentIndex(upTable.value.getCurrentRecord());
      pasteItem();
    }
    if (event.ctrlKey && event.code == 'KeyD') {
      deleteItem(upTable.value.getCurrentRecord());
    }
  };
  // 是否审删且金额不为0
  const isSheHeDelete = row => {
    return row?.ysshSysj?.change === 2 && ![0, '0'].includes(row.price);
  };
  const editCalc = row => {
    console.log('editCalc', row);
    comModel.value = true;
    textValue.value = row;
    oldValue.value = row.calculateFormula;
  };
  const cancelData = () => {
    comArea.value.value = oldValue.value;
    textValue.value.calculateFormula = oldValue.value;
    comModel.value = false;
  };
  const validateAndFormatCode = row => {
    const code = row.code.trim();
    const codePattern = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5]*$/;

    if (!codePattern.test(code)) {
      row.code = code.slice(0, -1); // 移除最后一个字符，因为它是非法字符
    }
  };
  const disposeSysj = data => {
    //处理审核 数据ysshSysj
    if (store.type === 'yssh' && data.hasOwnProperty('ysshSysj')) {
      data.ysshSysj = toRaw(data.ysshSysj);
    }
  };
  const sureData = () => {
    //编辑计算基数弹框确认
    const value = comArea.value.value;
    if (!value) {
      //不可以输入空
      message.warn(`输入不可为空`);
      return;
    }
    textValue.value.calculateFormula = value;
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      unitCostSummary: { ...textValue.value },
    };

    disposeSysj(apiData.unitCostSummary);
    console.log('修改费用汇总数据', apiData);
    feePro.saveCostSummary(apiData).then(res => {
      console.log('修改费用返回结果', res);
      if (res.status === 200 || res == true) {
        message.success('修改成功!');
        comModel.value = false;
        tableData.value.map((item, index) =>
          textValue.value.sequenceNbr === item.sequenceNbr
            ? (isCurrent.value = index)
            : ''
        );
        countUnitCostSummaryJS();
        getTableData();
      } else if (res?.status === 500) {
        message.error(res.message);
        comArea.value.value = oldValue.value;
        textValue.value.calculateFormula = oldValue.value;
        return;
      }
    });
  };
  // //获取当前计税方式
  // const getTaxMethods = () => {
  //   let apiData = {
  //     levelType: store.currentTreeInfo.levelType,
  //     constructId: store.currentTreeGroupInfo?.constructId,
  //   };
  //   if (store.currentTreeInfo.levelType === 3) {
  //     apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
  //     apiData.unitId = store.currentTreeInfo?.id; //单位ID
  //   }
  //   API[store.type].getTaxCalculation(apiData).then(res => {
  //     if (res.status === 200) {
  //       taxMode.value = res.result && res.result.taxCalculationMethod;
  //       console.log('计税方式', taxMode.value);
  //     }
  //   });
  // };
  // const getTotalFeeCode = () => {
  //   // getTaxMethods();
  //   taxMode.value = store.taxMade;
  //   const formdata = {
  //     type: '',
  //     constructId: store.currentTreeGroupInfo?.constructId,
  //     singleId: store.currentTreeGroupInfo?.singleId, //单项ID
  //     unitId: store.currentTreeInfo?.id, //单位ID
  //   };
  //   feePro.costCodePrice(formdata).then(res => {
  //     if (res.status === 200) {
  //       res.result && res.result.map(item => totalFee.value.push(item.code));
  //     }
  //   });
  // };
  const getTableData = async () => {
    loading.value = true;
    let apiData;
    if (store.type === 'yssh') {
      apiData = {
        ssConstructId: store.currentTreeGroupInfo?.ssConstructId,
        ssSingleId: store.currentTreeGroupInfo?.ssSingleId,
        ssUnitId: store.currentTreeInfo?.ysshUnitId,
        constructId: store.currentTreeGroupInfo?.constructId,
        singleId: store.currentTreeGroupInfo?.singleId,
        unitId: store.currentTreeInfo?.id,
      };
    } else {
      apiData = {
        constructId: store.currentTreeGroupInfo?.constructId,
        singleId: store.currentTreeGroupInfo?.singleId, //单项ID
        unitId: store.currentTreeInfo?.id, //单位ID
      };
    }
    totalFee.value = [];
    console.log('获取费用汇总数据', apiData, store);
    let isDifference = false;
    if (store.type === 'jieSuan') {
      let apiData = {
        constructId:
          store.currentTreeInfo.levelType === 1
            ? store.currentTreeInfo?.id
            : store.currentTreeGroupInfo?.constructId,
        singleId: store.currentTreeInfo?.parentId,
        unitId: store.currentTreeInfo?.id,
      };
      jieSuanApi.findUnitProjectById(apiData).then(res => {
        isDifference = res.isDifference;
      });
    }
    await API[store.type].getUnitCostSummary(apiData).then(res => {
      console.log('获取费用汇总数据返回结果', res);
      if (res.status === 200) {
        loading.value = false;

        res.result &&
          res.result.map((item, index) => {
            totalFee.value.push(item.code);
            if (
              item.type === '附加税费' ||
              item.type === '销项税额' ||
              item.type === '税金'
            ) {
              item.whetherTax = 1;
            } else {
              item.whetherTax = 0;
            }
          });
        tableData.value = res.result ? res.result : [];
        tableData.value.map(item => (item._X_ROW_KEY = item.sequenceNbr));
        isCurrent.value
          ? upTable.value.setCurrentRow(tableData.value[isCurrent.value])
          : upTable.value.setCurrentRow(tableData.value[0]);
        if (!isDifference && !store.currentTreeInfo.originalFlag) {
          tableData.value = tableData.value.filter((item, index) => {
            return item?.sourceFlag !== '1';
          });
        }
        console.log('findUnitProjectById----getTableData', isDifference);
        // currentInfo.value = upTable.value?.getCurrentRecord();
        if (['jieSuan', 'yssh'].includes(store.type)) setMoveInfoObj();
      }
    });

    // getTotalFeeCode();
  };
  const clear = () => {
    //清除编辑状态
    const $table = upTable.value;
    $table.clearEdit();
  };
  const editClosedEvent = ({ row, column }) => {
    console.log('费用汇总修改', row);
    const $table = upTable.value;
    const field = column.field;
    let value = row[field];
    const reg = /[^\d\.]/g;
    // 判断单元格值没有修改
    if (!$table.isUpdateByRow(row, field)) {
      return;
    }
    if (!['remark', 'rate'].includes(field) && !row[field]) {
      //不可以输入空
      $table.revertData(row, field);
      message.warn(`输入不可为空`);
      return;
    }
    // 判断单元格值是否被修改
    if ((field === 'name' || field === 'remark') && value?.length > 50) {
      console.log('field', field, 'value', value);
      row[field] = value.slice(0, 50);
      message.warn(`输入字符应50个字符范围内`);
    }
    if (field === 'name' && value?.length === 0) {
      $table.revertData(row, field);
      message.warn(`输入名称不可为空!`);
      return;
    }
    if (field === 'calculateFormula' && value?.length === 0) {
      message.warn(`计算基数不可为空`);
      $table.revertData(row, field);
      return;
    }
    if (field === 'rate' && value !== '' && reg.test(value)) {
      //不可以输入除数字和小数点之外的
      $table.revertData(row, field);
      return;
    }
    if (field === 'rate' && value === '') {
      row[field] = '';
    } else if (
      (field === 'rate' && Number(value) > 1000) ||
      Number(value) < 0
    ) {
      message.warn(`费率可输入数值范围：0-1000`);
      $table.revertData(row, field);
      return;
    }
    if (field === 'rate') {
      //22 税金，
      if (
        (Number(taxMode.value) === 1 &&
          (row.type === '附加税费' || row.type === '销项税额')) ||
        ((Number(taxMode.value) === 0 || store.deType === '22') &&
          row.type === '税金')
      ) {
        if ($table.isUpdateByRow(row, field)) {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '是否确认修改税率？',
            descText:
              '修改税率将会同步关联取费表中计税设置的费率，是否确认修改?',
            isFunction: false,
            confirm: () => {
              update(row, field);
              infoMode.hide();
            },
            close: () => {
              $table.revertData(row, field);
              infoMode.hide();
            },
          });
          return;
        }
      }
    }
    // 判断单元格值是否被修改
    if ($table.isUpdateByRow(row, field)) {
      update(row, field);
    }
  };
  const countUnitCostSummaryJS = () => {
    if (store.type !== 'jieSuan') return;
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
    };
    jieSuanApi.countUnitCostSummaryJS(apiData).then(res => {
      console.log('countUnitCostSummaryJS', res);
    });
  };
  const update = (row, field) => {
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
      unitCostSummary: { ...row },
    };
    console.log('费用汇总修改', apiData);
    disposeSysj(apiData.unitCostSummary);
    feePro.saveCostSummary(apiData).then(res => {
      console.log('费用汇总修改结果', res);
      if (res.status !== 500 || res == true) {
        message.success('修改成功!');
        tableData.value.map((item, index) => {
          if (item.sequenceNbr === row.sequenceNbr) {
            isCurrent.value = index;
          }
        });
        countUnitCostSummaryJS();
        getTableData();
      } else if (res.status === 500) {
        if (field === 'whetherPrint') {
          message.error('无法勾选');
        } else {
          message.error(res.message);
        }
        const $table = upTable.value;
        $table.revertData(row, field);
      }
    });
  };
  const pasteIsDisabled = () => {
    if (store.summaryCopyInfo && store.summaryCopyInfo.asideTitle === 'fyhz') {
      menuConfig.body.options[0][2].disabled = false;
    } else {
      menuConfig.body.options[0][2].disabled = true;
    }
  };
  const menuConfig = reactive({
    className: 'my-menus',
    body: {
      options: [
        [
          {
            name: '插入',
            code: 'add',
            disabled: false,
            visible: true,
          },

          {
            code: 'copy',
            name: '复制',
            visible: true,
          },
          {
            code: 'paste',
            name: '粘贴',
            disabled: true,
            visible: true,
          },
          {
            code: 'delete',
            name: '删除',
            className: 'redFont',
            disabled: false,
            visible: true,
          },
        ],
      ],
    },
    visibleMethod({ options, column, columnIndex, row, rowIndex }) {
      if (!row) return;
      if (store.type === 'jieSuan' && store.currentTreeInfo?.originalFlag) {
        //结算合同内不展示右键操作
        options[0].map(a => {
          a.visible = false;
        });
      } else {
        options[0].map(a => {
          a.visible = true;
        });
      }
      upTable.value.setCurrentRow(row);
      if (row && row.whetherTax === 1) {
        options[0][3].disabled = true;
        options[0][1].disabled = true;
        options[0][3].className = '';
      } else {
        options[0][3].disabled = false;
        options[0][1].disabled = false;
        options[0][3].className = 'redFont';
      }
      let del = options[0][3];
      if (store.type === 'yssh' && row.ysshSysj.change === 2) {
        del.disabled = true;
        del.className = '';
      } else {
        del.disabled = false;
        del.className = 'redFont';
      }
      if (store.type === 'yssh') {
        //预算审核复制粘贴暂时不做
        options[0][1].visible = false;
        options[0][2].visible = false;
        // options[0][3].visible = false;
      }
      pasteIsDisabled();

      return true;
    },
  });
  const getCurrentIndex = item => {
    if (item) {
      tableData.value.map((n, index) => {
        if (n.sequenceNbr === item.sequenceNbr) {
          isCurrent.value = index;
        }
      });
    } else {
      isCurrent.value = 0;
    }
    setMoveInfoObj();
  };
  const operate = (type, row) => {
    let operateType;
    switch (type) {
      case 'insert':
        // 插入
        operateType = 1;
        row ? getCurrentIndex(upTable.value.getCurrentRecord()) : '';
        break;
      case 'delete':
        // 删除
        operateType = 3;
        isCurrent.value = 0;
        break;
      case 'paste':
        // 粘贴
        operateType = 2;
        isCurrent.value += 1;
        break;
    }
    let isCurrentRow = upTable.value.getCurrentRecord();
    let targetSequenceNbr = isCurrentRow?.sequenceNbr;
    let lineNumber;
    tableData.value &&
      tableData.value.map((item, index) => {
        if (item.sequenceNbr === isCurrentRow.sequenceNbr) {
          lineNumber = index + 1;
        }
      });
    if (type === 'insert' || type === 'paste') {
      type === 'paste' ? (row.code = '') : '';
      if (['paste'].includes(type)) {
        //粘贴暂时不做
        disposeSysj(row);
      }
      let addData = {
        lineNumber: type === 'insert' ? lineNumber : lineNumber + 1,
        constructId: store.currentTreeGroupInfo?.constructId,
        singleId: store.currentTreeGroupInfo?.singleId, //单项ID
        unitId: store.currentTreeInfo?.id, //单位ID
        unitCostSummary: type === 'paste' ? { ...row } : {},
      };
      feePro.addCostSummary(addData).then(res => {
        if (res.status === 200) {
          type === 'insert'
            ? message.success('插入成功')
            : message.success('粘贴成功');
          console.log('********otherProjectOperates', res.result);
          countUnitCostSummaryJS();
          getTableData();
        }
      });
    } else if (type === 'delete') {
      let deleteData = {
        sequenceNbr: row?.sequenceNbr,
        constructId: store.currentTreeGroupInfo?.constructId,
        singleId: store.currentTreeGroupInfo?.singleId, //单项ID
        unitId: store.currentTreeInfo?.id, //单位ID
      };
      feePro.deleteCostSummary(deleteData).then(res => {
        if (res.result && res.result.status === 500) {
          message.error(res.result.message);
          return;
        } else {
          message.success('删除成功');
          countUnitCostSummaryJS();
          getTableData();
        }
      });
    }
  };
  const contextMenuClickEvent = ({ menu, row }) => {
    console.log('menu, row', menu, row);
    menu.code === 'delete' ? getCurrentIndex() : getCurrentIndex(row);
    switch (menu.code) {
      case 'copy':
        // 复制
        copyItem(row);
        break;
      case 'delete':
        // 删除
        deleteItem(row);
        break;
      case 'paste':
        // 粘贴
        pasteItem(row);
        break;
      case 'add':
        // 插入
        addItem(row);
        break;
    }
  };
  const pasteItem = () => {
    if (store.summaryCopyInfo && store.summaryCopyInfo.asideTitle === 'fyhz') {
      let paste = { ...store.summaryCopyInfo.copyInfo };
      operate('paste', paste);
    }
  };
  const addItem = item => {
    operate('insert', item);
  };
  const copyItem = item => {
    console.log('复制', item);
    if (item.whetherTax === 1) {
      message.warning('当前行不可复制');
      return;
    }
    store.SET_SUMMARY_COPYINFO({
      copyInfo: { ...item },
      asideTitle: 'fyhz',
    });
    message.success('复制成功');
    //需考虑跨单位工程之间可以复制粘贴
  };
  const deleteItem = item => {
    if (item.whetherTax === 1) {
      message.warning('当前行不可删除');
      return;
    }
    deleteInfo.value = { ...item };
    infoMode.show({
      iconType: 'icon-querenshanchu',
      isDelete: true,
      infoText: '是否确认删除？',
      descText: '是否删除当前数据行',
      confirm: () => {
        operate('delete', deleteInfo.value);
        infoMode.hide();
      },
      close: () => {
        infoMode.hide();
      },
    });
  };
  return {
    keyDownOperate,
    validateAndFormatCode,
    editCalc,
    cancelData,
    sureData,
    // getTaxMethods,
    // getTotalFeeCode,
    getTableData,
    clear,
    editClosedEvent,
    update,
    pasteIsDisabled,
    menuConfig,
    getCurrentIndex,
    operate,
    contextMenuClickEvent,
    pasteItem,
    addItem,
    copyItem,
    deleteItem,
    isSheHeDelete,
  };
};
