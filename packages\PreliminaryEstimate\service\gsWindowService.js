/*
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-05-13 20:37:06
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-07-31 16:37:41
 */
const {
  app: electronApp,
  dialog, shell, BrowserView, Notification,
  powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const ProjectDomain = require('../domains/ProjectDomain');
const { ObjectUtils } = require('../utils/ObjectUtils');
const AppContext = require('../core/container/APPContext');
const { Service } = require('../../../core');
const { ResponseData } = require('../../../electron/utils/ResponseData');
const { WinManageUtils } = require('../../../common/WinManageUtils');

class gsWindowService extends Service{

  constructor(ctx) {
    super(ctx);
  }



  async syncHistory()
  {

  }
  getAllWindowMap() {
		if (!global.windowMap) {
			global.windowMap = new Map();
		}
		return global.windowMap;
	}

  /**
   * 打开项目详情窗口
   */
  async openWindowForProject(constructId) {
    let project = ProjectDomain.getDomain(constructId).getProjectById(constructId);
    // //获取项目数据
    // let obj =await PricingFileFindUtils.getProjectObjByPath(path);
    // obj.path = path;
    // //用户的打开历史记录列表数据处理
    // obj = PricingFileWriteUtils.writeUserHistoryListFile(obj);
    // //将项目数据写入到内存当中
    // PricingFileWriteUtils.writeToMemory(obj);
    // if(obj.UPCContext){
    //     UPCContext.load(obj.UPCContext);
    // }
   //将项目数据更新到ysf文件当中
    //await this.service.ysfHandlerService.updateYsfFile(obj);
    let windowId = project.sequenceNbr;
    let windowName = project.name;

    // 从项目路径提取文件名（不含扩展名）
    const pathModule = require('path');
    let fileName = '';
    if (project.path) {
        fileName = pathModule.basename(project.path, pathModule.extname(project.path));
        // 如果文件名和项目名不同，则在窗口标题中显示文件名
        if (fileName && fileName !== windowName) {
            windowName = `${windowName} - ${fileName}`;
        }
    }

    // if (!ObjectUtils.isEmpty(AppContext.getWindowMap()) && AppContext.getWindowMap().has(windowId)){
    //   let newVar = AppContext.getWindowMap().get(windowId);
    //   // 获取对应的窗口引用
    //   let win = BrowserWindow.fromId(newVar);
    //   if (win.isMinimized()){
    //     win.restore();
    //   }
    //   //将窗口移动到顶部
    //   win.moveTop();
    //   return;
    // }
    if (this.getAllWindowMap().has(windowId)) {
      //异步需要await 啊，别改错了
      let windowMap = await WinManageUtils.getAllWindowIdCache();
			// 获取对应的窗口引用
			let win = BrowserWindow.fromId(
				windowMap.get(windowId)
			);
			if (win.isMinimized()) {
				win.restore();
			}
			//将窗口移动到顶部
			win.moveTop();
			// 更新现有窗口的标题
			win.setTitle(windowName);
			return;
		}
    //创建窗口
    let win = this.createWindowForProject(windowName,windowId);
    return win;
  }

  /**
   *
   * @param windowName
   * @param constructId
   * @returns {windowId}
   */
  createWindowForProject(windowName,constructId) {
    let addr = 'http://localhost:8080';
    // let addr = 'http://*************:8080/';
    if (this.config.env == 'prod') {
      const mainServer = this.app.config.mainServer;
      addr = mainServer.protocol + mainServer.host + ':' + mainServer.port;
    }
    const addonWindow = this.app.addon.window;
    let opt = {
      title: windowName,
      minWidth: 980,
      minHeight: 650,
      icon: this.config.windowsOption.icon
    }
    const name = constructId;
    const win = addonWindow.create(name, opt);
    //const winContentsId = win.webContents.id;
    let content = addr + '#/gsProjectDetail/customize?constructSequenceNbr='+constructId;
    // load page
    win.loadURL(content);
   	//全局map
		this.getAllWindowMap().set(constructId, win.id);
		//添加窗口关闭事件
		win.on('closed', () => {
			this.getAllWindowMap().delete(constructId);
            AppContext.removeContext(constructId);
			global.windowMap[constructId] = null;
		});
    // win.webContents.openDevTools();
    return win;
  }


}
gsWindowService.toString = () => '[class gsWindowService]';
module.exports = gsWindowService;