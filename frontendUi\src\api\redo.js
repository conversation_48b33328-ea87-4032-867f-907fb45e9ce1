/*
 * @Descripttion: 
 * @Author: k<PERSON><PERSON><PERSON>ang
 * @Date: 2024-11-14 16:00:51
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-11-15 09:30:37
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from './main';

export default {
  undo: params => ipc.invoke(ipcApiRoute.undo, params),
  redo: params => ipc.invoke(ipcApiRoute.redo, params),
  getDoList: params => ipc.invoke(ipcApiRoute.getDoList, params),
  backList: params => ipc.invoke(ipcApiRoute.backList, params),//获取备份列表
  clearFile: params => ipc.invoke(ipcApiRoute.clearFile, params),
  setBackupTime: params => ipc.invoke(ipcApiRoute.setBackupTime, params),
  openLocation: params => ipc.invoke(ipcApiRoute.openLocation, params),//打开项目文件位置
  openAndSave: params => ipc.invoke(ipcApiRoute.openAndSave, params),//打开项目并保存
  createModal: params => ipc.invoke(ipcApiRoute.createModal, params),//创建窗体
  setModalState: params => ipc.invoke(ipcApiRoute.setModalState, params),//设置窗体状态
}