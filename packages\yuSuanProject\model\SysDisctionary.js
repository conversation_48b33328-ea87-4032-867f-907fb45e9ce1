"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysDisctionary = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 字典表
 */
let SysDisctionary = class SysDisctionary extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "dict_code", nullable: true }),
    __metadata("design:type", String)
], SysDisctionary.prototype, "dictCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "entry_key" }),
    __metadata("design:type", String)
], SysDisctionary.prototype, "entryKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "entry_value" }),
    __metadata("design:type", String)
], SysDisctionary.prototype, "entryValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "order_num" }),
    __metadata("design:type", String)
], SysDisctionary.prototype, "orderNum", void 0);
SysDisctionary = __decorate([
    (0, typeorm_2.Entity)({ name: "sys_dictionary" })
], SysDisctionary);
exports.SysDisctionary = SysDisctionary;
//# sourceMappingURL=SysDisctionary.js.map