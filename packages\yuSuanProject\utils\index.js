function memoize(fn) {
    const cache = {};
    // 返回的闭包引用了cache cache并不会被回收 但cache仅对闭包可见
    return function (...args) {
        const key = JSON.stringify(args);
        if (!cache[key]) {
            cache[key] = fn.apply(this, args);
        }
        return cache[key];
    };
}

// 性能统计 使用process.hrtime 统计性能 返回毫秒
function calculateElapsedTime(startTime, endTime) {
    // 计算差值
    const diff = [endTime[0] - startTime[0], endTime[1] - startTime[1]];

    // 转换成毫秒
    const durationInMilliseconds = (diff[0] * 1e3) + (diff[1] / 1e6);

    return durationInMilliseconds;
}

module.exports = {
    memoize,
    calculateElapsedTime
}