"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversionDeRuleDetails = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 规则明细表
 */
let ConversionDeRuleDetails = class ConversionDeRuleDetails extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "kind", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_id", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "relationGroupId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_name", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "relationGroupName", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "relation_group_cnt", nullable: true }),
    __metadata("design:type", Number)
], ConversionDeRuleDetails.prototype, "relationGroupCnt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_rule", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "relationGroupRule", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_details_id", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "fileDetailsId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_code", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "relationCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "relation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "math", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "math", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_id", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "deId", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "selected", nullable: true }),
    __metadata("design:type", Number)
], ConversionDeRuleDetails.prototype, "selected", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "agency_code", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "agencyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "product_code", nullable: true }),
    __metadata("design:type", String)
], ConversionDeRuleDetails.prototype, "productCode", void 0);
ConversionDeRuleDetails = __decorate([
    (0, typeorm_2.Entity)({ name: "construct_de_rule_details" })
], ConversionDeRuleDetails);
exports.ConversionDeRuleDetails = ConversionDeRuleDetails;
//# sourceMappingURL=ConversionDeRuleDetails.js.map