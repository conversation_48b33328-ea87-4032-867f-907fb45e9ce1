
const HttpClient = require("../../../core/httpclient");
const Log = require("../../../core/log");
const {  isMainThread,parentPort } = require('worker_threads');

const httpClient = new HttpClient();
async function getSoftinfo() {
    try {
        const url = "http://localhost:20002/controller/manager/readFilterData";
        let result = await httpClient.request(url, {
            method: 'GET',
            contentType: 'json',
            data: {},
            headers: {}
        });
        if (result.res.status == 200) {
            let data = JSON.parse(result.res.data.toString());
            if (data.status == 200) {
                parentPort.postMessage(data);
            }
        }else {
            Log.info('error', result.res);
            parentPort.postMessage(null);
        }
    } catch (e) {
        Log.info('error', e);
        parentPort.postMessage(null);
    }

}
if (!isMainThread) {//在worker线程=
    setInterval(function() {
        console.log("test");
        getSoftinfo();
    }, 2000);
}
