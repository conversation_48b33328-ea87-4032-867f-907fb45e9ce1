"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StandardModel = exports.Node = void 0;
/**
 * 节点类
 */
class Node {
    constructor(attrs, marks = [], children = []) {
        this.attrs = attrs || {};
        this.marks = marks;
        this.children = children;
        this.parentId = this.attrs.parentId || 0;
    }
    refreshIndex() {
        let index = 0;
        for (const child of this.children) {
            child.index = index++;
        }
    }
    addChild(child) {
        child.parentId = this.attrs.sequenceNbr || 0;
        this.children.push(child);
        child.index = this.children.length - 1;
    }
    addChildAt(child, index) {
        if (index < 0 || index > this.children.length) {
            throw new Error('Invalid index for adding a child node');
        }
        child.parentId = this.attrs.sequenceNbr || 0;
        child.index = index;
        this.children.splice(index, 0, child); // 按指定下标插入子节点
        // 更新从插入位置开始的所有后续子节点的 index
        for (let i = index; i < this.children.length; i++) {
            this.children[i].index = i;
        }
    }
    updateAttrs(key, value) {
        this.attrs[key] = value;
    }
    removeChild(targetChild) {
        this.children.splice(targetChild.index, 1);
        this.refreshIndex();
        return true;
    }
    addMark(mark) {
        this.marks.push(mark);
    }
    cleanMark() {
        this.marks = [];
    }
    static create(attrs, marks = [], children = []) {
        return new Node(attrs, marks, children);
    }
}
exports.Node = Node;
/**
 * 通用节点类
 */
class StandardModel extends Node {
    constructor(attrs, marks = [], children = []) {
        super(attrs, marks, children);
        this.nodeMap = new Map();
    }
    getNodeById(nodeId) {
        return this.nodeMap.get(nodeId);
    }
    hasNodeById(nodeId) {
        return this.nodeMap.has(nodeId);
    }
    getAllNodes() {
        return Array.from(this.nodeMap.values());
    }
    find(filter) {
        let item = this.getAllNodes().filter(filter);
        if (item && item.length > 0) {
            return item[0];
        }
        return null;
    }
    filter(filter) {
        return this.getAllNodes().filter(filter);
    }
    addNode(newNode, parentNode) {
        this.nodeMap.set(newNode.attrs.sequenceNbr, newNode); // 将节点添加到节点映射表中
        (parentNode || this).addChild(newNode);
        return newNode;
    }
    addNodeAt(newNode, parentNode, index) {
        this.nodeMap.set(newNode.attrs.sequenceNbr, newNode); // 将节点添加到节点映射表中
        (parentNode || this).addChildAt(newNode, index);
        return newNode;
    }
    removeNode(nodeId) {
        const node = this.getNodeById(nodeId);
        if (node && node.parentId) {
            let parent = this.getNodeById(node.parentId);
            let result = parent.removeChild(node);
            if (result) {
                parent.refreshIndex();
            }
            return result;
        }
        else if (node) {
            this.removeChild(node);
            return true;
        }
        return false;
    }
}
exports.StandardModel = StandardModel;
//# sourceMappingURL=node.js.map