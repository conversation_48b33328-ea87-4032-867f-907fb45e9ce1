import { StandardConvertMod } from '../enums/ConversionSourceEnum';
import { CommonDto, Controller } from '../../../core';

const { ResponseData } = require("../utils/ResponseData");

export type GljBatchOperationalConversionRuleItemDto = {
	agencyCode?: string;
	chapterStatus?: boolean;
	dataFormat?: number;
	deCode?: string;
	ruleInfo?: string;
	clpb?: any;
	defaultValue: number | string;
	defaultValueMax?: string;
	defaultValueMin?: string;
	deId?: string;
	deName?: string;
	description?: string;
	extend1?: string;
	extend2?: string;
	extend3?: string;
	fbFxDeId: string;
	fileDetailsId?: string;
	height?: number;
	kind: string;
	libraryCode?: string;
	math: string;
	nowChange?: boolean;
	old_selected?: boolean;
	productCode?: string;
	rcjId?: string;
	recDate?: string;
	recStatus?: string;
	recUserCode?: string;
	relation: string;
	relationCode?: string;
	relationDeCode?: string;
	relationDeId?: string;
	relationGroupCnt?: number;
	relationGroupCode?: string;
	relationGroupId?: unknown;
	ruleGroupId?: unknown;
	relationGroupName?: string;
	relationGroupRule?: string;
	rowSpan?: number;
	ruleRange?: string;
	selected?: number;
	selectedRule: string;
	sequenceNbr: string;
	sortNo?: unknown;
	topGroupType?: string;
	type: string;
	index: number;
	// 是否是统一换算规则
	isUniteRule?: boolean;
	currentRcjCode?: string;
	defaultRcjCode?: string;
};
type GljBatchOperationalConversionRuleDto = CommonDto & {
	rules: GljBatchOperationalConversionRuleItemDto[];
};
// 标准换算
class GljConversionDeController extends Controller {
	constructor(ctx: unknown) {
		super(ctx);
	}
	static toString() {
		return "[class ConversionDeController]";
	}

	// 清除换算记录
	async cleanRules(dto: CommonDto, redo:String="清除- -换算信息") {
		await this.service.gongLiaoJiProject.gljConversionInfoService.clearConversionInfo(dto.constructId, dto.singleId, dto.unitId, dto.fbFxDeId)
		// await this.service.gongLiaoJiProject.gljConversionService.cleanRules(
		// 	dto.standardDeId,
		// 	dto.fbFxDeId,
		// 	dto.constructId,
		// 	dto.singleId,
		// 	dto.unitId
		// );

		// await this.service.gongLiaoJiProject.gljConversionDeProcess.cleanRules(
		// 	dto.standardDeId,
		// 	dto.fbFxDeId,
		// 	dto.constructId,
		// 	dto.singleId,
		// 	dto.unitId
		// );
		// await await this.service.gongLiaoJiProject.management.sycnTrigger("unitDeChange");
		// await await this.service.gongLiaoJiProject.management.trigger("itemChange");
		await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
			unitId: dto.unitId,
			singleId: dto.singleId,
			constructId: dto.constructId
		});
		return ResponseData.success(true);
	}

	async conversionRuleList(dto: CommonDto) {
		const result = await await this.service.gongLiaoJiProject.gljConversionDeProcess.conversionRuleList(
			dto
		);
		return ResponseData.success(result);
	}

	async conversionRuleListAll(dto: CommonDto) {
		const conversionRuleList = await this.service.gongLiaoJiProject.gljConversionDeProcess.conversionRuleListAll(
			dto
		);
		return ResponseData.success(conversionRuleList);
	}

	async getDefDonversion(dto: CommonDto & { deId: string }) {
		const res = await this.service.gongLiaoJiProject.gljConversionDeService.getDefDonversion(dto);
		return ResponseData.success(res);
	}

	// 批量更新默认属性
	//redo 不记录历史记录
	async updateDefDonversion(
		dto: CommonDto & { deId: string; donversions: string }
	) {
		const { constructId, singleId, unitId, deId, donversions } = dto;
		await await this.service.gongLiaoJiProject.gljConversionDeService.upDateDefault(
			constructId,
			singleId,
			unitId,
			deId,
			donversions
		);
		// await await this.service.gongLiaoJiProject.management.sycnTrigger("unitDeChange");
		// await await this.service.gongLiaoJiProject.management.trigger("itemChange");
    try {
      //联动计算装饰超高人材机数量
      // await this.service.gongLiaoJiProject.gljDeService.calculateZSFee(constructId, unitId, true);
      await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
        unitId: unitId,
        singleId: singleId,
        constructId: constructId
      });
    } catch (error) {
      console.error('联动计算计取费用捕获到异常:', error);
    }


    return ResponseData.success(true);
	}

	// 批量换算
	async batchOperationalConversionRule(dto: GljBatchOperationalConversionRuleDto, redo:string="编辑- -换算信息") {
		// TODO 先kind1 再 kind3 再取消kind1  kind3失效
		const { fbFxDeId, constructId, singleId, unitId, rules } = dto;
		await this.service.gongLiaoJiProject.gljConversionDeService.conversionRule(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			dto.rules
		);
		await Promise.all([
			// TODO 统一计算
			// await this.service.gongLiaoJiProject.management.sycnTrigger("unitDeChange"),
			// await this.service.gongLiaoJiProject.management.trigger("itemChange"),
		]);

		try {
            await this.service.gongLiaoJiProject.gljAZservice.calculateAZFeeLianDongAndAloneBzhs(constructId, unitId, fbFxDeId, "update");
			await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
				unitId: unitId,
				singleId: singleId,
				constructId: constructId
			});
		} catch (error) {
            console.error("联动计算安装计取费用捕获到异常:", error);
        }

		return ResponseData.success(true);
	}

	// 定额标准换算勾选
	async operationalConversionRule(
		dto: CommonDto & { clpb: any; baseRuleDetails: any }
	) {
		const { fbFxDeId, baseRuleDetails, constructId, singleId, unitId, clpb } =
			dto;
		baseRuleDetails.clpb = clpb; //材料配比
		const result = await await this.service.gongLiaoJiProject.gljConversionDeService.conversionRule(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			baseRuleDetails
		);
		await await this.service.gongLiaoJiProject.management.sycnTrigger("unitDeChange");
		await await this.service.gongLiaoJiProject.management.trigger("itemChange");
		return ResponseData.success(result);
	}

	// 定额换算信息列表
	async conversionInfoList(dto: CommonDto) {
		const { fbFxDeId, constructId, singleId, unitId } = dto;
		const result = await this.service.gongLiaoJiProject.gljConversionInfoService.getDeConversionInfo(
			constructId,
			singleId,
			unitId,
			fbFxDeId
		);
		return ResponseData.success(result);
	}

	/**
	 * 更新换算信息
	 * @param dto
	 */
	async updateDeConversionInfo(dto: {
		constructId: string;
		singleId: string;
		unitId: string;
		fbFxDeId: string;
		selectId: string;
		operateAction: string;
	},redo:string="删除- -换算信息") {
		const { fbFxDeId, constructId, singleId, unitId ,selectId,operateAction} = dto;
		const result = await this.service.gongLiaoJiProject.gljConversionInfoService.updateDeConversionInfo(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			selectId,	//当前行id
			operateAction	//up上移,down下移,delete删除
		);
		await this.service.gongLiaoJiProject.gljAutoCostMatchService.autoCostMath({
			unitId: unitId,
			singleId: singleId,
			constructId: constructId
		});
		return ResponseData.success(result);
	}

	async getGroupNames(dto: { libraryCode: string }) {
		const { libraryCode } = dto;
		const res = await this.service.gongLiaoJiProject.gljConversionDeProcess.getGroupNames(libraryCode);
		return ResponseData.success(res);
	}

	async getGroupDetail(dto: { groupName: string; libraryCode: string }) {
		const { groupName, libraryCode } = dto;
		const res = await this.service.gongLiaoJiProject.gljConversionDeProcess.getGroupDetail(
			groupName,
			libraryCode
		);
		return ResponseData.success(res);
	}

	// 切换当前换算标准 是默认值计算 还是 当前输入值计算
	async switchConversionMod(dto: {
		deId: string;
		standardConvertMod: StandardConvertMod;
	}, redo="切换- -数据执行规则") {
		const res = await await this.service.gongLiaoJiProject.gljConversionDeService.switchConversionMod(dto);
		return ResponseData.success(res);
	}

	// 切换主材换算模式 参与或不参与换算
	async switchConversionMainMatMod(dto: { deId: string }, redo="切换- -主材设备受系数影响") {
		const res =
			await await this.service.gongLiaoJiProject.gljConversionDeService.switchConversionMainMatMod(dto);
		return ResponseData.success(res);
	}
}
module.exports = GljConversionDeController;
