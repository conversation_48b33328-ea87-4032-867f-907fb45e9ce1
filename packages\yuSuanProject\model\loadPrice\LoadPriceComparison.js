"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoadPriceComparison = void 0;
//载价报告
class LoadPriceComparison {
    constructor(type, beforePrice, afterPrice, total) {
        this.type = type;
        this.beforePrice = beforePrice;
        this.afterPrice = afterPrice;
        this.total = total;
    }
}
exports.LoadPriceComparison = LoadPriceComparison;
//# sourceMappingURL=LoadPriceComparison.js.map