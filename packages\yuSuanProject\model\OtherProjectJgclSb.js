"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectJgclSb = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectJgclSb extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, sortNo, name, specification, unit, quantitativeExpression, amount, price, total, taxRemoval, jxTotal, csPrice, csTotal, deliveryWay, unitId, spId, constructId) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.sortNo = sortNo;
        this.name = name;
        this.specification = specification;
        this.unit = unit;
        this.quantitativeExpression = quantitativeExpression;
        this.amount = amount;
        this.price = price;
        this.total = total;
        this.taxRemoval = taxRemoval;
        this.jxTotal = jxTotal;
        this.csPrice = csPrice;
        this.csTotal = csTotal;
        this.deliveryWay = deliveryWay;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
    }
}
exports.OtherProjectJgclSb = OtherProjectJgclSb;
//# sourceMappingURL=OtherProjectJgclSb.js.map