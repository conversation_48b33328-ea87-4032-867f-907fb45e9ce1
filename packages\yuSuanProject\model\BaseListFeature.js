"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseListFeature = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * base 清单特征表
 */
let BaseListFeature = class BaseListFeature extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseListFeature.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "list_code", nullable: true }),
    __metadata("design:type", String)
], BaseListFeature.prototype, "listCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "feature_name", nullable: true }),
    __metadata("design:type", String)
], BaseListFeature.prototype, "featureName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "feature_value", nullable: true }),
    __metadata("design:type", String)
], BaseListFeature.prototype, "featureValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "agency_code", nullable: true }),
    __metadata("design:type", String)
], BaseListFeature.prototype, "agencyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "product_code", nullable: true }),
    __metadata("design:type", String)
], BaseListFeature.prototype, "productCode", void 0);
BaseListFeature = __decorate([
    (0, typeorm_1.Entity)({ name: "base_list_feature" })
], BaseListFeature);
exports.BaseListFeature = BaseListFeature;
//# sourceMappingURL=BaseListFeature.js.map