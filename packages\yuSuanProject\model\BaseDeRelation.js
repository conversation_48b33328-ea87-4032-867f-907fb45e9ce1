"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeRelation = void 0;
const typeorm_1 = require("typeorm");
/**
 * 12定额父级定额关联表
 */
let BaseDeRelation = class BaseDeRelation {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "list_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "listCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "job_content", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "jobContent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "groupid", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "groupid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code_f", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "deCodeF", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name_f", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "deNameF", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit_f", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "unitF", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code_relation", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "libraryCodeRelation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_content", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "relationContent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code_z", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "deCodeZ", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name_z", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "deNameZ", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit_z", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "unitZ", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quantity", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code_z", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelation.prototype, "libraryCodeZ", void 0);
BaseDeRelation = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_relation" })
], BaseDeRelation);
exports.BaseDeRelation = BaseDeRelation;
//# sourceMappingURL=BaseDeRelation.js.map