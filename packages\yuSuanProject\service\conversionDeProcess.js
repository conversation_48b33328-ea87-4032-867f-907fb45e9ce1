"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// @ts-nocheck
const core_1 = require("../../../core");
const BaseRuleDetailFull_1 = require("../model/BaseRuleDetailFull");
const BaseRuleDetailsFull2022_1 = require("../model/BaseRuleDetailsFull2022");
const conversionListItem_1 = require("../../../electron/model/conversionListItem");
const Log = require("../../../core/log");
const { ObjectUtils } = require("../utils/ObjectUtils");
const { PricingFileFindUtils } = require("../utils/PricingFileFindUtils");
const { ConversionRuleOperationRecord, } = require("../model/ConversionRuleOperationRecord");
const { Snowflake } = require("../utils/Snowflake");
const { SqlUtils } = require("../utils/SqlUtils");
const { NumberUtil } = require("../utils/NumberUtil");
const { ParamUtils } = require("../../../core/core/lib/utils/ParamUtils");
const ConstantUtil = require("../enum/ConstantUtil");
const { BaseRuleFileDetails } = require("../model/BaseRuleFileDetails");
const { getRepository, In } = require("typeorm");
const { RcjApplicationContext } = require("../rcj_handle/RcjContext");
/**
 * 定额换算 process
 */
class ConversionDeProcess extends core_1.Service {
    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
        this.baseDeRuleRelationService = this.service.yuSuanProject.baseDeRuleRelationService;
        this.baseRuleDetailsService = this.service.yuSuanProject.baseRuleDetailsService;
        this.baseRuleFileDetailsService = this.service.yuSuanProject.baseRuleFileDetailsService;
        this.conversionRuleOperationRecordService = this.service.yuSuanProject.conversionRuleOperationRecordService;
        this.conversionInfoService = this.service.yuSuanProject.conversionInfoService;
        this.baseRuleDetailFull2022Repo = this.app.appDataSource.getRepository(BaseRuleDetailsFull2022_1.BaseRuleDetailFull2022);
        this.baseRuleDetailFullRepo = this.app.appDataSource.getRepository(BaseRuleDetailFull_1.BaseRuleDetailFull);
        this.baseRuleFileDetailsRepo = this.app.appDataSource.getRepository(BaseRuleFileDetails);
        this.getDefPiaoZhunHuanSuan = () => {
            return [
                { sort: 1, type: "人工费", val: 1 },
                { sort: 2, type: "机械费", val: 1 },
                { sort: 3, type: "材料费", val: 1 },
                { sort: 4, type: "主材费", val: 1 },
                { sort: 5, type: "单价", val: 1 },
            ];
        };
        // R,C,J * k对应定额下的人工，材料（含主材），机械
        // 0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比
        this.rcjKindConvert = {
            "1": "R",
            "3": "J",
            "2": "C",
            "5": "C",
            "6": "C",
            "7": "C",
            "8": "C",
            "9": "C",
            "10": "C",
        };
    }
    initDef(constructId, singleId, unitId, deId) {
        if (!constructId && !singleId && !unitId) {
            constructId = ParamUtils.getPatram("commonParam").constructId;
            singleId = ParamUtils.getPatram("commonParam").singleId;
            unitId = ParamUtils.getPatram("commonParam").unitId;
        }
        let defHuanSuan = this.getDefPiaoZhunHuanSuan();
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unitProject.defaultConcersions
            ? (unitProject.defaultConcersions[deId] = defHuanSuan)
            : (unitProject.defaultConcersions = {
                [deId]: defHuanSuan,
            });
    }
    upDateDefault(constructId, singleId, unitId, deId, oldAlgorithm, newAlgorithm) {
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //  1. 获取定额下需要变动的人材机
        let rcjs = unitProject.constructProjectRcjs.filter((f) => f.deId == deId);
        if (rcjs && rcjs.length > 0) {
            if (newAlgorithm.type === "人工费") {
                rcjs = rcjs.filter((f) => f.kind === 1);
            }
            else if (newAlgorithm.type === "机械费") {
                rcjs = rcjs.filter((f) => f.kind === 3);
            }
            else if (newAlgorithm.type === "材料费") {
                rcjs = rcjs.filter((f) => f.kind === 2 || f.kind > 5);
            }
            else if (newAlgorithm.type === "主材费") {
                rcjs = rcjs.filter((f) => f.kind === 5);
            }
            else if (newAlgorithm.type === "单价") {
                rcjs = rcjs;
            }
        }
        if (!rcjs || rcjs.length == 0) {
            return rcjs;
        }
        //  3. 做记录
        // 换算信息 后端存的一个，迭代1就有，不太清楚干啥的
        if (!unitProject.conversionRuleOperationRecordList) {
            unitProject.conversionRuleOperationRecordList = [];
        }
        unitProject.conversionRuleOperationRecordList =
            unitProject.conversionRuleOperationRecordList.filter((f) => f.fbFxDeId != deId && f.ruleDetailId != newAlgorithm.name);
        unitProject.conversionRuleOperationRecordList.push({
            sequenceNbr: Snowflake.nextId(),
            fbFxDeId: deId,
            ruleDetailId: newAlgorithm.name,
            source: "标准换算",
        });
        // 前端展示用的 删除旧的记录 添加新的记录
        if (!unitProject.conversionInfoList) {
            unitProject.conversionInfoList = [];
        }
        unitProject.conversionInfoList = unitProject.conversionInfoList.filter((f) => f.deId != deId && f.ruleId != newAlgorithm.name);
        unitProject.conversionInfoList.push({
            sequenceNbr: Snowflake.nextId(),
            deId: deId,
            ruleId: newAlgorithm.name,
            source: "标准换算",
            conversionString: newAlgorithm.name + "*" + newAlgorithm.val,
            conversionExplain: newAlgorithm.name + "*" + newAlgorithm.val, // 用来记kind2的查看
        });
        //  4. 根据oldAlgorithm反算人材机消耗量
        for (let i = 0; i < rcjs.length; ++i) {
            if (rcjs[i].ruleDeActive && !rcjs[i].ruleDeActive[newAlgorithm.name]) {
                // 改过的数据 不动
                continue;
            }
            rcjs[i].resQty = NumberUtil.divide(rcjs[i].resQty, Number(oldAlgorithm.val));
        }
        //  5. 根据newAlgorithm计算人材机消耗量
        for (let i = 0; i < rcjs.length; ++i) {
            if (rcjs[i].ruleDeActive && !rcjs[i].ruleDeActive[newAlgorithm.name]) {
                // 改过的数据 不动
                continue;
            }
            if (!rcjs[i].lastResQty) {
                rcjs[i].lastResQty = rcjs[i].resQty;
            }
            rcjs[i].resQty = rcjs[i].lastResQty;
            rcjs[i].resQty = NumberUtil.multiply(rcjs[i].resQty, Number(oldAlgorithm.val));
        }
        // 6. 删除人材机标识
        if (newAlgorithm.val == 1) {
            for (let i = 0; i < rcjs.length; ++i) {
                if (rcjs[i].ruleDeActive) {
                    delete rcjs[i].ruleDeActive;
                }
            }
        }
    }
    /**
     * kind下拉框一级接口
     * @return {*}
     */
    getGroupNames(libraryCode, constructId, singleId, unitId) {
        if (ObjectUtils.isEmpty(libraryCode)) {
            return [];
        }
        let is2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let tableName = "base_bzhs_clpb";
        if (is2022) {
            tableName = "base_bzhs_clpb_2022";
        }
        let sql = "select group_name as groupName, library_code as libraryCode from " +
            tableName +
            " where library_code = ? group by group_name";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(libraryCode);
        return sqlRes;
    }
    getGroupDetail(groupName, libraryCode, constructId, singleId, unitId) {
        if (ObjectUtils.isEmpty(libraryCode)) {
            throw new Error("libraryCode为空");
        }
        let is2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let clpbTable = "base_bzhs_clpb";
        let rcjTable = "base_rcj";
        if (is2022) {
            clpbTable = "base_bzhs_clpb_2022";
            rcjTable = "base_rcj_2022";
        }
        let sql = "select a.*, b.specification from "
            + clpbTable + " a, "
            + rcjTable + " b "
            + " where a.standard_id = b.sequence_nbr and a.group_name = ? and a.library_code = ? order by a.details_code";
        // let sql =
        // 	"select * from " +
        // 	clpbTable +
        // 	" where group_name = ? and library_code = ? order by details_code";
        let sqlRes = this.app.betterSqlite3DataSource
            .prepare(sql)
            .all(groupName, libraryCode);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        return convertRes;
    }
    async cleanRules(standardDeId, fbFxDeId, constructId, singleId, unitId) {
        const { line: rawLine, belong: type } = this.service.yuSuanProject.baseBranchProjectOptionService.findLineOnlyById(fbFxDeId);
        const ruleIds = rawLine.conversionList.map((v) => v.sequenceNbr);
        let { defaultConcersions = {} } = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        delete rawLine.conversionList;
        delete rawLine.conversionInfo;
        delete rawLine.redArray;
        delete rawLine.blackArray;
        delete rawLine.rcjBackup;
        delete rawLine.rcjToRules;
        delete rawLine.deletedRcjToRules;
        delete defaultConcersions[fbFxDeId];
        if (rawLine.appendType) {
            rawLine.appendType = rawLine.appendType.filter((v) => v != "换");
        }
        this.service.yuSuanProject.conversionDeService.resetLineSuffix(rawLine);
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // 删除换算新增的定额
        const delItemBillIds = unitProject.itemBillProjects
            .filter((v) => v.fromConversionRuleId && ruleIds.includes(v.fromConversionRuleId))
            .map((v) => v.sequenceNbr);
        for (const id of delItemBillIds) {
            unitProject.itemBillProjects.removeNode(id);
        }
        const delIds = unitProject.measureProjectTables
            .filter((v) => v.fromConversionRuleId && ruleIds.includes(v.fromConversionRuleId))
            .map((v) => v.sequenceNbr);
        for (const id of delIds) {
            unitProject.measureProjectTables.removeNode(id);
        }
        const historyData = new Map(unitProject.constructProjectRcjs
            .filter((v) => v.deId == fbFxDeId)
            .map((v) => [
            v.standardId,
            {
                ifDonorMaterial: v.ifDonorMaterial,
                donorMaterialNumberManage: v.donorMaterialNumberManage,
                donorMaterialNumber: v.donorMaterialNumber,
            },
        ]));
        // 人材机回退
        this.service.yuSuanProject.rcjProcess.delRcjAndRcjDetailBatch([fbFxDeId], constructId, singleId, unitId);
        let findRes = this.service.yuSuanProject.baseBranchProjectOptionService.findFromAllById(constructId, singleId, unitId, fbFxDeId);
        await this.service.yuSuanProject.rcjProcess.batchSaveRcjData(findRes.item, constructId, singleId, unitId);
        for (const rcj of unitProject.constructProjectRcjs) {
            if (rcj.deId != fbFxDeId)
                continue;
            const history = historyData.get(rcj.standardId);
            if (history) {
                rcj.ifDonorMaterial = history.ifDonorMaterial;
                rcj.donorMaterialNumber = history.donorMaterialNumber;
                rcj.donorMaterialNumberManage = history.donorMaterialNumberManage;
            }
        }
        await this.service.yuSuanProject.rcjProcess.donorMaterialNumberRefresh(constructId, singleId, unitId);
        await new RcjApplicationContext({
            constructId,
            singleId,
            unitId,
            projectObj: PricingFileFindUtils.getProjectObjById(constructId)
        }).deColumnHandle(rawLine);
    }
    /**
     * 记录定额下的人材机
     * @param constructId
     * @param singleId
     * @param unitId
     * @param deId
     * @returns {Promise<{constructProjectRcjs: Array<ConstructProjectRcj>, rcjDetailList: Array<RcjDetails>}>}
     */
    async recordRcj(constructId, singleId, unitId, deId) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let constructProjectRcjs = unit.constructProjectRcjs;
        let rcjDetailList = unit.rcjDetailList;
        if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
            constructProjectRcjs = constructProjectRcjs.filter((i) => i.deId == deId);
        }
        if (!ObjectUtils.isEmpty(rcjDetailList)) {
            rcjDetailList = rcjDetailList.filter((i) => i.deId == deId);
        }
        return {
            constructProjectRcjs: constructProjectRcjs,
            rcjDetailList: rcjDetailList,
        };
    }
    /**
     * 使用记录
     * @param constructId
     * @param singleId
     * @param unitId
     * @param deId
     * @param promise
     */
    async useRecordRcj(constructId, singleId, unitId, deId, promise) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let constructProjectRcjs = unit.constructProjectRcjs;
        let rcjDetailList = unit.rcjDetailList;
        let moduleData = PricingFileFindUtils.getModuleData(constructId, singleId, unitId, deId);
        let t1 = moduleData.find((i) => i.sequenceNbr == deId);
        if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
            constructProjectRcjs = constructProjectRcjs.filter((i) => i.deId == deId);
        }
        if (!ObjectUtils.isEmpty(rcjDetailList)) {
            rcjDetailList = rcjDetailList.filter((i) => i.deId == deId);
        }
        let constructProjectRcjs1 = promise.constructProjectRcjs;
        if (ObjectUtils.isEmpty(constructProjectRcjs1)) {
            return;
        }
        for (let constructProjectRcjs1Element of constructProjectRcjs1) {
            if (!ObjectUtils.isEmpty(constructProjectRcjs1Element.consumerResQty)) {
                let t = constructProjectRcjs.find((i) => i.materialCode == constructProjectRcjs1Element.materialCode);
                if (!ObjectUtils.isEmpty(t)) {
                    t.resQty = constructProjectRcjs1Element.resQty;
                    t.totalNumber = NumberUtil.numberScale4(NumberUtil.multiply(Number(t.resQty), t1.quantity));
                    t.total = NumberUtil.numberScale2(NumberUtil.multiply(t.totalNumber, t.marketPrice));
                }
            }
        }
    }
    /**
     * kind =3 b 删除定额
     * @param constructId
     * @param singleId
     * @param unitId
     * @param deid
     */
    async cleanKind3bDe(constructId, singleId, unitId, deid) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let itemBillProjects = unit.itemBillProjects;
        let measureProjectTables = unit.measureProjectTables;
        let moduleData;
        let type = true;
        moduleData = itemBillProjects.find((k) => k.sequenceNbr === deid);
        if (ObjectUtils.isEmpty(moduleData)) {
            moduleData = measureProjectTables.find((k) => k.sequenceNbr === deid);
            type = false;
        }
        if (ObjectUtils.isEmpty(moduleData)) {
            return;
        }
        if (ObjectUtils.isEmpty(moduleData.createDeId)) {
            return;
        }
        if (type) {
            await this.service.yuSuanProject.itemBillProjectOptionService.removeLine(constructId, singleId, unitId, {
                sequenceNbr: moduleData.createDeId,
                parentId: moduleData.parentId,
                kind: "04",
            }, false);
        }
        else {
            await this.service.yuSuanProject.stepItemCostService.removeLine(constructId, singleId, unitId, {
                sequenceNbr: moduleData.createDeId,
                parentId: moduleData.parentId,
                kind: "04",
            }, false);
        }
        moduleData.createDeId = null;
    }
    /**
     * 标准换算列表
     * @param standardDeId 国标定额id
     * @param fbFxDeId 分部分项定额id
     * @param libraryCode 定额册编码
     * @param constructId
     * @param singleId
     * @param unitId
     */
    async conversionRuleList(args) {
        return await this.standardConvertList(args);
        // 入参 7188546426811449344
        const { standardDeId, fbFxDeId, libraryCode, constructId, singleId, unitId, } = args;
        //查 定额和换算规则 的关系
        /*let deRuleRelation = await this.baseDeRuleRelationService.selectRelationByDeId(standardDeId);
        if (ObjectUtils.isEmpty(deRuleRelation) || ObjectUtils.isEmpty(deRuleRelation.relationGroupCode)) {
            Log.error("定额对应的规则关联关系为空或规则组编码relationGroupCode为空,定额id:", standardDeId);
            return [];
        }*/
        if (!standardDeId || !libraryCode)
            return [];
        // 根据定额和换算规则的关系查规则明细
        // let baseRuleDetailList = await this.baseRuleDetailsService.ByRelationGroupCode(deRuleRelation.relationGroupCode);
        const sqlRes = await (`${libraryCode}`.startsWith("2022")
            ? this.baseRuleDetailFull2022Repo
            : this.baseRuleDetailFullRepo).find({
            where: {
                deId: standardDeId,
            },
            order: {
                sequenceNbr: "ASC",
            },
        });
        // 规则详情列表
        const baseRuleDetailList = SqlUtils.convertToModel(sqlRes).map((item) => {
            //数仓可能有字符串为Null的问题  在这里再做一次过滤
            for (const key in item) {
                if (item[key] == "NULL")
                    item[key] = null;
            }
            return item;
        });
        // 组装查询参数
        // 批量查规则文件 by规则文件ids
        // 规则文件列表
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        const ruleFileIdList = baseRuleDetailList
            .filter((v) => !ObjectUtils.isEmpty(v))
            .map((v) => v.fileDetailsId);
        const baseRuleFileList = ruleFileIdList.length > 0
            ? await this.baseRuleFileDetailsRepo.findBy({
                sequenceNbr: In(ruleFileIdList),
            })
            : [];
        // 查换算规则操作记录（在内存或ysf文件中查规则的操作记录）
        let conversionRuleOperationRecordList = this.conversionRuleOperationRecordService.getDeConversionRuleOperationRecord(constructId, singleId, unitId, fbFxDeId);
        if (!conversionRuleOperationRecordList ||
            conversionRuleOperationRecordList.length == 0) {
            if (Array.isArray(unitProject.conversionInfoList)) {
                const target = unitProject.conversionInfoList.filter((f) => f.deId == fbFxDeId);
                if (target)
                    conversionRuleOperationRecordList = target;
            }
        }
        //  给规则明细填充数据：填充规则文件及是否选中
        baseRuleDetailList.forEach((i) => {
            // 填充规则文件
            if (!ObjectUtils.isEmpty(baseRuleFileList)) {
                if (!ObjectUtils.isEmpty(i.fileDetailsId)) {
                    i.ruleFile = baseRuleFileList.find((j) => i.fileDetailsId === j.sequenceNbr);
                }
            }
            if (!ObjectUtils.isEmpty(conversionRuleOperationRecordList)) {
                let history = conversionRuleOperationRecordList.find((c) => c.ruleId == i.sequenceNbr);
                if (history) {
                    const data = {};
                    switch (+i.kind
                    // case 1:
                    //     data.selected = history.selected
                    //     break;
                    // case 2:
                    //     data.ruleInfo = history.description;
                    //     /*i.selected = kind2Selected.description;*/
                    //     data.selectedRuleGroup = history.selectedRuleGroup;
                    // default:
                    //     data.selectedRule = history.selectedRule;
                    //     break;
                    ) {
                    }
                    Object.assign(i, data);
                    // if (i.kind == 1) {
                    //     let selected = conversionRuleOperationRecordList.find(c => c.ruleId == i.sequenceNbr);
                    //     if (selected) {
                    //         i.selected = true;
                    //     }
                    // } else if (i.kind == 2) {
                    //     let kind2Selected = conversionRuleOperationRecordList.find(c => c.ruleId == i.sequenceNbr);
                    //     if (kind2Selected) {
                    //         i.ruleInfo = kind2Selected.description;
                    //         /*i.selected = kind2Selected.description;*/
                    //         i.selectedRuleGroup = kind2Selected.selectedRuleGroup;
                    //     }
                    // } else {
                    //     let kind3Selected = conversionRuleOperationRecordList.find(c => c.ruleId == i.sequenceNbr);
                    //     i.selectedRule = kind3Selected.selectedRule;
                    // }
                }
            }
            if (i.kind == 2) {
                i.defaultValue = i.relation;
                let standId = i.rcjId;
                let rcjs = this.service.yuSuanProject.rcjProcess.getRcjListByDeId(fbFxDeId, constructId, singleId, unitId);
                let res = rcjs.filter((f) => f.standardId === standId);
                if (res && res.length > 0) {
                    i.selected = res[0].materialName;
                    i.ruleInfo = res[0].materialName;
                    i.beGray = false;
                    if (!i.selectedRuleGroup) {
                        i.selectedRuleGroup = i.topGroupType;
                    }
                }
                else {
                    i.beGray = true;
                }
            }
        });
        if (baseRuleDetailList && baseRuleDetailList.length > 0) {
            baseRuleDetailList.forEach((f) => {
                if (f.kind == 1) {
                    f.defaultValue = "-";
                }
                if (!f.selectedRule && f.selectedRule != 0) {
                    // f.selectedRule = f.defaultValue;
                }
            });
        }
        // 给规则列表的行数据中增加fbFxDeId, 用于处理ysf文件时和BS交互
        baseRuleDetailList.forEach((i, index) => {
            i.fbFxDeId = fbFxDeId;
            i.index = index;
        });
        if (unitProject.conversionRuleSeqNbrSortIndex &&
            Array.isArray(unitProject.conversionRuleSeqNbrSortIndex[fbFxDeId])) {
            const sortedList = baseRuleDetailList
                .sort((a, b) => {
                const aIndex = unitProject.conversionRuleSeqNbrSortIndex[fbFxDeId].findIndex((i) => i === a.sequenceNbr);
                const bIndex = unitProject.conversionRuleSeqNbrSortIndex[fbFxDeId].findIndex((i) => i === b.sequenceNbr);
                return aIndex - bIndex;
            })
                .map((v, index) => {
                v.index = index;
                return v;
            });
            return sortedList;
        }
        return baseRuleDetailList;
    }
    // 标准换算列表
    async standardConvertList(args) {
        const { standardDeId, fbFxDeId, libraryCode, constructId, singleId, unitId, } = args;
        const { line: rawLine, belong: type } = this.service.yuSuanProject.baseBranchProjectOptionService.findLineOnlyById(fbFxDeId);
        //let is2022 = `${libraryCode}`.startsWith("2022");
        let is2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        if (!rawLine || rawLine.kind != "04" || !standardDeId)
            return [];
        if (!rawLine.conversionList || rawLine.conversionList.length <= 0) {
            const list = await (is2022
                ? this.baseRuleDetailFull2022Repo
                : this.baseRuleDetailFullRepo).find({
                where: {
                    deId: standardDeId,
                },
                order: {
                    sortNoGlobal: "ASC",
                },
            });
            //处理数据库中数据未NULL 值
            if (ObjectUtils.isNotEmpty(list)) {
                for (const de of list) {
                    if (de.relationGroupName == "NULL") {
                        de.relationGroupName = null;
                    }
                    if (de.relationGroupRule == "NULL") {
                        de.relationGroupRule = null;
                    }
                    if (de.relationGroupId == "NULL") {
                        de.relationGroupId = null;
                    }
                }
            }
            let baseRcjService = is2022 ? this.service.yuSuanProject.baseRcj2022Service : this.service.yuSuanProject.baseRcjService;
            let rcjIds = list.filter((v) => v.rcjId != "0").map((v) => v.rcjId);
            let rcjObjs = await baseRcjService.getRcjListByRcjIdList(rcjIds);
            let rcjObjMap = new Map();
            rcjObjs.forEach((v) => rcjObjMap.set(v.sequenceNbr, v));
            rawLine.conversionList = list.map((v, index) => {
                var _a, _b, _c, _d, _e, _f;
                const conversionListItem = new conversionListItem_1.ConversionListItem();
                return Object.assign(conversionListItem, v, {
                    isSelect: false,
                    value: v.defaultValue,
                    selectValue: v.defaultValue,
                    index,
                    // kind3 前端渲染KEY
                    selectedRule: v.kind == "3" ? v.defaultValue : null,
                    // kind2 前端渲染KEY
                    ruleInfo: v.kind == "2" ? v.relation : null,
                    // kind1 前端渲染KEY
                    selected: v.kind == "1" ? false : null,
                    defaultValue: v.kind == "1" ? "-" : v.kind == "2" ? v.relation : v.defaultValue,
                    selectedRuleGroup: v.topGroupType,
                    currentRcjCode: (_a = rcjObjMap.get(v.rcjId)) === null || _a === void 0 ? void 0 : _a.materialCode,
                    currentRcjLibraryCode: (_b = rcjObjMap.get(v.rcjId)) === null || _b === void 0 ? void 0 : _b.libraryCode,
                    defaultRcjCode: (_c = rcjObjMap.get(v.rcjId)) === null || _c === void 0 ? void 0 : _c.materialCode,
                    defaultRcjLibraryCode: (_d = rcjObjMap.get(v.rcjId)) === null || _d === void 0 ? void 0 : _d.libraryCode,
                    defaultRcjName: (_e = rcjObjMap.get(v.rcjId)) === null || _e === void 0 ? void 0 : _e.materialName,
                    defaultRcjSpecification: (_f = rcjObjMap.get(v.rcjId)) === null || _f === void 0 ? void 0 : _f.specification,
                });
            });
        }
        let deRcjList = null;
        let resultRules = [];
        for (let rule of rawLine.conversionList) {
            if (rule.kind == "2") {
                if (ObjectUtils.isEmpty(deRcjList)) {
                    deRcjList = this.service.yuSuanProject.rcjProcess.getRcjListByDeId(fbFxDeId, constructId, singleId, unitId);
                }
                // 兼容历史版本
                if (rule.ruleInfo == rule.defaultValue) {
                    rule.currentRcjCode = rule.defaultRcjCode;
                    rule.currentRcjLibraryCode = rule.defaultRcjLibraryCode;
                }
                let index = deRcjList.findIndex(rcj => (rcj.materialCode == rule.currentRcjCode || rcj.materialCode.startsWith(rule.currentRcjCode + "#")) && rcj.libraryCode == rule.currentRcjLibraryCode);
                if (index < 0) {
                    continue;
                }
                rule.ruleInfoShow = ObjectUtils.isEmpty(rule.currentRcjCode) ? rule.ruleInfo : rule.currentRcjCode + " " + rule.ruleInfo;
            }
            resultRules.push(rule);
        }
        return resultRules;
    }
    /**
     * 定额标准换算勾选及下拉框点击
     * @param fbFxDeId 分部分项定额id
     * @param baseRuleDetails 规则明细数据行
     * @param constructId
     * @param singleId
     * @param unitId
     */
    async operationalConversionRule(fbFxDeId, baseRuleDetails, constructId, singleId, unitId, clpb, consumerInput) {
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let excuteRule = {};
        excuteRule.ruleId = baseRuleDetails.sequenceNbr;
        // 规则id
        let ruleId = baseRuleDetails.sequenceNbr;
        let saveNote;
        if (baseRuleDetails.kind == 1) {
            excuteRule.kind = 1;
            // 是否选中 true是 false否
            let selected = baseRuleDetails.selected;
            // 判断是选中还是未选中
            if (selected) {
                excuteRule.type = "caculate";
                // 选中
                // 添加换算操作记录
                this.conversionRuleOperationRecordService.addOperationRecord(constructId, singleId, unitId, fbFxDeId, ruleId);
                // 添加换算信息  前端用
                this.conversionInfoService.addConversionInfo(constructId, singleId, unitId, fbFxDeId, baseRuleDetails);
            }
            else {
                excuteRule.type = "back";
                // 未选中（未选中即取消选中）
                // 删除换算操作记录
                this.conversionRuleOperationRecordService.deleteOperationRecord(constructId, singleId, unitId, fbFxDeId, ruleId);
                // 删除对应的换算信息  前端用
                this.conversionInfoService.deleteConversionInfo(constructId, singleId, unitId, fbFxDeId, ruleId);
            }
            excuteRule.math = baseRuleDetails.math;
        }
        else if (baseRuleDetails.kind == 2) {
            excuteRule.kind = 2;
            excuteRule.type = "caculate";
            excuteRule.math =
                baseRuleDetails.rcjId + "^" + clpb.libraryCode + ":" + clpb.detailsCode;
            let note = this.conversionRuleOperationRecordService.addOperationRecord(constructId, singleId, unitId, fbFxDeId, ruleId); // 换算信息
            note.description = clpb.details;
            note.selectedRuleGroup = clpb.groupName;
            if (!unitProject.conversionInfoList) {
                unitProject.conversionInfoList = [];
            }
            else {
                unitProject.conversionInfoList = unitProject.conversionInfoList.filter((f) => f.ruleId !== baseRuleDetails.sequenceNbr);
            }
            let sql = "select material_code as mcode from base_rcj where sequence_nbr = ?";
            let sqlRes = this.app.betterSqlite3DataSource
                .prepare(sql)
                .get(baseRuleDetails.rcjId);
            let oriCode = sqlRes.mcode;
            unitProject.conversionInfoList.push({
                sequenceNbr: Snowflake.nextId(),
                ruleId: baseRuleDetails.sequenceNbr,
                deId: fbFxDeId,
                source: "标准换算",
                conversionString: "H" + oriCode + " " + clpb.detailsCode,
                conversionExplain: "换算材料:" + clpb.details, // 用来记kind2的查看
            });
            let item = this.service.yuSuanProject.baseBranchProjectOptionService.findFromAllById(constructId, singleId, unitId, fbFxDeId).item;
            /* if (!item.appendType) {
                 item.appendType = [];
             } else {
                 item.appendType = item.appendType.filter(f=>f!="换");
             }
             item.appendType.push("换");*/
            if (item.bdName) {
                if (!item.bdName.endsWith(clpb.details)) {
                    item.bdName += "\n";
                    item.bdName += clpb.details;
                }
            }
            else {
                if (!item.name.endsWith(clpb.details)) {
                    item.name += "\n";
                    item.name += clpb.details;
                }
            }
        }
        else if (baseRuleDetails.kind == 3) {
            excuteRule.kind = 3;
            excuteRule.type = "caculate";
            excuteRule.oriRule = baseRuleDetails;
            excuteRule.consumerInput = consumerInput;
            // 记录
            let note = this.conversionRuleOperationRecordService.addOperationRecord(constructId, singleId, unitId, fbFxDeId, ruleId); // 换算信息
            // note.description = clpb.details;
            // 展示用的那个记录
            if (!unitProject.conversionInfoList) {
                unitProject.conversionInfoList = [];
            }
            else {
                saveNote = unitProject.conversionInfoList.filter((f) => f.deId == fbFxDeId && f.ruleId === baseRuleDetails.sequenceNbr);
                if (saveNote && saveNote.length > 0) {
                    saveNote = saveNote[0];
                }
                // unitProject.conversionInfoList = unitProject.conversionInfoList.filter(f=>f.deId == fbFxDeId && f.ruleId !== baseRuleDetails.sequenceNbr);
            }
            if (!saveNote) {
                saveNote = {
                    sequenceNbr: Snowflake.nextId(),
                    ruleId: baseRuleDetails.sequenceNbr,
                    deId: fbFxDeId,
                    source: "标准换算",
                    consumerInput: consumerInput,
                    conversionString: baseRuleDetails.math,
                    conversionExplain: baseRuleDetails.math, // 用来记kind3的查看
                };
            }
            unitProject.conversionInfoList.push(saveNote);
            // 分类
            if (baseRuleDetails.type === "a") {
                excuteRule.kindType = "a";
                excuteRule.defVal = baseRuleDetails.defaultValue;
            }
            if (baseRuleDetails.type === "b") {
                excuteRule.kindType = "b";
            }
            if (baseRuleDetails.type === "c") {
                excuteRule.kindType = "c";
            }
            if (baseRuleDetails.type === "d") {
                excuteRule.kindType = "d";
            }
            if (baseRuleDetails.type === "e") {
                excuteRule.kindType = "e";
            }
            if (baseRuleDetails.type === "f") {
                excuteRule.kindType = "f";
            }
            excuteRule.math = baseRuleDetails.math;
        }
        await this.caculateRcj(constructId, singleId, unitId, fbFxDeId, excuteRule, saveNote);
        let allData = this.service.yuSuanProject.rcjProcess.reCaculateDeRcjs(constructId, singleId, unitId, fbFxDeId); //改了定额工程量重算人材机合计数量 合价
        this.service.yuSuanProject.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, fbFxDeId, true, allData);
        return true;
    }
    reSetRcjActiveStatu(excuteRule, infRcjs) {
        if (excuteRule.type === "back") {
            for (let i = 0; i < infRcjs.length; ++i) {
                if (infRcjs[i].ruleDeActive &&
                    infRcjs[i].ruleDeActive[excuteRule.ruleId]) {
                    delete infRcjs[i].ruleDeActive[excuteRule.ruleId];
                }
            }
        }
        if (excuteRule.defVal == excuteRule.consumerInput) {
            for (let i = 0; i < infRcjs.length; ++i) {
                if (infRcjs[i].ruleDeActive &&
                    infRcjs[i].ruleDeActive[excuteRule.ruleId]) {
                    delete infRcjs[i].ruleDeActive[excuteRule.ruleId];
                }
            }
        }
    }
    async caculateRcj(constructId, singleId, unitId, fbFxDeId, excuteRule, saveNote) {
        let convertRules = this.convertRule(excuteRule, saveNote);
        let infRcjs = [];
        for (let i = 0; i < convertRules.length; ++i) {
            // 遍历规则
            let res = await this.excuteRule(constructId, singleId, unitId, convertRules[i], PricingFileFindUtils.getUnit(constructId, singleId, unitId), fbFxDeId, saveNote); // 对所有人材机执行规则
            infRcjs = infRcjs.concat(res);
        }
        // 算完后做记录
        let backs = [];
        for (let i = 0; i < convertRules.length; ++i) {
            let cr = convertRules[i];
            let singleBack = { "+": "-", "*": "/", "-": "+", "/": "*" };
            let backSingle = singleBack[cr.math.substring(0, 1)];
            let backVal = eval(cr.math.substring(1));
            let bacmMath = backSingle + backVal;
            backs.push(bacmMath);
        }
        saveNote.kind3Backs = backs;
        let infRcjIds = {};
        for (let i = 0; i < infRcjs.length; ++i) {
            infRcjIds[infRcjs[i].sequenceNbr] = true;
        }
        saveNote.infRcjIds = infRcjIds;
        this.reSetRcjActiveStatu(excuteRule, infRcjs);
        // 换
        let deLine = this.service.yuSuanProject.rcjProcess.findDeByDeId(constructId, singleId, unitId, fbFxDeId);
        let nowRcjs = this.service.yuSuanProject.rcjProcess.queryRcjDataByDeId(fbFxDeId, constructId, singleId, unitId);
        let baseRcjs = this.service.yuSuanProject.rcjProcess.getBaseRcjInfoByDeId(deLine.standardId);
        deLine.appendType = deLine.appendType.filter((a) => a !== "换");
        if (!this.isSameRcj(baseRcjs, nowRcjs)) {
            if (!deLine.appendType) {
                deLine.appendType = [];
            }
            else {
                deLine.appendType = deLine.appendType.filter((f) => f !== "换");
            }
            deLine.appendType.push("换");
        }
    }
    isSameRcj(rcjArrayA, rcjArrayB) {
        if (!Array.isArray(rcjArrayA) || !Array.isArray(rcjArrayB)) {
            return false;
        }
        if (rcjArrayA.length !== rcjArrayB.length) {
            return false;
        }
        let aMap = {};
        for (let i = 0; i < rcjArrayA.length; ++i) {
            let str = "" +
                Number.parseFloat(rcjArrayA[i].resQty).toFixed(6) +
                rcjArrayA[i].materialCode +
                rcjArrayA[i].materialName +
                rcjArrayA[i].specification +
                rcjArrayA[i].kind +
                rcjArrayA[i].unit;
            aMap[str] = 1;
        }
        let bMap = {};
        for (let i = 0; i < rcjArrayB.length; ++i) {
            let str = "" +
                Number.parseFloat(rcjArrayB[i].resQty).toFixed(6) +
                rcjArrayB[i].materialCode +
                rcjArrayB[i].materialName +
                rcjArrayB[i].specification +
                rcjArrayB[i].kind +
                rcjArrayB[i].unit;
            let isBExistInA = aMap[str];
            if (!isBExistInA) {
                return false;
            }
            bMap[str] = 1;
        }
        for (let i = 0; i < rcjArrayA.length; ++i) {
            let str = "" +
                Number.parseFloat(rcjArrayA[i].resQty).toFixed(6) +
                rcjArrayA[i].materialCode +
                rcjArrayA[i].materialName +
                rcjArrayA[i].specification +
                rcjArrayA[i].kind +
                rcjArrayA[i].unit;
            let isAExistInB = bMap[str];
            if (!isAExistInB) {
                return false;
            }
        }
        return true;
    }
    async excuteRule(constructId, singleId, unitId, convertRule, unit, fbFxDeId, saveNote) {
        if (convertRule.type == 1) {
            //类型是1的规则
            return this._excuteKind1Rule(unit, fbFxDeId, convertRule);
        }
        else if (convertRule.type == 2) {
            //类型是2的规则
            return await this._excuteKind2Rule(constructId, singleId, unitId, unit, fbFxDeId, convertRule);
        }
        else if (convertRule.type == 3) {
            if (convertRule.kindType == "a") {
                return this._excuteKind3ARule(unit, fbFxDeId, convertRule, saveNote);
            }
            if (convertRule.kindType == "b") {
                return this._excuteKind3BRule(unit, fbFxDeId, convertRule);
            }
            if (convertRule.kindType == "c") {
                return this._excuteKind3CRule(unit, fbFxDeId, convertRule);
            }
            if (convertRule.kindType == "d") {
                return this._excuteKind3DRule(unit, fbFxDeId, convertRule);
            }
        }
    }
    _excuteKind3DRule(unit, fbFxDeId, convertRule) {
        let infRcjs = [];
        for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
            let rcj = unit.constructProjectRcjs[i];
            if (rcj.deId == fbFxDeId) {
                // 如果对于本规则，人材机是未激活状态 则不执行
                if (rcj.ruleDeActive && rcj.ruleDeActive[convertRule.ruleId]) {
                    continue;
                }
                // 正常执行
                if (!convertRule.find.attr) {
                    // *num形式的规则
                    rcj.resQty = NumberUtil.numberScale(eval(rcj.resQty + convertRule.math), 6);
                    infRcjs.push(rcj);
                    continue;
                }
                if (rcj[convertRule.find.attr] == convertRule.find.val ||
                    this.rcjKindConvert[rcj[convertRule.find.attr]] ===
                        convertRule.find.val) {
                    // RCJ，code
                    rcj.resQty = NumberUtil.numberScale(eval(rcj.resQty + convertRule.math), 6);
                    infRcjs.push(rcj);
                }
            }
        }
        return infRcjs;
    }
    async _excuteKind2Rule(constructId, singleId, unitId, unit, fbFxDeId, convertRule) {
        let infRcjs = [];
        for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
            let orgRcj = unit.constructProjectRcjs[i];
            if (orgRcj.deId == fbFxDeId) {
                if (orgRcj.standardId === convertRule.findStandId) {
                    // 删除本人材机的配合比
                    unit.rcjDetailList = unit.rcjDetailList.filter((f) => f.rcjId !== orgRcj.sequenceNbr);
                    // 替换人材机
                    let sql = "select sequence_nbr as sid from base_rcj where library_code = ? and material_code = ?";
                    let baseIndex = this.app.betterSqlite3DataSource
                        .prepare(sql)
                        .get(convertRule.toLib, convertRule.toCode).sid;
                    let { rcj, pb } = await this.service.yuSuanProject.rcjProcess.addRcjLineOnOptionMenu(constructId, singleId, unitId, baseIndex, fbFxDeId);
                    let oriStand = unit.constructProjectRcjs[i].standardId;
                    unit.constructProjectRcjs[i] = rcj;
                    unit.constructProjectRcjs[i].standardId = oriStand;
                    // 存入配合比
                    unit.rcjDetailList.concat(pb);
                    infRcjs.push(unit.constructProjectRcjs[i]);
                }
            }
        }
    }
    _excuteKind3CRule(unit, fbFxDeId, convertRule) {
        // c,s,u
        let constructId = ParamUtils.getPatram("commonParam").constructId;
        let singleId = ParamUtils.getPatram("commonParam").singleId;
        let unitId = ParamUtils.getPatram("commonParam").unitId;
        // 1 查fbFxDeId 得到定额行 并得出是fbfx 还是 csxm
        let deInfo = this.service.yuSuanProject.baseBranchProjectOptionService.findLineOnlyById(fbFxDeId);
        let deLine = deInfo.line;
        let type = deInfo.belong;
        // 2.根据根据定额id查询人材机
        let sql = "select *\n" +
            "from base_rcj\n" +
            "where sequence_nbr in (select rcj_id from base_de_rcj_relation where quota_id = ?)";
        let sqlRes = this.app.betterSqlite3DataSource
            .prepare(sql)
            .all(convertRule.newDe);
        let baseRcjs = SqlUtils.convertToModel(sqlRes);
        // 3.对新人才记得 resQty执行math
        for (let i = 0; i < baseRcjs.length; ++i) {
            baseRcjs[i].resQty = NumberUtil.numberScale(eval(baseRcjs[i].resQty + convertRule.math), 6);
            baseRcjs[i].isBorrow = true;
            baseRcjs[i].deId = fbFxDeId;
            baseRcjs[i].sequenceNbr = Snowflake.nextId();
            // todo 处理合计数量，合价
        }
        // 4.将新的人材机添加到当前定额下
        unit.constructProjectRcjs = unit.constructProjectRcjs.concat(baseRcjs);
        return [];
    }
    _excuteKind3BRule(unit, fbFxDeId, convertRule) {
        // c,s,u
        let constructId = ParamUtils.getPatram("commonParam").constructId;
        let singleId = ParamUtils.getPatram("commonParam").singleId;
        let unitId = ParamUtils.getPatram("commonParam").unitId;
        // 1 查fbFxDeId 得到定额行 并得出是fbfx 还是 csxm
        let deInfo = this.service.yuSuanProject.baseBranchProjectOptionService.findLineOnlyById(fbFxDeId);
        let deLine = deInfo.line;
        let type = deInfo.belong;
        // 2.根据 fbfx 还是 csxm 新增数据
        let sql = "select * from base_de where sequence_nbr = ?";
        let sqlRes = this.app.betterSqlite3DataSource
            .prepare(sql)
            .all(convertRule.newDe);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        let newDataInfo;
        if (type == "fbfx") {
            newDataInfo = this.service.yuSuanProject.itemBillProjectOptionService.fillDataFromIndexPage(constructId, singleId, unitId, deLine, "04", convertRes[0].sequenceNbr, convertRes[0].unit);
        }
        else {
            newDataInfo = this.service.yuSuanProject.stepItemCostService.fillDataFromIndexPage(constructId, singleId, unitId, deLine, "04", convertRes[0].sequenceNbr, convertRes[0].unit);
        }
        // 3.对新数据的的人材机进行计算
        let newRcjs = PricingFileFindUtils.getRcjList(constructId, singleId, unitId).filter((f) => f.deId === newDataInfo.data.sequenceNbr);
        if (newRcjs && newRcjs.length > 0) {
            newRcjs.forEach((rcj) => {
                rcj.resQty = NumberUtil.numberScale(eval(rcj.resQty + convertRule.math), 6);
            });
        }
        // 4.新数据和当前数据进行关联
        deLine.createDeId = newDataInfo.data.sequenceNbr;
        newDataInfo.data.relationDeId = deLine.sequenceNbr;
        // 5.处理新定额的工程量表达式
        newDataInfo.quantityExpression = "GLZDE";
        newDataInfo.quantityExpressionNbr = deLine.quantityExpressionNbr;
        let unitNum = Number.parseInt(newDataInfo.unit);
        if (Number.isNaN(unitNum) || 0 == unitNum) {
            unitNum = 1;
        }
        newDataInfo.quantity = newDataInfo.quantityExpressionNbr / unitNum;
        return [];
    }
    _excuteKind3ARule(unit, fbFxDeId, convertRule, saveNote) {
        let infRcjs = [];
        for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
            let rcj = unit.constructProjectRcjs[i];
            if (rcj.deId == fbFxDeId) {
                if (!rcj.lastResQty) {
                    rcj.lastResQty = rcj.resQty;
                }
                // 如果对于本规则，人材机是未激活状态 则不执行
                if (rcj.ruleDeActive && rcj.ruleDeActive[convertRule.ruleId]) {
                    continue;
                }
                // 如果当前规则被执行过，则先反算
                if (saveNote.infRcjIds[rcj.sequenceNbr]) {
                }
                // 正常执行
                rcj.resQty = rcj.lastResQty;
                rcj.resQty = NumberUtil.numberScale(eval(rcj.resQty + convertRule.math), 6);
                infRcjs.push(rcj);
            }
        }
        return infRcjs;
    }
    _excuteKind1Rule(unit, fbFxDeId, convertRule) {
        let infRcjs = [];
        for (let i = 0; i < unit.constructProjectRcjs.length; ++i) {
            let rcj = unit.constructProjectRcjs[i];
            if (rcj.deId == fbFxDeId) {
                // 如果对于本规则，人材机是未激活状态 则不执行
                if (rcj.ruleDeActive && rcj.ruleDeActive[convertRule.ruleId]) {
                    continue;
                }
                // 正常执行
                if (!convertRule.find.attr) {
                    // *num形式的规则
                    rcj.resQty = NumberUtil.numberScale(eval(rcj.resQty + convertRule.math), 6);
                    infRcjs.push(rcj);
                    continue;
                }
                if (rcj[convertRule.find.attr] == convertRule.find.val ||
                    this.rcjKindConvert[rcj[convertRule.find.attr]] ===
                        convertRule.find.val) {
                    // RCJ，code
                    rcj.resQty = NumberUtil.numberScale(eval(rcj.resQty + convertRule.math), 6);
                    infRcjs.push(rcj);
                }
            }
        }
        return infRcjs;
    }
    convertRule(excuteRule, saveNote) {
        let convertRules = [];
        let excuteKind = excuteRule.kind;
        let excuteType = excuteRule.type;
        let kindMath = excuteRule.math;
        if (excuteKind == 1) {
            // 类型1 有两种表达式  R*1.2  ； HAE1-0011 AE1-0011 0
            let rules = kindMath.split(",");
            for (let i = 0; i < rules.length; ++i) {
                let oneRule = rules[i];
                convertRules.push(this.doConvertKind1Rule(excuteRule.sequenceNbr, oneRule, excuteType));
            }
        }
        else if (excuteKind == 2) {
            convertRules.push(this.doConvertKind2Rule(excuteRule));
        }
        else if (excuteKind == 3) {
            let rules = kindMath.split(",");
            for (let i = 0; i < rules.length; ++i) {
                let oneRule = rules[i];
                convertRules.push(this.doConvertKind3Rule(excuteRule, oneRule));
            }
        }
        return convertRules;
    }
    doConvertKind3Rule(excuteRule, oneRule) {
        let bdRule = excuteRule.oriRule;
        if (bdRule.type == "a") {
            return this._convertKind3A(bdRule, excuteRule);
        }
        if (bdRule.type == "b") {
            return this._convertKind3b(bdRule, excuteRule);
        }
        if (bdRule.type == "c") {
            return this._convertKind3c(bdRule, excuteRule);
        }
        if (bdRule.type == "d") {
            return this._convertKind3d(bdRule, excuteRule, oneRule);
        }
    }
    _convertKind3d(bdRule, excuteRule, oneRule) {
        let findType, findAttr, math;
        // 两种格式 ED1-0041 +169*(V-4)，ZA1-0002 +0.042*(V-4)  R*(1+(RU(V-30))*3%)，J*(1+(RU(V-30))*3%);
        let splitR = oneRule.split(" ");
        if (splitR.length > 0) {
            // 是  编码 算式
            findType = "materialCode";
            findAttr = oneRule.split(" ")[0];
            math = oneRule.split(" ")[1];
        }
        else {
            let single = oneRule.match("[*+-/]")[0];
            let singleIndex = oneRule.match("[*+-/]").index;
            if (singleIndex == 0) {
                // 是 *Num格式
                findType = undefined;
                findAttr = undefined;
                math = oneRule.substring(singleIndex);
            }
            else {
                // 是 （R/C/J）*Num格式
                findType = "kind";
                findAttr = oneRule.substring(0, singleIndex);
                math = oneRule.substring(singleIndex);
            }
        }
        // 替换math中的数据
        // 替换 ru rd v
        let rega = new RegExp("\\b\\V\\b", "g");
        let regb = new RegExp("\\b(RU)\\b", "g");
        let regc = new RegExp("\\b(RD)\\b", "g");
        let regd = new RegExp("\\b(RD)\\b", "g");
        let convertMath = math.replace(rega, excuteRule.consumerInput);
        convertMath = convertMath.replace(regb, "Math.ceil");
        convertMath = convertMath.replace(regc, "Math.floor");
        convertMath = convertMath.replace(regd, "*0.01");
        // 处理 ^
        if (convertMath.indexOf("^") > 0) {
            let params = convertMath.split("^");
            params[0] = params[0].substring(1);
            convertMath = "*" + "Math.pow(" + params[0] + " , " + params[1] + ")";
        }
        return {
            type: 3,
            ruleId: bdRule.sequenceNbr,
            find: {
                // 用于标识怎么找人材机
                attr: findType,
                val: findAttr,
            },
            math: convertMath, // 用于对人材机的消耗量进行计算
        };
    }
    _convertKind3c(bdRule, excuteRule) {
        let oriMath = bdRule.math;
        let rega = new RegExp("\\b\\V\\b", "g");
        let regb = new RegExp("\\b(RU)\\b", "g");
        let regc = new RegExp("\\b(RD)\\b", "g");
        let convertMath = oriMath.replace(rega, excuteRule.consumerInput);
        convertMath = convertMath.replace(regb, "Math.ceil");
        convertMath = convertMath.replace(regc, "Math.floor");
        return {
            type: 3,
            kindType: "b",
            newDe: oriMath.relationDeId,
            math: convertMath, // 用于对人材机的消耗量进行计算
        };
    }
    _convertKind3b(bdRule, excuteRule) {
        let oriMath = bdRule.math;
        let rega = new RegExp("\\b\\V\\b", "g");
        let regb = new RegExp("\\b(RU)\\b", "g");
        let regc = new RegExp("\\b(RD)\\b", "g");
        let convertMath = oriMath.replace(rega, excuteRule.consumerInput);
        convertMath = convertMath.replace(regb, "Math.ceil");
        convertMath = convertMath.replace(regc, "Math.floor");
        return {
            type: 3,
            kindType: "b",
            newDe: oriMath.relationDeId,
            math: convertMath, // 用于对人材机的消耗量进行计算
        };
    }
    _convertKind3A(bdRule, excuteRule) {
        let oriMath = bdRule.math;
        // 处理 ，n = XXX
        if (oriMath.indexOf("n=") > 0) {
            let splitMath = oriMath.split("，");
            let mathStr = splitMath[0];
            let nstr = splitMath[1].substring(2);
            let regn = new RegExp("\\b(n)\\b", "g");
            oriMath = mathStr.replace(regn, nstr);
        }
        // 替换 ru rd v
        let rega = new RegExp("\\b\\V\\b", "g");
        let regb = new RegExp("\\b(RU)\\b", "g");
        let regc = new RegExp("\\b(RD)\\b", "g");
        let convertMath = oriMath.replace(rega, excuteRule.consumerInput);
        convertMath = convertMath.replace(regb, "Math.ceil");
        convertMath = convertMath.replace(regc, "Math.floor");
        // 处理 ^
        if (convertMath.indexOf("^") > 0) {
            let params = convertMath.split("^");
            params[0] = params[0].substring(1);
            convertMath = "*" + "Math.pow(" + params[0] + " , " + params[1] + ")";
        }
        return {
            type: 3,
            kindType: "a",
            math: convertMath, // 用于对人材机的消耗量进行计算
        };
    }
    doConvertKind2Rule(excuteRule) {
        // excuteRule.math = 被转换的rcj标砖id+"-"+要转换的人材机库+":"+要转换的人材机编码;
        let findCode = excuteRule.math.split("^")[0];
        let toInfo = excuteRule.math.split("^")[1];
        let toLib = toInfo.split(":")[0];
        let toCode = toInfo.split(":")[1];
        return {
            type: 2,
            findStandId: findCode,
            toLib: toLib,
            toCode: toCode,
        };
    }
    doConvertKind1Rule(ruleId, oneRule, excuteKind) {
        let singleBack = { "+": "-", "*": "/", "-": "+", "/": "*" };
        // R*1.2  或者 HAE1-0011 AE1-0011 0
        let findType, findAttr;
        let math;
        if (oneRule.indexOf(" ") == -1) {
            // 是 （R/C/J）+-*/ (num)
            let single = oneRule.match("[*+-/]")[0];
            let singleIndex = oneRule.match("[*+-/]").index;
            if (singleIndex == 0) {
                // 是 *Num格式
                findType = undefined;
                findAttr = undefined;
                let mathSingle = excuteKind == "caculate" ? single : singleBack[single];
                math = mathSingle + oneRule.substring(singleIndex + 1);
            }
            else {
                findType = "kind";
                findAttr = oneRule.substring(0, singleIndex);
                let mathSingle = excuteKind == "caculate" ? single : singleBack[single];
                math = mathSingle + oneRule.substring(singleIndex + 1);
            }
        }
        else {
            // 是 Hcode code ?Num
            findType = "materialCode";
            findAttr = oneRule.split(" ")[1];
            let orgMath = oneRule.split(" ")[2];
            let mathRes = orgMath.match("[*+-/]");
            let single, num;
            if (!mathRes) {
                single = "+";
                num = orgMath;
            }
            else {
                single = mathRes[0];
                num = orgMath.substring(mathRes.index + 1);
            }
            single = excuteKind == "caculate" ? single : singleBack[single];
            math = single + num;
        }
        return {
            type: 1,
            excuteKind: excuteKind,
            ruleId: ruleId,
            find: {
                // 用于标识怎么找人材机
                attr: findType,
                val: findAttr,
            },
            math: math, // 用于对人材机的消耗量进行计算
        };
    }
}
ConversionDeProcess.toString = () => "[class ConversionDeProcess]";
module.exports = ConversionDeProcess;
//# sourceMappingURL=conversionDeProcess.js.map