"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseListJobContentQuota2022 = void 0;
const typeorm_1 = require("typeorm");
const typeorm_2 = require("typeorm");
/**
 * 清单指引 2022
 */
let BaseListJobContentQuota2022 = class BaseListJobContentQuota2022 {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code_list", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "libraryCodeList", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "list_code", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "listCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "list_id", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "listId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "job_content", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "jobContent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quota_id", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "quotaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quota_code", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "quotaCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "quota_name", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "quotaName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit", nullable: true }),
    __metadata("design:type", String)
], BaseListJobContentQuota2022.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "price", nullable: true }),
    __metadata("design:type", Number)
], BaseListJobContentQuota2022.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sort_no", nullable: true }),
    __metadata("design:type", Number)
], BaseListJobContentQuota2022.prototype, "sortNo", void 0);
BaseListJobContentQuota2022 = __decorate([
    (0, typeorm_2.Entity)({ name: "base_list_job_content_quota_2022" })
], BaseListJobContentQuota2022);
exports.BaseListJobContentQuota2022 = BaseListJobContentQuota2022;
//# sourceMappingURL=BaseListJobContentQuota2022.js.map