<!--
 * @@Descripttion: 
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-11 11:02:37
-->
<template>
  <div
    class="table-content"
    :style="`height:${store.currentTreeInfo.levelType<3?'calc(100% - 40px)':'100%'}`"
  >
    <!-- <p class="title"><span class="text">人材机服务</span></p> -->
    <p :class="store.currentTreeInfo.levelType<3?'selectTabNo2':'selectTab'">
      <a-radio-group
        v-model:value="activeKey"
        :style="{ marginBottom: '8px' }"
      >
        <a-radio-button
          :value="item.key"
          v-for="item of activeOptions"
        >{{ item.tab }}</a-radio-button>
      </a-radio-group>
      <!-- <span class="showTitle">
        {{ showInfo?.materialName }}
      </span> -->
    </p>
    <p class="searchTab">
      <span class="label">
        {{activeKey===2?'推荐数据类型：':'地区：'}}

      </span>
      <!-- 信息价增加二级县区 -->
      <a-cascader
        v-if="activeKey===0"
        size="small"
        :allowClear="false"
        v-model:value="regionList[activeKey].selectValue.cityVal"
        placeholder="请选择地区"
        :options="regionList[activeKey].cityList"
        style="max-width: 181px"
        @change="handleChange('city',regionList[activeKey].selectValue.cityVal)"
      />
      <a-select
        v-else
        ref="select"
        size="small"
        v-model:value="regionList[activeKey].selectValue.cityVal"
        style="width: 120px"
        @change="handleChange('city',regionList[activeKey].selectValue.cityVal)"
      >
        <a-select-option
          v-for="item in regionList[activeKey].cityList"
          :value="item"
        >{{ item }}</a-select-option>
      </a-select>
      <span
        class="label"
        style="margin-left:20px"
      >
        期数：
      </span>
      <a-select
        size="small"
        ref="select"
        v-model:value="regionList[activeKey].selectValue.dateVal"
        style="width: 120px"
        @change="handleChange('month')"
      >
        <a-select-option
          :value="data"
          v-for="data in regionList[activeKey].showDateList"
        >{{ data }}</a-select-option>
      </a-select>
      <a-input-search
        size="small"
        v-model:value="searchName"
        :maxlength="50"
        :placeholder="'请输入名称查询'"
        class="searchTab-input"
        :class="!searchName?'noAllow':''"
        @search="onSearch"
      />
    </p>
    <div
      class="content"
      v-if="isOnline"
    >
      <div
        class="content-leftTree"
        v-if="!isContract"
      >
        <p class="content-leftTree-title">
          所有材料类别
        </p>
        <a-tree
          v-model:expandedKeys="expandedKeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeData"
          @select="selectChildren"
          style="z-index: 10;margin-left: 5px;"
          :show-line="true"
          :show-icon="false"
        >
          <template #icon><carry-out-outlined /></template>
          <template #title="{ dataRef ,key ,node}">
            <span :class="hisSelect===dataRef.name?'color_light':''">
              {{ dataRef.name }}
            </span>
          </template>
          <template #switcherIcon="{ dataRef, defaultIcon }">
            <component :is="defaultIcon" />
          </template>
        </a-tree>

      </div>
      <div
        class="content-leftTitle"
        v-if="isContract"
      >
        <p class="content-leftTitle-title">所有材料类别</p>
      </div>
      <div class="content-rightTable">
        <p
          class="content-rightTable-select"
          v-if='activeKey == "0" || activeKey == "1"'
        >
          <a-radio-group
            button-style="solid"
            v-model:value="selectTableType"
            :style="{ height: '30px'}"
            @change="changeGrid"
          >
            <a-radio-button
              :value="item.key"
              v-for="item of typeOptions"
            >{{ item.tab }}</a-radio-button>
          </a-radio-group>
        <div
          v-if='selectTableType == "2"'
          style="margin-left: 20px;display: flex;align-items: center;"
        >
          <div style="width: 70px;">加权规则：</div>
          <a-dropdown
            v-model:visible="isOpenShow"
            style='min-width: 340px;max-width: 450px;'
            :trigger="['click']"
          >
            <div
              class="ant-dropdown-link"
              @click.prevent
            >
              <a-button
                @click.prevent
                :title="ruleStr"
                style="display: flex;align-items: center;height: 28px"
                @click="weightedRuleBox"
              >
                <div
                  style="max-width: 380px;"
                  class="surely-table-cell-text-ellipsis"
                >{{ ruleStr }}</div>
                <icon-font
                  style="margin-left: 5px;margin-top: 2px;"
                  type="icon-bianji2"
                  class="iconType"
                />
              </a-button>
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item style="padding: 6px 5px 0;min-width: 320px;height: 340px;">
                  <weightedRulePopup
                    :key="activeKey"
                    ref="weightedRulePopupRef"
                    :showDateList="(regionList[activeKey].showDateList||[]).map(item => ({date:item}))"
                    @selectedRow="selectedRow"
                  >
                    <!-- 样式占位 -->
                    <template #default>
                      <span></span>
                    </template>
                  </weightedRulePopup>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        </p>
        <div :style="{ height: activeKey === '2' ? '100%' : 'calc(100% - 28px)' }">
          <vxe-grid
            class="trends-table-column"
            v-bind="gridOptions"
            ref="rightTable"
            height="100%"
            :min-height="0"
            style="overflow-x: auto;margin-right: 4px"
            v-on="gridEvents"
            :loading="tableLoading"
            :scroll-x="{enabled:true}"
            :scroll-y="{enabled:true}"
          >
            <template #empty>
              <span style="
            color: #898989;
            font-size: 14px;
            display: block;
            margin: 25px 0;
          ">
                <img
                  :src="getUrl('newCsProject/none.png')"
                  style="margin: auto"
                />
              </span>
            </template>
          </vxe-grid>
        </div>
      </div>
      <div
        class="btnExpand"
        :style="{ left: isContract ? '2%' : '24.8%' }"
      >
        <div
          class="btn"
          @click.stop="contractHandle"
        >
          <img
            :src="isContract ? getUrl('expandnew.png') : getUrl('retractnew.png')"
            alt=""
          />
        </div>
      </div>
    </div>
    <div
      class="content"
      v-if="!isOnline"
    >
      <img
        :src="getUrl('newCsProject/none.png')"
        style="margin: auto;
"
      />
    </div>
  </div>
  <unitConvert
    v-if="unitConvertVisible"
    @closeDialog="closeUnitConvert"
    :priceInfo="postData"
    :isDiffDeType="true"
    ref="unitConvertRef"
  />
  <common-modal
    v-if="modelValue"
    className="dialog-comm"
    title="设置显示平均价"
    width="550"
    v-model:modelValue="modelValue"
    @close="cancel"
    :mask="true"
  >
    <weightedRulePopup
      ref="weightedRuleRef"
      :isShow="false"
      :showDateList="(regionList[activeKey].showDateList||[]).map(item => ({date:item}))"
    />
    <div style="margin-top: 10px;text-align: center">
      <a-button @click="cancelHandle">取消</a-button>
      <a-button
        type="primary"
        @click="sureHandle()"
        :disabled="!(Array.isArray(regionList[activeKey].showDateList) &&
                                                        regionList[activeKey].showDateList.length)"
        style="margin-left: 20px;"
      >确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { defineAsyncComponent, ref, watch, onMounted, reactive, toRaw, onActivated, nextTick } from 'vue';
import feePro from '@/api/feePro';
import loadPrice from '@/api/loadPrice';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber, getUrl } from '@/utils/index';
import { message } from 'ant-design-vue';
import { CarryOutOutlined, SmileTwoTone } from '@ant-design/icons-vue';
import * as aes from '@/utils/aes/public.js';
import weightedRulePopup from './components/weightedRulePopup.vue';
import infoMode from '@/plugins/infoMode.js';
import api from '@/api/loadPrice.js';
import { columnWidth } from '@/hooks/useSystemConfig';
const unitConvert = defineAsyncComponent(() => import('@/components/unitConvert.vue'));
const unitConvertRef = ref(null);

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } = useCellClick();
const emits = defineEmits(['getUpList', 'upDateMarketPrice']);
const store = projectDetailStore();
let tableData = ref([]);
let activeKey = ref(0); //0-信息价   1-市场价   2-推荐价
let selectTableType = ref('1'); //1-显示本期价格   2-显示平均价格
let treeData = ref([]); //左侧树数据
let expandedKeys = ref(['0-0', '0-0-0']);
let unitConvertVisible = ref(false);
let selectedKeys = ref(['0-0']);
let isContract = ref(false); // 是否收缩
let selectInfo = reactive({
  city: '1',
  month: '1',
  cityList: [{ name: '西安', id: '1' }],
  monthList: [{ name: '2023-3', id: '1' }],
});
onMounted(async () => {
  console.log(props.showInfo, 'onMounted');
  searchName.value = props.showInfo?.materialName;
  await getCityList();
  await getMonthAndCity();
  // await getSomeData(false, true);
  await getLoadPriceCache();
});
//每次进来必需要要执行调取
onActivated(async () => {
  await getLoadPriceCache();
});
const activeOptions = reactive([
  {
    key: 0,
    tab: '信息价',
  },
  //暂时隐藏
  {
    key: 1,
    tab: '市场价',
  },
  {
    key: 2,
    tab: '推荐价',
  },
]);
const typeOptions = reactive([
  {
    key: '1',
    tab: '显示本期价格',
  },
  {
    key: '2',
    tab: '显示平均价格',
  },
]);
const props = defineProps(['showInfo', 'tableData']);
let isOnline = ref(true);

watch(
  () => props.showInfo,
  () => {
    if (props.showInfo?.materialName) {
      searchName.value = props.showInfo?.materialName;
      getSomeData(true, true);
    } else {
      searchName.value = null;
      getSomeData(true, false);
    }
  }
);
watch(
  () => activeKey.value,
  () => {
    if (store.tabSelectName === '人材机汇总') {
      //侧边栏数据变化重新更新
      expandedKeys.value = ['0-0', '0-0-0'];
      changeGrid(searchName.value ? true : false);
    }
  }
);

let cityAllList = reactive([]);
const getCityList = async () => {
  await loadPrice.getDimRegion({}).then(res => {
    if (res.status === 200) {
      let list = JSON.parse(aes.decrypt(res.result));
      list.map(a => {
        a.label = a.name;
        a.value = a.name;
        a?.children?.map(b => {
          b.label = b.name;
          b.value = b.name;
        });
      });
      //王浩和产品确认过-过滤河北省（河北省无日期数据）批量载价也是
      cityAllList = list.filter(a => a.code !== '130000');
      console.log('cityAllList', cityAllList);
    }
  });
};
const getSomeData = async (isChange = false, isSearch = false) => {
  //isChange = false   true时是切换日期的地区不需要重新获取列表
  //isSearch   true---首次加载默认查询选中数据
  nextTick(async () => {
    searchName.value = props.showInfo?.materialName;
    let isOnlineFlag = await feePro.isOnline();
    isOnline.value = isOnlineFlag.result ? true : false;
    if (isOnline.value) {
      //有网的话查询列表数据
      if (!isChange) await getTreeData();
      await changeGrid(isSearch);
    }
  });
};
let searchName = ref(null);
const onSearch = async () => {
  if (!searchName.value) return;
  let isOnlineFlag = await feePro.isOnline();
  if (isOnlineFlag.result) {
    // 判断当前 1-显示本期价格   2-显示平均价格
    if (selectTableType.value === '1') {
      getGridData(true);
    } else if (selectTableType.value === '2') {
      getZtzjRcjAvg(true);
    }
  } else {
    gridOptions.data = [];
  }
  console.log('searchName', searchName.value);
};
const getMonthAndCity = async () => {
  let apiData = getParamsData({});
  await loadPrice.queryLoadPriceAreaDate(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('queryLoadPriceAreaDate', res.result);
      for (let citys in res.result) {
        if (citys === '信息价') {
          let cityAll = Object.keys(res.result[citys]);
          regionList.value[0].cityList = cityAllList;
          regionList.value[0].selectValue.cityVal = [
            regionList.value[0].cityList[0].value,
            regionList.value[0].cityList[0].children[0].value,
          ];
        }
        setSelectList(citys, res.result);
      }
    }
  });
};
let regionList = ref([
  {
    title: '信息价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: [],
      dateVal: null,
    },
  },
  {
    title: '市场价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: null,
      dateVal: null,
    },
  },
  {
    title: '推荐价',
    cityList: [],
    dateList: [],
    showDateList: [],
    selectValue: {
      cityVal: null,
      dateVal: null,
    },
  },
]);
const setSelectList = (item, total) => {
  regionList.value.map(i => {
    if (i.title === item) {
      if (i.title !== '信息价') {
        i.cityList = Object.keys(total[item]);
      }
      i.dateList = total[item];
      if (i.title !== '信息价') {
        i.selectValue.cityVal = i.cityList[0];
        i.showDateList = i.dateList[i.selectValue.cityVal];
      } else {
        i.showDateList = i.dateList[i.selectValue.cityVal[i.selectValue.cityVal.length - 1]] || [];
      }
      i.selectValue.dateVal = (i.showDateList && i.showDateList[0]) || null;
    }
  });
  console.log('regionList.value', regionList.value);
};
// 回显能力添加
const getLoadPriceCache = async () => {
  try {
    const res = await loadPrice.getLoadPriceCache({
      constructId: store.currentTreeGroupInfo?.constructId,
    });
    if (res.status === 200 && res.result) {
      if ('type1' in res.result && res.result.type1.type == 1) {
        // regionList.value[0].selectValue.cityVal = ['',res.result.type1.areaName];
        regionList.value[0].selectValue.dateVal = res.result.type1.yearMonths;
      }
      if ('type2' in res.result && res.result.type2.type == 2) {
        regionList.value[1].selectValue.cityVal = res.result.type2.areaName;
        regionList.value[1].selectValue.dateVal = res.result.type2.yearMonths;
      }
      if ('type3' in res.result && res.result.type3.type == 3) {
        regionList.value[2].selectValue.cityVal = res.result.type3.areaName;
        regionList.value[2].selectValue.dateVal = res.result.type3.yearMonths;
      }
    }
    for (let index = 0; index < cityAllList.length; index++) {
      if (cityAllList[index]?.children.find(i => i.name === res.result?.type1.areaName)) {
        regionList.value[0].selectValue.cityVal = [
          cityAllList[index].name,
          res.result.type1.areaName,
        ];
        break;
      }
    }
    console.log('loadPrice.getLoadPriceCache', res.result);
  } catch (e) {
    console.error('loadPrice.getLoadPriceCache错误', e);
  } finally {
    await getTreeData();
    if (store.currentTreeInfo.levelType < 3) {
      changeGrid(searchName.value ? true : false);
    }
  }
};

const handleChange = async (type, target) => {
  if (type === 'city') {
    isOpenShow.value = false; // 切换时默认关闭平均价弹框
    ruleStr.value = ''; //清空默认的显示项

    if (activeKey.value === 0) {
      // debugger;
      console.log('target', target);
      console.log(regionList.value[0]);
      regionList.value[0].showDateList = regionList.value[0].dateList[target[target.length - 1]];
      regionList.value[0].selectValue.dateVal =
        regionList.value[0].showDateList && regionList.value[0].showDateList[0];
    } else {
      regionList.value[activeKey.value].showDateList =
        regionList.value[activeKey.value].dateList[target];
      regionList.value[activeKey.value].selectValue.dateVal =
        regionList.value[activeKey.value].showDateList[0];
    }
  }
  selectTableType.value = '1';
  ruleStr.value = '';
  await changeGrid(searchName.value ? true : false);
  console.log('regionList.value', regionList.value);
};
let tableLoading = ref(false);
const gridOptions = reactive({
  headerAlign: 'center',
  showOverflow: true,
  autoResize: true,
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
    height: 30,
  },
  columns: [],
  data: [],
  align: 'center',
});
let postData = ref(null);
const closeUnitConvert = val => {
  unitConvertVisible.value = false;
  if (val) {
    postData.value = {
      ...val,
      loadPrice: {
        ...val.loadPrice,
        marketPrice: val.marketPriceAfter,
        loadPriceFinally: val.marketPriceAfter,
      },
    };
    zjFun(postData.value.loadPrice);
  }
  console.log(postData.value);
};
const startZJ = async ({ row }) => {
  // 测试提优化，如果人材机表格中无数据，则下方表格中点击事件不生效
  if (Array.isArray(props.tableData) && !props.tableData.length) {
    return null;
  }
  if (props.showInfo?.noLoadPrice === 1) {
    message.warning('该行数据不支持载价，请重新选择');
    return;
  }
  console.log(row, props.showInfo, 'startZJ');
  if (row.unit !== props.showInfo.unit) {
    let res = null;
    // 是否存在默认系数，如果存在，则默认计算
    try {
      console.log('loadPrice.queryUnitConversion=>res', {
        desc1: props.showInfo.unit,
        desc2: row.unit,
      });
      // 传参：desc1 之前单位 ;传参：desc2 之后单位
      postData.value = {
        loadPrice: { ...row },
        beforeUnit: row.unit,
        nowUnit: props.showInfo.unit,
        marketPrice:
          activeKey.value === 0 &&
          Number(store.deStandardReleaseYear) === 22 &&
          Number(store?.taxMade) === 1
            ? 'notIncludingTaxMarketPrice' in row
              ? row.notIncludingTaxMarketPrice
              : row.notIncludingTaxMarketPriceAvg
            : 'marketPrice' in row
            ? row.marketPrice
            : row.marketPriceAvg,
        marketPriceAfter: '',
        sourcePrice: row.sourcePrice,
        num: null,
      };
      res = await loadPrice.queryUnitConversion({
        desc1: props.showInfo.unit,
        desc2: row.unit,
      });
      console.log('loadPrice.queryUnitConversion=>res', res);
      if (res.code === 200 && res.result) {
        postData.value.num = res.result;
      }
    } catch (e) {
      console.error('loadPrice.queryUnitConversion', e);
    } finally {
      unitConvertVisible.value = true;
    }
    return;
  } else {
    (row.loadPriceFinally =
      activeKey.value === 0 &&
      Number(store.deStandardReleaseYear) === 22 &&
      Number(store?.taxMade) === 1
        ? 'notIncludingTaxMarketPrice' in row
          ? row.notIncludingTaxMarketPrice
          : row.notIncludingTaxMarketPriceAvg
        : 'marketPrice' in row
        ? row.marketPrice
        : row.marketPriceAvg),
      zjFun(row);
  }
};
const zjFun = row => {
  let apiData = {
    rcj: toRaw(props.showInfo),
    searchRcj: toRaw(row),
    type: store.currentTreeInfo.levelType,
  };
  if (store.currentTreeInfo.levelType === 3) {
    apiData.searchRcj.loadPrice = row.loadPriceFinally;
  }
  apiData = getParamsData(apiData);
  console.log('startZJ---useZtzjRcj', apiData);
  loadPrice.useZtzjRcj(apiData).then(res => {
    if (res.status === 200) {
      console.log('startZJ---接收结果useZtzjRcj', res);
      if (store.currentTreeInfo.levelType === 3) {
        emits('getUpList');
      } else {
        // if (activeKey.value === 0 && Number(store?.taxMade) === 1) {
        //   row.marketPrice = row.notIncludingTaxMarketPrice;
        // }
        row.marketPrice = row.loadPriceFinally;
        emits('upDateMarketPrice', row);
      }
    }
  });
};
const gridEvents = ref({
  cellDblclick: startZJ,
});

let modelValue = ref(false);
let ruleStr = ref(''); //'2024年9月*2+2024年8月*3）/ 5'
let isOpenShow = ref(false); // 加权规则是否显示
const weightedRulePopupRef = ref();
const weightedRuleRef = ref();
const avgRuleList = ref([]); // 加权规则缓存
// 加权弹框内部确认按钮回调
const selectedRow = async rowList => {
  console.log(rowList, 'selectedRow');
  isOpenShow.value = false;
  let list = weightedRulePopupRef.value.tableEdit.getCheckboxRecords();
  await sureHandle(list);
  // await getZtzjRcjAvg();
};
// 获取显示平均价接口
const getZtzjRcjAvg = async (isSearch = false) => {
  console.log(activeKey.value);
  let list = [];
  if (Array.isArray(avgRuleList.value) && avgRuleList.value.length > 0) {
    avgRuleList.value.forEach(item => {
      list.push({
        field: `${item.yearMonths}(含税)`,
        title: `${item.yearMonths}(含税)`,
        width: 120,
      });
      list.push({
        field: `${item.yearMonths}(不含税)`,
        title: `${item.yearMonths}(不含税)`,
        width: 130,
      });
    });
  }
  if (activeKey.value == 0 && selectTableType.value == 2) {
    //信息价页面:   marketPriceAvg:含税平均价   notIncludingTaxMarketPriceAvg:不含税平均价xxx(含税)xxx(不含税)
    gridOptions.columns = [
      { type: 'seq', title: '序号', width: 70 },
      { field: 'materialName', title: '材料名称', minWidth: 200 },
      { field: 'specification', title: '规格型号', width: 120 },
      { field: 'unit', title: '单位', width: 70 },
      {
        field: 'marketPriceAvg',
        title: '含税加权平均价',
        width: 120,
      },
      {
        field: 'notIncludingTaxMarketPriceAvg',
        title: '不含税加权平均价',
        width: 130,
      },
      ...list,
    ];
  } else if (activeKey.value == 1 && selectTableType.value == 2) {
    //市场价页面:marketPriceAvg:平均价xxx(含税)xxx(不含税)
    gridOptions.columns = [
      { type: 'seq', title: '序号', width: 70 },
      { field: 'materialName', title: '材料名称', minWidth: 200 },
      { field: 'specification', title: '规格型号', width: 120 },
      { field: 'unit', title: '单位', width: 70 },
      {
        field: 'marketPriceAvg',
        title: '加权平均价',
        width: 120,
      },
      ...list,
    ];
  }
  gridOptions.columns = gridOptions.columns.map(item => {
    if (item.width) item.width = columnWidth(item.width);
    if (item.minWidth) item.minWidth = columnWidth(item.minWidth);
    return item;
  });
  const apiData = parameterHandle(isSearch) || {}; // 公用参数
  apiData.areaName =
    activeKey.value === 0
      ? regionList.value[activeKey.value].selectValue.cityVal[
          regionList.value[activeKey.value].selectValue.cityVal.length - 1
        ]
      : regionList.value[activeKey.value].selectValue.cityVal;
  apiData.tabType = activeKey.value == 0 ? '1' : '2';
  try {
    tableLoading.value = true;
    console.log('loadPrice.getZtzjRcjAvg==>参数', apiData);
    let res = await loadPrice.getZtzjRcjAvg(apiData);
    console.log('loadPrice.getZtzjRcjAvg==>返回值', res);
    if (res.status == 200) {
      gridOptions.data = Array.isArray(res.result) ? res.result : [];
    }
  } catch (e) {
    console.error('getZtzjRcjAvg=>参数错误', e);
  } finally {
    tableLoading.value = false;
  }
};
// 二次编辑时
const weightedRuleBox = async () => {
  let res = await getAvgRule(); // 获取平均价的回显接口
  setTimeout(() => {
    console.log(res, 'weightedRuleBox');
    if (Array.isArray(res) && res.length > 0) {
      weightedRulePopupRef.value.feedbackDateList(res);
    } else {
      weightedRulePopupRef.value.feedbackDateList([]);
    }
  }, 100);
};
// 平均价规则 获取查询
const getAvgRule = async () => {
  try {
    console.log('loadPrice.getAvgRule==>参数', {
      constructId: store.currentTreeGroupInfo?.constructId,
      areaName:
        activeKey.value === 0
          ? regionList.value[activeKey.value].selectValue.cityVal[
              regionList.value[activeKey.value].selectValue.cityVal.length - 1
            ]
          : regionList.value[activeKey.value].selectValue.cityVal,
      tabType: activeKey.value == 0 ? '1' : '2',
    });
    let res = await loadPrice.getAvgRule({
      constructId: store.currentTreeGroupInfo?.constructId,
      areaName:
        activeKey.value === 0
          ? regionList.value[activeKey.value].selectValue.cityVal[
              regionList.value[activeKey.value].selectValue.cityVal.length - 1
            ]
          : regionList.value[activeKey.value].selectValue.cityVal,
      tabType: activeKey.value == 0 ? '1' : '2',
    });
    console.log('loadPrice.getAvgRule==>返回值', res);
    if (res.status === 200) {
      avgRuleList.value = res.result;
      if (Array.isArray(res.result) && res.result.length > 0) {
        let resultString = '',
          down = 0;
        res.result.forEach((current, index) => {
          resultString += `${current.yearMonths}*${current.up}+`;
          down += parseInt(current.up);
        });
        ruleStr.value = `(${resultString.replace(/\+$/, '')})/${down}`;
      } else {
        ruleStr.value = '';
      }
    }
    return res.result;
  } catch (e) {
    console.error('getAvgRule=>参数错误', e);
  }
};
// 首次显示平均价的弹框 确认按钮
const sureHandle = async (list = weightedRuleRef.value.tableEdit.getCheckboxRecords()) => {
  // modelValue.value = false;
  if (Array.isArray(list)) {
    let loadPriceMessageList = list.map((item, index) => {
      return {
        areaName:
          activeKey.value === 0
            ? regionList.value[activeKey.value].selectValue.cityVal[
                regionList.value[activeKey.value].selectValue.cityVal.length - 1
              ]
            : regionList.value[activeKey.value].selectValue.cityVal,
        yearMonths: item.date,
        up: item.up,
        down: item.down,
      };
    });
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      loadPriceMessageList: loadPriceMessageList,
      areaName:
        activeKey.value === 0
          ? regionList.value[activeKey.value].selectValue.cityVal[
              regionList.value[activeKey.value].selectValue.cityVal.length - 1
            ]
          : regionList.value[activeKey.value].selectValue.cityVal,
      tabType: activeKey.value == 0 ? '1' : '2',
    };
    try {
      console.log('loadPrice.saveAvgRule==>参数', apiData);
      let res = await loadPrice.saveAvgRule(apiData);
      console.log('loadPrice.saveAvgRule==>返回值', res);
      if (res.status == 200) {
        message.success('数据保存成功');
        await getAvgRule(); // 获取平均价的回显接口
        await getZtzjRcjAvg(true); // 获取平均价的表格数据接口
        modelValue.value = false;
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.error('数据保存失败', e);
      message.error('数据保存失败');
    }
  } else {
    message.info('请设置平均价信息！');
  }
};
//弹窗 取消
const cancelHandle = () => {
  if (
    !(
      Array.isArray(regionList.value[activeKey.value].showDateList) &&
      regionList.value[activeKey.value].showDateList.length
    )
  ) {
    selectTableType.value = '1';
    modelValue.value = false; // 切换时默认关闭弹框
  } else {
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-tishineirong',
      infoText: '请选择加权规则后进行确认操作',
      confirm: async () => {
        selectTableType.value = '1';
        // modelValue.value = false;// 切换时默认关闭弹框
        infoMode.hide();
      },
      close: () => {
        infoMode.hide();
      },
    });
  }
};
//弹框右上角关闭
const cancel = () => {
  console.log('cancel');
  selectTableType.value = '1';
  modelValue.value = false; // 切换时默认关闭弹框
};
// 参数公用
const parameterHandle = (isSearch = false) => {
  let apiData = {};
  apiData = {
    type: activeKey.value + 1,
    areaName:
      activeKey.value === 0
        ? regionList.value[activeKey.value].selectValue.cityVal[
            regionList.value[activeKey.value].selectValue.cityVal.length - 1
          ]
        : regionList.value[activeKey.value].selectValue.cityVal,
    yearMonths: regionList.value[activeKey.value].selectValue.dateVal,
    specification: props.showInfo.specification,
  };
  tableLoading.value = true;
  if (isSearch) {
    apiData.materialName = searchName.value;
  } else {
    console.log('selectedKeys', selectedKeys.value);
    if (selectedKeys.value.length > 0) {
      let str = selectedKeys.value[0].slice(2);
      let arr = str.split('-');
      let obj = [...treeData.value];
      let list = [];
      arr.map(a => {
        list.push(obj[a]?.name);
        obj = obj[a]?.children;
      });
      list.map((i, idx) => {
        apiData[`classlevel0${idx + 1}`] = i;
      });
    }
  }
  apiData = getParamsData(apiData);
  return apiData;
};

const changeGrid = async isSearch => {
  isOpenShow.value = false; // 切换时默认关闭弹框
  if (['0', '1'].includes(String(activeKey.value)) && selectTableType.value === '2') {
    let res = await getAvgRule(); // 获取平均价的回显接口
    console.log('getAvgRule=>返回值', res);
    if (Array.isArray(res) && res.length > 0) {
      modelValue.value = false;
    } else {
      modelValue.value = true; // 切换时默认关闭弹框
      return null;
    }
  }
  if (String(activeKey.value) === '2' || String(selectTableType.value) === '1') {
    gridOptions.columns = [
      { type: 'seq', title: '序号', width: 70 },
      { field: 'materialName', title: '材料名称', minWidth: 200 },
      { field: 'specification', title: '规格型号', width: 120 },
      { field: 'unit', title: '单位', width: 70 },
      {
        field: 'notIncludingTaxMarketPrice',
        title: '不含税市场价',
        width: 120,
        visible: activeKey.value === 0,
      },
      {
        field: 'marketPrice',
        title: activeKey.value === 0 ? '含税市场价' : activeKey.value === 1 ? '工程价' : '推荐价',
        width: 120,
      },
      // { field: 'rate', title: '历史价', width: 100 },
      { field: 'priceDate', title: '报价时间', width: 120 },
    ].map(item => {
      if (item.width) item.width = columnWidth(item.width);
      if (item.minWidth) item.minWidth = columnWidth(item.minWidth);
      return item;
    });
    getGridData(isSearch);
  } else if (String(selectTableType.value) === '2') {
    await getZtzjRcjAvg(isSearch); // 获取平均价的表格数据接口
  }
};
let hisSelect = ref();
const selectChildren = (selectedKeys, { node, event }) => {
  selectedKeys.value = [];
  if (selectedKeys.length == 0) {
    selectedKeys.value = [node.key];
    return;
  } else {
    hisSelect.value = node.dataRef.name;
    selectedKeys.value = selectedKeys;
  }
  // getGridData();
  changeGrid();
};
const getGridData = (isSearch = false) => {
  let apiData = parameterHandle(isSearch) || {}; // 公用参数
  loadPrice
    .getZtzjRcj(apiData)
    .then(res => {
      console.log('getZtzjRcj---res', apiData, res);
      if (res.status === 200 && res.result) {
        gridOptions.data = JSON.parse(res.result);
      } else {
        gridOptions.data = [];
      }
    })
    .finally(() => {
      tableLoading.value = false;
    });
};
const getTreeData = async () => {
  let apiData = {
    type: activeKey.value + 1,
    areaName:
      activeKey.value === 0
        ? regionList.value[activeKey.value].selectValue.cityVal[
            regionList.value[activeKey.value].selectValue.cityVal.length - 1
          ]
        : regionList.value[activeKey.value].selectValue.cityVal,
    yearMonths: regionList.value[activeKey.value].selectValue.dateVal,
    materialName: searchName.value,
  };
  apiData = getParamsData(apiData);
  await loadPrice.getRcjTypeTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log('getRcjTypeTree', res.result, apiData);
      for (let citys in res.result) {
        // setSelectList(citys, res.result);
        treeData.value = res.result;
      }
    }
  });
};
const contractHandle = () => {
  isContract.value = !isContract.value;
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
</script>
<style lang="scss" scoped>
.table-content {
  // height: calc(100% - 40px);
  .title {
    width: 100%;
    background-color: #e7e7e7;
    height: 35px;
    text-align: left;
    margin: 2px 0 0px;
    .text {
      display: inline-block;
      width: 100px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background-color: #f8fbff;
      border-top: 2px solid #4786ff;
    }
  }
  .selectTab,
  .selectTabNo2 {
    background-color: #e7e7e7;
    height: 32px;
    line-height: 30px;
    // padding-left: 20px;
    position: relative;
    border-bottom: 2px solid #e7e7e7;
    margin: 3px 0;
    .label {
      color: grey;
      font-size: 12px;
    }
    .showTitle {
      position: absolute;
      right: 0px;
      top: 0px;
      line-height: 30px;
      height: 30px;
      padding: 0 20px;
      font-size: 12px;
      // background-color: #e7e7e7;
      border-radius: 5px;
    }
  }
  .selectTab {
    .ant-radio-button-wrapper {
      font-size: 12px;
      background-color: #e7e7e7;
      border: none;
      box-shadow: none;
      // border-radius: 5px;
    }
    .ant-radio-button-wrapper-checked {
      // border-color: none;
      background-color: white;
      border: none;
      border-top: 2px solid #4786ff;
      color: black;
      &:hover {
        color: black;
      }
    }
  }
  .selectTabNo2 {
    .ant-radio-button-wrapper {
      font-size: 12px;
      background-color: white;
      border: none;
      box-shadow: none;
      color: black;
    }
    .ant-radio-button-wrapper-checked {
      background-color: #40a9ff;
      border: none;
      color: white;
    }
  }
  .searchTab {
    height: 30px;
    line-height: 30px;
    // padding-left: 20px;
    display: flex;
    // border-bottom: 2px solid #e7e7e7;
    margin-bottom: 0px;
    .label {
      // color: grey;
      font-size: 12px;
      margin: 0 0px 0 5px;
    }
    .ant-select,
    .ant-input,
    .ant-input-group {
      height: 25px !important;
      font-size: 12px !important;
      // color: gray;
      margin: auto 0;
    }
    :deep(.ant-input-group) {
      font-size: 12px !important;
      margin: auto 0;
    }
    &-input {
      width: 250px;
      margin: 2px 0 0 22px;
      height: 25px;
      :deep(.ant-input) {
        font-size: 12px;
        line-height: 22px;
      }
    }
  }
  .content {
    width: 100%;
    position: relative;
    height: calc(100% - 70px);
    display: flex;
    // justify-content: space-between;
    &-leftTree {
      width: 25%;
      border-right: 2px solid #e7e7e7;
      &-title {
        height: 30px;
        background-color: #e7e7e7;
        font-size: 12px;
        font-weight: 700;
        line-height: 30px;
        padding-left: 10px;
      }
    }
    &-leftTitle {
      width: 30px;
      height: 100%;
      margin: 0;
      background-color: #e7e7e7;
      position: relative;
      &-title {
        position: absolute;
        top: 50%;
        left: 50%;
        font-size: 12px;
        font-weight: 700;
        transform: translate(-85%, -50%);
        width: 10px;
        padding: 0;
        height: auto;
        border-bottom: none;
        white-space: normal;
      }
    }
    &-rightTable {
      width: 74%;
      padding-left: 1px;
      margin-left: 10px;
      &-select {
        height: 28px;
        margin-bottom: 3px;
        display: flex;
        .ant-radio-button-wrapper {
          height: 28px;
          line-height: 28px;
          font-size: 12px;
        }
        .ant-radio-button-wrapper-checked {
          // background-color: #f8fbff;
          border-color: #d9d9d9;
        }
        .ant-input-affix-wrapper {
          padding: 0 6px 0 4px;
          height: 28px;
        }
      }
    }
    &:hover .btnExpand .btn {
      display: block;
    }
    .btnExpand {
      position: absolute;
      // left: 24%;
      transition: transform 0.2s;
      top: calc(50% + 30px);
      transform: translateY(-50%);
      width: 14px;
      font-size: 12px;
      height: 80px;
      z-index: 100;
      text-align: center;
      transition: all 0.1s linear;
      cursor: pointer;
      user-select: none;
      .btn {
        text-align: right;
        img {
          width: 10px;
        }
        display: none;
        &:hover {
          display: block;
        }
      }
      span {
        display: inline-block;
        transform: translateX(-1px);
        transition: all 0.4s;
      }
    }
  }
  .ant-radio-button-wrapper::before {
    content: '';
    width: 0;
  }
}
:deep(.ant-tree) {
  height: calc(100% - 40px);
  overflow-y: scroll;
  font-size: var(--project-detail-table-font-size);
  .ant-tree-node-content-wrapper {
    padding: 0 0px;
  }
  .color_light {
    height: 24px;
    padding: 0;
    display: inline-block;
    background-color: #bae7ff;
  }
}
:deep(.noAllow .ant-input-search-button) {
  pointer-events: none;
  cursor: not-allowed;
  background-color: #e7e7e7;
}
</style>
