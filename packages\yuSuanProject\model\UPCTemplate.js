"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UPCTemplate = void 0;
const typeorm_1 = require("typeorm");
const typeorm_2 = require("typeorm");
/**
 * 定额表
 */
let UPCTemplate = class UPCTemplate {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "qf_code", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "standard", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "standard", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sort", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "sort", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "type", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "type_code", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "typeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "code", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "name", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "caculate_base", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "caculateBase", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "desc", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "desc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rate", nullable: true }),
    __metadata("design:type", String)
], UPCTemplate.prototype, "rate", void 0);
UPCTemplate = __decorate([
    (0, typeorm_2.Entity)({ name: "upc_template" })
], UPCTemplate);
exports.UPCTemplate = UPCTemplate;
//# sourceMappingURL=UPCTemplate.js.map