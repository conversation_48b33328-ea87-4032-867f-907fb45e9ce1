"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstructRcjAvgRule = void 0;
const BaseModel_1 = require("./BaseModel");
/**
 *  人材机下的加权平均规则
 */
class ConstructRcjAvgRule extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, areaName, yearMonths, up, down) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.areaName = areaName;
        this.yearMonths = yearMonths;
        this.up = up;
        this.down = down;
    }
}
exports.ConstructRcjAvgRule = ConstructRcjAvgRule;
//# sourceMappingURL=ConstructRcjAvgRule.js.map