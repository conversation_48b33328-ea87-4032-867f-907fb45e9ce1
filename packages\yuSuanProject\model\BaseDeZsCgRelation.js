"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeZsCgRelation2022 = exports.BaseDeZsCgRelation = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 定额表
 */
let BaseDeZsCgRelation = class BaseDeZsCgRelation extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "cg_de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation.prototype, "cgDeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zs_de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation.prototype, "zsDeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "storey", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation.prototype, "storey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "location", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation.prototype, "location", void 0);
BaseDeZsCgRelation = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_zs_cg_relation" })
], BaseDeZsCgRelation);
exports.BaseDeZsCgRelation = BaseDeZsCgRelation;
let BaseDeZsCgRelation2022 = class BaseDeZsCgRelation2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "cg_de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation2022.prototype, "cgDeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zs_de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation2022.prototype, "zsDeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "storey", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation2022.prototype, "storey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "location", nullable: true }),
    __metadata("design:type", String)
], BaseDeZsCgRelation2022.prototype, "location", void 0);
BaseDeZsCgRelation2022 = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_zs_cg_relation_2022" })
], BaseDeZsCgRelation2022);
exports.BaseDeZsCgRelation2022 = BaseDeZsCgRelation2022;
//# sourceMappingURL=BaseDeZsCgRelation.js.map