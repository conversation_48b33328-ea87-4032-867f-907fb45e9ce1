const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {NumberUtil} = require("../utils/NumberUtil");

/**
 * 独立费接口
 */
class GljIndependentCostsController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 保存
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async save(args, redo="插入或编辑- -独立费") {
        const res = await this.service.gongLiaoJiProject.gljIndependentCostsService.save(args);
        return ResponseData.success(res);
    }

    /**
     * 删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async delete(args, redo="删除- -独立费") {
        const res = await this.service.gongLiaoJiProject.gljIndependentCostsService.delete(args);
        return ResponseData.success(res);
    }

    /**
     * 查询
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getList(args) {
        const res = await this.service.gongLiaoJiProject.gljIndependentCostsService.getList(args);
        return ResponseData.success(res);
    }

    /**
     * 独立费-查询人材机数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async insertRcj(args, redo="查询人材机并插入- -独立费") {
        const res = await this.service.gongLiaoJiProject.gljIndependentCostsService.insertRcj(args);
        return ResponseData.success(res);
    }

}

GljIndependentCostsController.toString = () => '[class GljIndependentCostsController]';
module.exports = GljIndependentCostsController;