<!--
 * @@Descripttion:
 * @Author: wangru
 * @Date: 2023-05-18 17:56:43
 * @LastEditors: wangru
 * @LastEditTime: 2025-05-15 14:21:56
-->
<!-- 底部价格面板 -->
<template>
  <div class="foot-content">
    <div class="priceList">
      <span class="title">
        河北13国标清单规范-河北{{ store.deStandardReleaseYear }}定额标准<span v-if="store.currentTreeInfo?.constructMajorType">-</span>{{ store.currentTreeInfo?.constructMajorType }}
        <!-- <span>自动计算</span> -->
      </span>
      <span
        v-for="item in showList"
        :key="item.name"
      >{{ item.name }}={{ item.value }}</span>
    </div>

  </div>
</template>

<script setup>
import { watch, ref } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
const defaultList = [
  {
    name: '工程造价（含设备费及税金）',
    value: 0,
  },
  {
    name: '人工费',
    value: 0,
  },
  {
    name: '机械费',
    value: 0,
  },
  {
    name: '主材费',
    value: 0,
  },
  {
    name: '材料费',
    value: 0,
  },
  {
    name: '管理费',
    value: 0,
  },
  {
    name: '利润',
    value: 0,
  },
  {
    name: '直接工程费',
    value: 0,
  },
];
const props = defineProps({
  priceList: {
    type: Array,
  },
});
let showList = ref([]);
watch(() => {
  if (props.priceList.length === 0) {
    showList.value = defaultList;
  } else {
    showList.value = props.priceList;
  }
});
</script>
<style lang="scss" scoped>
.foot-content {
  // padding: 0 30px;
  display: flex;
  width: 100%;
  align-items: center;
  font-size: 12px;
  height: 33px;
  color: #214d8f;
  span {
    padding-left: 1.5%;
  }
}
.title {
  line-height: 33px;
  span {
    padding-left: 0px;
    margin: 0 5px 0 5px;
  }
}
.priceList {
  width: 100%;
  white-space: nowrap; /* 强制不换行 */
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 1300px) {
  //页面缩小列表减小
  .foot-content {
    font-size: 11px;
  }
}
</style>
