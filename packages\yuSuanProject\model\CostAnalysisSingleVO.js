"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CostAnalysisSingleVO = void 0;
class CostAnalysisSingleVO {
    constructor(average, unitcost, gczj, fbfxhj, csxhj, qtxmhj, gfee, safeFee, jxse, xxse, zzsynse, fjse, sj, sbfsj, levelType, dispNo, projectName, sequenceNbr, childrenList) {
        this.average = average;
        this.unitcost = unitcost;
        this.gczj = gczj;
        this.fbfxhj = fbfxhj;
        this.csxhj = csxhj;
        this.qtxmhj = qtxmhj;
        this.gfee = gfee;
        this.safeFee = safeFee;
        this.jxse = jxse;
        this.xxse = xxse;
        this.zzsynse = zzsynse;
        this.fjse = fjse;
        this.sj = sj;
        this.sbfsj = sbfsj;
        this.levelType = levelType;
        this.dispNo = dispNo;
        this.projectName = projectName;
        this.sequenceNbr = sequenceNbr;
        this.childrenList = childrenList;
    }
}
exports.CostAnalysisSingleVO = CostAnalysisSingleVO;
//# sourceMappingURL=CostAnalysisSingleVO.js.map