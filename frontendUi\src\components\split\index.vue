<template>
  <div
    class="split"
    :class="{ horizontal }"
  >
    <div
      class="sub"
      ref="one"
      :style="topStyle"
      v-if="props.onlyPart !== 'Bootom'"
    >
      <slot name="one"></slot>
    </div>
    <div
      class="resizer"
      @mousedown.stop="startResize"
      v-if="props.onlyPart === 'all' && props.isDrop"
    ></div>
    <div
      class="sub"
      ref="two"
      :style="{ flexGrow: grow2 }"
      v-if="onlyPart !== 'Top'"
    >
      <slot name="two"></slot>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, watchEffect, onActivated } from 'vue';
const props = defineProps({
  horizontal: {
    type: Boolean,
    default: false,
  },
  horizontalTop: {
    type: Number,
    default: 0,
  },
  horizontalBottom: {
    type: Number,
    default: 0,
  },
  minHorizontalTop: {
    //最小设置
    type: Number,
    default: -1,
  },
  maxHorizontalTop: {
    //最大设置
    type: Number,
    default: -1,
  },
  ratio: {
    type: String,
  },
  onlyPart: {
    //只有一项存在  all-有两部分  top只有上部分   bottom-只有下部分
    type: String,
    default: 'all',
  },
  isDrop: {
    //是否可以拖拽
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['onDragHeight']);

const one = ref();
const two = ref();
let [initGrow1, initGrow2] = parseRatio(props.ratio);
// states
let grow1 = ref(initGrow1);
let grow2 = ref(initGrow2);
let topStyle = ref({ flexGrow: grow1 });
onMounted(() => {
  setMinOrMax();
  emits('onDragHeight', one.value?.offsetHeight);
});

onActivated(() => {
  emits('onDragHeight', one.value?.offsetHeight);
});

watch(
  () => grow1.value,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      emits('onDragHeight', one.value?.offsetHeight);
    }
  }
);

const setMinOrMax = () => {
  if (props.isDrop) {
    //可以拖拽
    topStyle.value = {
      flexGrow: grow1,
    };
    props.minHorizontalTop && !props.horizontal
      ? (topStyle.value.minWidth = props.minHorizontalTop + 'px')
      : '';
    props.maxHorizontalTop && !props.horizontal
      ? (topStyle.value.maxWidth = props.maxHorizontalTop + 'px')
      : '';
  } else {
    topStyle.value = {
      flexGrow: grow1,
      width:
        props.minHorizontalTop && !props.horizontal
          ? props.minHorizontalTop + 'px'
          : '', //不可以拖拽的话可以设置宽
    };
    if (props.onlyPart === 'all') {
      //不可拖拽的时候宽度不能变化
      topStyle.value.minWidth = topStyle.value.width + '!important';
      topStyle.value.maxWidth = topStyle.value.width + '!important';
    }
  }
};

watch(
  () => [props.minHorizontalTop, props.maxHorizontalTop],
  () => {
    // console.log('watch', props.minHorizontalTop, props.maxHorizontalTop);
    [initGrow1, initGrow2] = parseRatio(props.ratio);
    grow1.value = initGrow1;
    grow2.value = initGrow2;
    console.log('拖拽-监听--', grow1.value, grow2.value);
    setMinOrMax();
  }
);
// methods
function parseRatio(ratio) {
  // ratio: strings like "1/2"
  const rn = ratio
    ?.split('/')
    ?.map(Number)
    ?.filter(val => !isNaN(val));

  if (!rn || rn.length !== 2) {
    return [1, 1];
  }

  return rn;
}

function startResize(event) {
  // console.log('event+++++++++++++++++++', event, props.onlyPart);
  if (props.onlyPart !== 'all') return;
  // 点击活动条时添加class
  one.value?.classList.add('forbid-select');
  two.value?.classList.add('forbid-select');

  // 如果是竖向split取y，反之取x
  // 取两个dom对应x | y
  const initialPos = props.horizontal ? event.clientY : event?.clientX;
  let sizeOne = props.horizontal
    ? one?.value?.offsetHeight
    : one?.value?.offsetWidth;
  let sizeTwo = props.horizontal
    ? two?.value?.offsetHeight
    : two?.value?.offsetWidth;

  function handleMouseMove(mme) {
    let pos = props.horizontal ? mme.clientY : mme?.clientX;
    if ((props.minHorizontalTop > 0 && pos < props.minHorizontalTop) || pos < 0)
      return;
    if (props.maxHorizontalTop > 0 && pos > props.maxHorizontalTop) return;
    let newSizeOne = sizeOne + pos - initialPos;
    const totalGrow = grow1.value + grow2.value;
    grow1.value = totalGrow * (newSizeOne / (sizeOne + sizeTwo));
    grow2.value = totalGrow - grow1.value;
    if (countGrowToPX(grow2.value, totalGrow, pos) <= props.horizontalBottom) {
      grow2.value = countPXToGrow(props.horizontalBottom, totalGrow, pos);
      grow1.value =
        totalGrow - countPXToGrow(props.horizontalBottom, totalGrow, pos);
    }
    // console.log(countPX(totalGrow - grow1.value, totalGrow, pos), props.horizontalBottom)
  }

  function handleMouseUp(mue) {
    one.value?.classList.remove('forbid-select');
    two.value?.classList.remove('forbid-select');

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
}
function countGrowToPX(grow, totalGrow, clientY) {
  return clientY * (grow / totalGrow);
}
function countPXToGrow(px, totalGrow, clientY) {
  return (px / clientY) * totalGrow;
}
</script>

<style lang="scss" scoped>
.forbid-select {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.split {
  width: 100%;
  height: 100%;
  display: flex;

  .resizer {
    width: 5px;
    // background-color: #ddd;
    cursor: w-resize;
    transition: 0.3s;
    margin: 5px 0 0 0;
    &:hover {
      background-color: #4786ff;
      cursor: w-resize;
    }
  }

  .sub {
    width: 100%;
    height: 100%;
    flex-grow: 1;
    flex-basis: 0%;
    align-items: stretch;
    align-content: stretch;

    overflow-y: hidden;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background-color: #fff;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #666;
    }

    scrollbar-width: thin;
  }

  &.horizontal {
    flex-direction: column;

    .resizer {
      height: 5px;
      width: 100%;
      // cursor: n-resize;
      cursor: row-resize;
    }
  }
}
</style>
