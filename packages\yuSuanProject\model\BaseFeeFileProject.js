"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseFeeFileProject2022 = exports.BaseFeeFileProject = void 0;
const typeorm_1 = require("typeorm");
const { BaseModel } = require("./BaseModel");
/**
 * 取费文件关联关系表
 */
let BaseFeeFileProject = class BaseFeeFileProject extends BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_code" }),
    __metadata("design:type", String)
], BaseFeeFileProject.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_name" }),
    __metadata("design:type", String)
], BaseFeeFileProject.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "code" }),
    __metadata("design:type", String)
], BaseFeeFileProject.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_name" }),
    __metadata("design:type", String)
], BaseFeeFileProject.prototype, "qfName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_code" }),
    __metadata("design:type", String)
], BaseFeeFileProject.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "project_type" }),
    __metadata("design:type", String)
], BaseFeeFileProject.prototype, "projectType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "unit_project_name" }),
    __metadata("design:type", String)
], BaseFeeFileProject.prototype, "unitProjectName", void 0);
BaseFeeFileProject = __decorate([
    (0, typeorm_1.Entity)()
], BaseFeeFileProject);
exports.BaseFeeFileProject = BaseFeeFileProject;
/**
 * 取费文件关联关系表
 */
let BaseFeeFileProject2022 = class BaseFeeFileProject2022 extends BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_code" }),
    __metadata("design:type", String)
], BaseFeeFileProject2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_name" }),
    __metadata("design:type", String)
], BaseFeeFileProject2022.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "code" }),
    __metadata("design:type", String)
], BaseFeeFileProject2022.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_name" }),
    __metadata("design:type", String)
], BaseFeeFileProject2022.prototype, "qfName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_code" }),
    __metadata("design:type", String)
], BaseFeeFileProject2022.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "project_type" }),
    __metadata("design:type", String)
], BaseFeeFileProject2022.prototype, "projectType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "unit_project_name" }),
    __metadata("design:type", String)
], BaseFeeFileProject2022.prototype, "unitProjectName", void 0);
BaseFeeFileProject2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_fee_file_project_2022" })
], BaseFeeFileProject2022);
exports.BaseFeeFileProject2022 = BaseFeeFileProject2022;
//# sourceMappingURL=BaseFeeFileProject.js.map