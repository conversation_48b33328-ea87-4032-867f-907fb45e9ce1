"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseList = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 清单表
 */
let BaseList = class BaseList extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_name", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_code_level01", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdCodeLevel01", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_name_level01", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdNameLevel01", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_code_level02", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdCodeLevel02", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_name_level02", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdNameLevel02", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_code_level03", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdCodeLevel03", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_name_level03", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdNameLevel03", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_code_level04", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdCodeLevel04", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "bd_name_level04", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "bdNameLevel04", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "unit", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zjcs_label_name", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "zjcsLabelName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zjcs_class_code", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "zjcsClassCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zjcs_class_name", nullable: true }),
    __metadata("design:type", String)
], BaseList.prototype, "zjcsClassName", void 0);
BaseList = __decorate([
    (0, typeorm_1.Entity)({ name: "base_list" })
], BaseList);
exports.BaseList = BaseList;
//# sourceMappingURL=BaseList.js.map