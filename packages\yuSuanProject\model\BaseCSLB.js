"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCSLB2022 = exports.BaseCSLB = void 0;
const typeorm_1 = require("typeorm");
const { BaseModel } = require("./BaseModel");
/**
 * 取费文件关联关系表
 */
let BaseCSLB = class BaseCSLB extends BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_name" }),
    __metadata("design:type", String)
], BaseCSLB.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "cslb_code" }),
    __metadata("design:type", String)
], BaseCSLB.prototype, "cslbCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "cslb_name" }),
    __metadata("design:type", String)
], BaseCSLB.prototype, "cslbName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "unit_project_name" }),
    __metadata("design:type", String)
], BaseCSLB.prototype, "unitProjectName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseCSLB.prototype, "sortNo", void 0);
BaseCSLB = __decorate([
    (0, typeorm_1.Entity)({ name: "base_cslb" })
], BaseCSLB);
exports.BaseCSLB = BaseCSLB;
let BaseCSLB2022 = class BaseCSLB2022 extends BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_name" }),
    __metadata("design:type", String)
], BaseCSLB2022.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "cslb_code" }),
    __metadata("design:type", String)
], BaseCSLB2022.prototype, "cslbCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "cslb_name" }),
    __metadata("design:type", String)
], BaseCSLB2022.prototype, "cslbName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "unit_project_name" }),
    __metadata("design:type", String)
], BaseCSLB2022.prototype, "unitProjectName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseCSLB2022.prototype, "sortNo", void 0);
BaseCSLB2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_cslb_2022" })
], BaseCSLB2022);
exports.BaseCSLB2022 = BaseCSLB2022;
//# sourceMappingURL=BaseCSLB.js.map