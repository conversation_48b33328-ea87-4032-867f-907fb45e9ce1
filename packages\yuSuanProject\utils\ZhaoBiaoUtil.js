// exceljs 所需的 polyfills
// require('core-js/modules/es.promise');
// require('core-js/modules/es.string.includes');
// require('core-js/modules/es.object.assign');
// require('core-js/modules/es.object.keys');
// require('core-js/modules/es.symbol');
// require('core-js/modules/es.symbol.async-iterator');
// require('regenerator-runtime/runtime');

// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("./ObjectUtils");
const {ExcelUtil} = require("./ExcelUtil.js");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../../core');
const {NumberUtil} = require("./NumberUtil");
class ZhaoBiaoUtil {

    constructor() {
    }
    /********工程项目层级*************/
    // 封面2 招标控制价（标底）
    async writeDataToCover1(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName!=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招  标  人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充造价咨询人
        let adviceCell = ExcelUtil.findValueCell(worksheet,"造价咨询人：");
        if (adviceCell != null) {
            let filterAdvice = data.filter(object => object.name=="造价咨询人")[0];
            let adviceRow = worksheet.getRow(adviceCell.cell._row._number);
            if (filterAdvice!=null && filterAdvice.remark != null) {
                adviceRow._cells[4].value = filterAdvice.remark;
            }
            ExcelUtil.traversalRowToCellBottom(adviceRow);
        }
        //填充编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    //封面2 招标控制价(不含造价咨询人)
    // 扉页2 招标控制价（标底）
    async writeDataToCover2(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName!=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标控制价 小写
        let xiaoXie = ExcelUtil.findValueCell(worksheet,"招标控制价(标底) ");
        let filterXiaoXie = data.filter(object => object.name=="招标控制价小写")[0];
        let xiaoXieRow = worksheet.getRow(xiaoXie.cell._row._number);
        if (filterXiaoXie!=null && filterXiaoXie.remark != null) {
            xiaoXieRow._cells[4].value = filterXiaoXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(xiaoXieRow);
        //填充招标控制价 大写
        let daXie = ExcelUtil.findValueCell(worksheet,"(大写)：");
        let filterDaXie = data.filter(object => object.name=="招标控制价大写")[0];
        let daXieRow = worksheet.getRow(daXie.cell._row._number);
        if (filterDaXie!=null && filterDaXie.remark != null) {
            daXieRow._cells[4].value = filterDaXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(daXieRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招 标 人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充委托代理人
        let agentCell = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell != null) {
            let filterAgent = data.filter(object => object.name=="法定代表人或委托代理人(第三行)")[0];
            let agentRow = worksheet.getRow(agentCell.cell._row._number);
            if (filterAgent!=null && filterAgent.remark != null) {
                agentRow._cells[4].value = filterAgent.remark;
            }
        }

        //工程造价咨询人
        let adviceCell = ExcelUtil.findValueCell(worksheet,"工程造价　\n咨 询 人：");
        if (adviceCell != null) {
            let filterAdvice = data.filter(object => object.name=="工程造价咨询人")[0];
            let adviceRow = worksheet.getRow(adviceCell.cell._row._number);
            if (filterAdvice!=null && filterAdvice.remark != null) {
                adviceRow._cells[4].value = filterAdvice.remark;
            }
            ExcelUtil.traversalRowToCellBottom(adviceRow);
        }
        //委托代理人
        let agentCell2 = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell2 != null) {
            let filterAgent2 = data.filter(object => object.name=="法定代表人或委托代理人(第五行)")[0];
            let agentRow2 = worksheet.getRow(agentCell2.cell._row._number);
            if (filterAgent2!=null && filterAgent2.remark != null) {
                agentRow2._cells[4].value = filterAgent2.remark;
            }
        }
        //编制人
        let organizeCell = ExcelUtil.findValueCell(worksheet,"编 制 人：");
        if (organizeCell != null) {
            let organizeAgent = data.filter(object => object.name=="编制人")[0];
            let organizeRow = worksheet.getRow(organizeCell.cell._row._number);
            if (organizeAgent!=null && organizeAgent.remark != null) {
                organizeRow._cells[4].value = organizeAgent.remark;
            }
            ExcelUtil.traversalRowToCellBottom(organizeRow);
        }
        //复核人
        let verifyCell = ExcelUtil.findValueCell(worksheet,"复　核　人：");
        let filterVerify = data.filter(object => object.name=="复核人")[0];
        let verifyRow = worksheet.getRow(verifyCell.cell._row._number);
        if (filterVerify!=null && filterVerify.remark != null) {
            verifyRow._cells[4].value = filterVerify.remark;
        }
        //编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport !=null && filterImport.remark != null) {
            importRow._cells[5].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    // 扉页2 招标控制价（不含甲供设备及其税金）
    // 表1-2 工程量清单报价说明

    //表1-1工程量清单编制说明
    async writeDataToCover3(data, worksheet) {
        let row = worksheet._rows[2];
        for (let i = 0; i < row._cells.length; i++) {

            let cell = row._cells[i];
            cell.value = {
                'richText': [{'font': {'bold': false,'size': 12,'color': {'theme': 1},
                'name': 'Calibri','family': 2,'scheme': 'minor'},'text': data}]
            };
            cell.protection = {
                locked: false,
                hidden: false,
            };
            break;
        }
        // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
    }
    //工程项目总价表  和单位层级的模板表结构一样
    async writeDataToSheet1(data, worksheet,constructIs2022) {
        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = data[countRow].sortNo+"";
                }
                if (cell.col == 2) {
                    cell.value = data[countRow].name;
                }
                if (cell.col == 4) {
                    cell.value = data[countRow].total; //金额
                    if (data[countRow].total != null && ObjectUtils.isNotEmpty(data[countRow].level)&&
                        data[countRow].level=="parent"
                    ) {
                        amount+= Number.parseFloat(cell.value);
                    }
                }
                if (constructIs2022) {
                    if (cell.col == 5) {
                        cell.value = data[countRow].awf;//安文费
                        if (data[countRow].level != null  && ObjectUtils.isNotEmpty(cell.value)) {
                            awenFeeTotal+= Number.parseFloat(cell.value);
                        }
                    }
                }else {
                    if (cell.col == 6) {
                        cell.value = data[countRow].gf;//规费
                        if (data[countRow].level != null && ObjectUtils.isNotEmpty(cell.value)) {
                            gfeeTotal += Number.parseFloat(cell.value);
                        }
                    }
                    if (cell.col == 7) {
                        cell.value = data[countRow].awf;//安文费
                        if (data[countRow].level != null  && ObjectUtils.isNotEmpty(cell.value)) {
                            awenFeeTotal+= Number.parseFloat(cell.value);
                        }
                    }
                }
            }
        }

        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet," 合　　计");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[3].value = amount.toFixed(2);//金额合计
        if (constructIs2022) {
            row._cells[4].value = awenFeeTotal.toFixed(2);//安文费合计
        }else {
            row._cells[5].value = gfeeTotal.toFixed(2);//规费合计
            row._cells[6].value = awenFeeTotal.toFixed(2);//安文费合计
        }

    }

    //表1-4 单项工程费汇总表
    async writeDataToSheet2(data, worksheet,constructIs2022) {

        let dataList = data.analysisZJ;
        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataList.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataList[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = dataList[countRow].projectName+" 合计";//名称
                }
                if (cell.col == 4) {
                    let totalHj = 0;
                    totalHj = NumberUtil.addParams(dataList[countRow].fbfxhj,dataList[countRow].csxhj,dataList[countRow].qtxmhj,
                        dataList[countRow].gfee,dataList[countRow].safeFee,dataList[countRow].sj);//金额
                    cell.value = totalHj.toFixed(2);
                }
                if (constructIs2022) {
                    if (cell.col == 5) {
                        cell.value = dataList[countRow].safeFee;//安文费
                    }
                }else {
                    if (cell.col == 6) {
                        cell.value = dataList[countRow].gfee;//规费
                    }
                    if (cell.col == 7) {
                        cell.value = dataList[countRow].safeFee;//安文费
                    }
                }
            }
        }
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合　计(不含设备费)");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[3].value = data.amount.toFixed(2);//金额合计
        if (constructIs2022) {
            row._cells[4].value = data.awenFeeTotal.toFixed(2);//安文费合计
        }else {
            row._cells[5].value = data.gfeeTotal.toFixed(2);//规费合计
            row._cells[6].value = data.awenFeeTotal.toFixed(2);//安文费合计
        }
    }

    //单项工程造价分析表
    async writeDataToSheet3(data, worksheet,constructIs2022) {

        let dataList = data.analysisZJ;
        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataList.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataList[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = dataList[countRow].projectName+" 合计";
                }
                if (cell.col == 3) {
                    let totalHj = NumberUtil.addParams(dataList[countRow].fbfxhj,dataList[countRow].csxhj,
                        dataList[countRow].qtxmhj,dataList[countRow].gfee,dataList[countRow].safeFee,dataList[countRow].sj);
                    cell.value = totalHj.toFixed(2);//金额
                }
                if (cell.col == 4) {
                    cell.value = dataList[countRow].fbfxhj;//分部分项合计
                }
                if (cell.col == 6) {
                    cell.value = dataList[countRow].csxhj;//措施项目合计
                }
                if (cell.col == 7) {
                    cell.value = dataList[countRow].qtxmhj;//其他项目合计
                }
                if (constructIs2022) {
                    if (cell.col == 8) {
                        cell.value = dataList[countRow].safeFee;//安文费
                    }
                    if (cell.col == 9) {
                        cell.value = dataList[countRow].sj;//税金
                    }
                    if (cell.col == 11) {
                        cell.value = dataList[countRow].average;//建筑面积(m、㎡)
                    }
                    if (cell.col == 12) {
                        cell.value = dataList[countRow].unitcost;//造价指标
                    }
                    if (cell.col == 13) {
                        cell.value = dataList[countRow].sbfsj;//设备费及其税金
                    }
                }else {
                    if (cell.col == 8) {
                        cell.value = dataList[countRow].gfee;//规费
                    }
                    if (cell.col == 9) {
                        cell.value = dataList[countRow].safeFee;//安文费
                    }
                    if (cell.col == 10) {
                        cell.value = dataList[countRow].sj;//税金
                    }
                    if (cell.col == 12) {
                        cell.value = dataList[countRow].average;//建筑面积(m、㎡)
                    }
                    if (cell.col == 13) {
                        cell.value = dataList[countRow].unitcost;//造价指标
                    }
                    if (cell.col == 14) {
                        cell.value = dataList[countRow].sbfsj;//设备费及其税金
                    }
                }

            }
        }
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合　计(不含设备费)");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[2].value = data.amount.toFixed(2);//金额合计
        row._cells[4].value = data.fbfxhj.toFixed(2);//分部分项合计
        row._cells[5].value = data.csxhj.toFixed(2);//措施项目合计
        row._cells[6].value = data.qtxmhj.toFixed(2);//其他项目合计
        if (constructIs2022) {
            // row._cells[7].value = data.gfeeTotal.toFixed(2);//规费合计
            row._cells[7].value = data.awenFeeTotal.toFixed(2);//安文费合计
            row._cells[8].value = data.sj.toFixed(2);//税金合计
            row._cells[12].value = data.sbfsj.toFixed(2);//设备费及其税金合计
        }else {
            row._cells[7].value = data.gfeeTotal.toFixed(2);//规费合计
            row._cells[8].value = data.awenFeeTotal.toFixed(2);//安文费合计
            row._cells[9].value = data.sj.toFixed(2);//税金合计
            row._cells[13].value = data.sbfsj.toFixed(2);//设备费及其税金合计
        }


    }

    // 安全生产、文明施工费汇总表
    async writeDataToSheet4(data, worksheet,singleData) {
        //计算单项的安文费之和
        let heJi = 0;
        for (let i = 0; i < singleData.length; i++) {
            heJi+=singleData[i].safeFee;
        }

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = data[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = data[countRow].name;
                }
                if (cell.col == 4) {
                    cell.value = data[countRow].awen;
                }
                if (cell.col == 5) {
                    cell.value = data[countRow].awen;//基本费
                }
                if (cell.col == 7) {
                    cell.value = 0;  //增加费
                }
            }
        }

        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[3].value = heJi.toFixed(2);
        row._cells[4].value = heJi.toFixed(2);//合计也作为基本费
    }

    /********单项工程层级*************/
    // 表1-5单项工程费汇总表
    async writeDataToSheet5(data, worksheet) {
        let dataTotal = [];
        let totalUnit = 0;
        let totalRg = 0;
        let totalCl = 0;
        let totalJx = 0;
        for (let i = 0; i < data.length; i++) {
            totalUnit = totalUnit+data[i][0].price
            totalRg= totalRg + data[i][0].rg
            totalCl= totalCl + data[i][0].cl
            totalJx= totalJx + data[i][0].jx
            for (let m = 0; m < data[i].length; m++) {
                dataTotal.push(data[i][m]);
            }
        }

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataTotal.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataTotal[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = dataTotal[countRow].name;
                }
                if (cell.col == 4) {
                    cell.value = dataTotal[countRow].instructions;
                }
                if (cell.col == 5) {
                    cell.value = dataTotal[countRow].rate;//费率
                    if (cell.value==100) {
                        cell.value = "/";
                    }
                }
                if (cell.col == 6) {
                    cell.value = dataTotal[countRow].price;  //金额
                }
                if (cell.col == 8) {
                    cell.value = dataTotal[countRow].rg;  //人工费
                }
                if (cell.col == 9) {
                    cell.value = dataTotal[countRow].cl;  //材料费
                }
                if (cell.col == 10) {
                    cell.value = dataTotal[countRow].jx;  //机械费
                }
            }
        }
        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = totalUnit;
        row._cells[7].value = totalRg;
        row._cells[8].value = totalCl;
        row._cells[9].value = totalJx;
    }
    /********单位工程层级*************/
    // 封面2 招标控制价（标底）
    async writeUnitDataToCover1(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName !=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招  标  人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb !=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充造价咨询人
        let adviceCell = ExcelUtil.findValueCell(worksheet,"造价咨询人：");
        if (adviceCell != null) {
            let filterAdvice = data.filter(object => object.name=="造价咨询人")[0];
            let adviceRow = worksheet.getRow(adviceCell.cell._row._number);
            if (filterAdvice!=null && filterAdvice.remark != null) {
                adviceRow._cells[4].value = filterAdvice.remark;
            }
            ExcelUtil.traversalRowToCellBottom(adviceRow);
        }
        //填充编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    // 封面2 招标控制价(不含造价咨询人)
    // 扉页2 招标控制价（标底）
    async writeUnitDataToCover2(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName !=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        //对行中的cell 格式进行定制化
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标控制价 小写
        let xiaoXie = ExcelUtil.findValueCell(worksheet,"招标控制价(标底) ");
        let filterXiaoXie = data.filter(object => object.name=="招标控制价小写")[0];
        let xiaoXieRow = worksheet.getRow(xiaoXie.cell._row._number);
        if (filterXiaoXie!=null && filterXiaoXie.remark != null) {
            xiaoXieRow._cells[4].value = filterXiaoXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(xiaoXieRow);
        //填充招标控制价 大写
        let daXie = ExcelUtil.findValueCell(worksheet,"(大写)：");
        let filterDaXie = data.filter(object => object.name=="招标控制价大写")[0];
        let daXieRow = worksheet.getRow(daXie.cell._row._number);
        if (filterDaXie!=null && filterDaXie.remark != null) {
            daXieRow._cells[4].value = filterDaXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(daXieRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招 标 人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充委托代理人
        let agentCell = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell != null) {
            let filterAgent = data.filter(object => object.name=="法定代表人或委托代理人(第三行)")[0];
            let agentRow = worksheet.getRow(agentCell.cell._row._number);
            if (filterAgent!=null && filterAgent.remark != null) {
                agentRow._cells[4].value = filterAgent.remark;
            }
        }

        //工程造价咨询人
        let adviceCell = ExcelUtil.findValueCell(worksheet,"工程造价　\n咨 询 人：");
        if (adviceCell != null) {
            let filterAdvice = data.filter(object => object.name=="工程造价咨询人")[0];
            let adviceRow = worksheet.getRow(adviceCell.cell._row._number);
            if (filterAdvice!=null && filterAdvice.remark != null) {
                adviceRow._cells[4].value = filterAdvice.remark;
            }
            ExcelUtil.traversalRowToCellBottom(adviceRow);
        }
        //委托代理人
        let agentCell2 = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell2 != null) {
            let filterAgent2 = data.filter(object => object.name=="法定代表人或委托代理人(第五行)")[0];
            let agentRow2 = worksheet.getRow(agentCell2.cell._row._number);
            if (filterAgent2 !=null && filterAgent2.remark != null) {
                agentRow2._cells[4].value = filterAgent2.remark;
            }
        }
        //编制人
        let organizeCell = ExcelUtil.findValueCell(worksheet,"编 制 人：");
        if (organizeCell != null) {
            let organizeAgent = data.filter(object => object.name=="编制人")[0];
            let organizeRow = worksheet.getRow(organizeCell.cell._row._number);
            if (organizeAgent!=null && organizeAgent.remark != null) {
                organizeRow._cells[4].value = organizeAgent.remark;
            }
            ExcelUtil.traversalRowToCellBottom(organizeRow);
        }
        //复核人
        let verifyCell = ExcelUtil.findValueCell(worksheet,"复　核　人：");
        let filterVerify = data.filter(object => object.name=="复核人")[0];
        let verifyRow = worksheet.getRow(verifyCell.cell._row._number);
        if (filterVerify!=null && filterVerify.remark != null) {
            verifyRow._cells[4].value = filterVerify.remark;
        }
        //编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[5].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    // 扉页2 招标控制价（不含甲供设备及其税金）
    // 表1-2 工程量清单报价说明

    // 表1-5 单位工程费汇总表
    async writeDataToSheet8(dataTotal, worksheet) {
        let fbfx = dataTotal.filter(object => object.name=="分部分项工程量清单计价合计")[0];
        let csxm = dataTotal.filter(object => object.name=="措施项目清单计价合计")[0];
        let csxmDj = dataTotal.filter(object => object.name=="单价措施项目工程量清单计价合计")[0];
        let csxmOther = dataTotal.filter(object => object.name=="其他总价措施项目清单计价合计")[0];
        if (csxm != null) {
            csxm.rg = (csxmDj!=null?Number.parseFloat(csxmDj.rg):0)+(csxmOther!=null?Number.parseFloat(csxmOther.rg):0);
            csxm.cl = (csxmDj!=null?Number.parseFloat(csxmDj.cl):0)+(csxmOther!=null?Number.parseFloat(csxmOther.cl):0);
            csxm.jx = (csxmDj!=null?Number.parseFloat(csxmDj.jx):0)+(csxmOther!=null?Number.parseFloat(csxmOther.jx):0);
        }
        let totalRg = ( ((csxm!=null?Number.parseFloat(csxm.rg):0))+(fbfx!=null?Number.parseFloat(fbfx.rg):0) ).toFixed(2);
        let totalCl = ( ((csxm!=null?Number.parseFloat(csxm.cl):0))+(fbfx!=null?Number.parseFloat(fbfx.cl):0) ).toFixed(2);
        let totalJx = ( ((csxm!=null?Number.parseFloat(csxm.jx):0))+(fbfx!=null?Number.parseFloat(fbfx.jx):0) ).toFixed(2);

        let filter = dataTotal.filter(object => object.name === "工程造价");
        if (ObjectUtils.isEmpty(filter)) {
            filter = dataTotal.filter(object => object.name === "含税工程造价");
        }
        let total = 0;
        if (ObjectUtils.isNotEmpty(filter) && Array.isArray(filter) && filter.length > 0) {
            total = filter[0].price;
        }

        dataTotal = dataTotal.filter(item => item.whetherPrint==1);//1 为需要打印 展示的数据
        //对数据进行处理
        let countRow = -1;
        for (let i = 0; i < worksheet._rows.length; i++) {
            let rowNum = worksheet._rows[i];
            if (rowNum.number >= 5) {  //从第五行开始填充
                countRow++;//从索引零开始 填充
                if (countRow>=dataTotal.length) break;
                for (let j = 0; j < rowNum._cells.length; j++) {
                    let cell = rowNum._cells[j];
                    if (cell.col == 1) {
                        cell.value = dataTotal[countRow].dispNo;
                    }
                    if (cell.col == 2){
                        cell.value = dataTotal[countRow].name;
                    }
                    if (cell.col == 4) {
                        cell.value = dataTotal[countRow].instructions;
                    }
                    if (cell.col == 5) {
                        if (dataTotal[countRow].rate == 100) {
                            cell.value = "/";
                        }else {
                            cell.value = dataTotal[countRow].rate;//费率
                        }
                    }
                    if (cell.col == 6) {
                        cell.value = dataTotal[countRow].price;  //金额
                    }
                    if (cell.col == 8) {
                        cell.value = dataTotal[countRow].rg;  //人工费
                    }
                    if (cell.col == 9) {
                        cell.value = dataTotal[countRow].cl;  //材料费
                    }
                    if (cell.col == 10) {
                        cell.value = dataTotal[countRow].jx;  //机械费
                    }
                }
            }
        }

        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = total;
        row._cells[7].value = totalRg;
        row._cells[8].value = totalCl;
        row._cells[9].value = totalJx;
    }

    // 表1-5 单位工程费汇总表--(唐山地区)
    // 表1-6 分部分项工程量清单与计价表
    async writeDataToUnitSheet9(data, worksheet,arg) {
        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) cell.value = data[countRow].dispNo;
                if (cell.col == 2) cell.value = data[countRow].bdCode;
                if (cell.col == 3) cell.value = data[countRow].name;
                if (cell.col == 5) cell.value = data[countRow].projectAttr;
                if (cell.col==6) cell.value = data[countRow].unit;//计量单位  缺失
                if (cell.col == 7) cell.value = data[countRow].quantity;
                if (cell.col == 9){
                    cell.value = data[countRow].price;
                    if (cell.value == null) {
                        cell.value = 0;
                    }
                }
                if (cell.col == 10){
                    cell.value = data[countRow].total;
                    if (cell.value == null) {
                        cell.value = 0;
                    }
                }
            }
        }
    }
    // 表1-7 单价措施项目工程量清单与计价表
    async writeDataToUnitSheet10(data, worksheet) {
        //增加第一行数据 项目名称为 单价措施项目
        data.unshift({'name':"单价措施项目"});
        let orderNum = 0;
        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1 && data[countRow].dispNo!=null) {
                    cell.value = ++orderNum;
                }
                if (cell.col == 2){
                    cell.value = data[countRow].fxCode;
                }
                if (cell.col == 3) {
                    cell.value = data[countRow].name;
                }
                if (cell.col == 5) {
                    cell.value = data[countRow].projectAttr;
                }
                if (cell.col == 6) {
                    cell.value = data[countRow].unit;//计量单位  缺失
                }
                if (cell.col == 7) {
                    cell.value = data[countRow].quantity;
                }
                if (cell.col == 9) {
                    cell.value = data[countRow].price;
                    if (cell.value == null && countRow!=0) { //  countRow!=0 第一行的综合单价和合价 就置为空
                        cell.value = 0;
                    }
                }
                if (cell.col == 10) {
                    cell.value = data[countRow].total;
                    if (cell.value == null && countRow!=0) {
                        cell.value = 0;
                    }
                }
            }
        }
    }
    // 表1-8 总价措施项目清单与计价表
    async writeDataToUnitSheet11(data, worksheet) {

        //先填充安文费
        let row = worksheet._rows[4];//安文费序号为1的那一行
        row._cells[1].value = data[0][0].fxCode;
        row._cells[2].value = data[0][0].name;
        row._cells[5].value = data[0][0].total;
        let awenXiaoJi = worksheet._rows[5];
        awenXiaoJi._cells[5].value = data[0][0].total;
        //增加其他总价措施的小计
        let otherTotal = 0;
        data[1].forEach(function(element) {
            if (element.total != null) {
                otherTotal += element.total;
            }
        });
        data[1].push({"fxCode":"/","name":"小计","total":otherTotal.toFixed(2)});
        let orderNum = 0;
        //填充其他总价措施项目
        let headCount = 6;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data[1].length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1 && data[1][countRow].dispNo!=null) {
                    cell.value = ++orderNum;
                }
                if (cell.col == 2){
                    cell.value = data[1][countRow].fxCode;
                }
                if (cell.col == 3) {
                    cell.value = data[1][countRow].name;
                    if (cell.value != null && cell.value == "小计") {
                        //对小计的格式做出处理
                        cell.style.alignment.horizontal = "center";
                    }
                }
                if (cell.col == 6) {
                    cell.value = data[1][countRow].total;
                    if (cell.value == null) {
                        cell.value = 0;
                    }
                }
            }
        }
    }
    // 表1-9 其他项目清单与计价表
    async writeDataToUnitSheet12(data, worksheet) {
        //对数据进行处理
        // let name = "工程名称：单项工程建筑工程";
        // let cell1 = ExcelUtil.findValueCell(worksheet,name);
        // let newStr = name.replace("单项工程建筑工程", "你好呀");
        // cell1.cell.value = newStr;
        let countRow = -1;
        let priceTotal = 0;
        for (let i = 0; i < worksheet._rows.length; i++) {
            let rowNum = worksheet._rows[i];
            if (rowNum.number >= 4) {  //从第四行开始填充
                countRow++;//从索引零开始 填充
                if (countRow>=data.length) break;
                for (let j = 0; j < rowNum._cells.length; j++) {
                    let cell = rowNum._cells[j];
                    if (cell.col == 1) {
                        cell.value = data[countRow].dispNo;
                    }
                    if (cell.col == 2){
                        cell.value = data[countRow].extraName;
                    }
                    if (cell.col == 5) {
                        if (data[countRow].extraName.includes("材料暂估价") || data[countRow].extraName.includes("设备暂估价")) {
                            cell.value = "/";
                        }else {
                            cell.value = data[countRow].total;
                            if (ObjectUtils.isNotEmpty(data[countRow].dispNo) && !data[countRow].dispNo.includes(".") && cell.value != null) {
                                priceTotal +=Number.parseFloat(cell.value);
                            }
                        }
                    }
                }
            }
        }
        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合  计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[4].value = priceTotal.toFixed(2);
    }
    // 表1-10 暂列金额明细表
    async writeDataToUnitSheet13(data, worksheet) {

        let total = 0;
        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = data[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = data[countRow].name;
                }
                if (cell.col == 5) {
                    cell.value = data[countRow].provisionalSum;//暂列金额
                    if (cell.value != null) {
                        total += Number.parseFloat(cell.value);
                    }
                }
                if (cell.col == 6) {
                    cell.value = data[countRow].description;//备注
                }
            }
        }
        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[4].value = total.toFixed(2);
    }
    // 表1-11 暂估价表
    async writeDataToUnitSheet14(data, worksheet) {

        //对数据进行处理
        // let name = "工程名称：单项工程建筑工程";
        // let cell1 = ExcelUtil.findValueCell(worksheet,name);
        // let newStr = name.replace("单项工程建筑工程", "你好呀");
        // cell1.cell.value = newStr;

        let row = worksheet._rows[4];
        row._cells[0].value = "3";
        row._cells[1].value = "专业工程暂估价";
        row._cells[2].value = "/";
        row._cells[4].value = "/";
        row._cells[6].value= "/";

        let orderNum = 0;
        let total = 0;
        let headCount = 4;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    orderNum++;
                    cell.value = "3."+orderNum;
                }
                if (cell.col == 2){
                    if (data[countRow].name != null) {
                        cell.value = data[countRow].name;//名称
                        ExcelUtil.setStyleForCellHorizontal(cell.style,cell,"left");
                    }
                }
                if (cell.col == 3) {
                    cell.value = data[countRow].content;//规格或工程内容
                    ExcelUtil.setStyleForCellHorizontal(cell.style,cell,"left");
                }
                if (cell.col == 5) {
                    cell.value = data[countRow].unit;//计量单位
                }
                if (cell.col == 7) {
                    cell.value = data[countRow].total;//暂估价
                    if (cell.value != null) {
                        total += Number.parseFloat(cell.value);
                    }
                }
                if (cell.col == 8) {
                    cell.value = data[countRow].description;//备注
                }
            }
        }
        //填入小计
        let xiaoJiRow = worksheet._rows[++headCount];
        xiaoJiRow._cells[0].value = "/";
        xiaoJiRow._cells[1].value = "小计";
        ExcelUtil.setStyleForCellHorizontal(xiaoJiRow._cells[1].style,xiaoJiRow._cells[1],"center");
        xiaoJiRow._cells[2].value = "/";
        ExcelUtil.setStyleForCellHorizontal(xiaoJiRow._cells[2].style,xiaoJiRow._cells[2],"center");
        xiaoJiRow._cells[4].value = "/";
        xiaoJiRow._cells[6].value = total;
    }
    // 表1-12 总承包服务费计价表
    async writeDataToUnitSheet15(data, worksheet) {
        //数据行 dataType 2  标题行 1
        let dataList = [];
        let heJi = 0;
        let headLines = data.filter(item =>item.dataType==1);
        for (let i = 0; i < headLines.length; i++) {
            dataList.push(headLines[i]);
            if (ObjectUtils.isNotEmpty(headLines[i].fwje)) {
                heJi += parseFloat(headLines[i].fwje);
            }
            let children = data.filter(item => item.parentId==headLines[i].sequenceNbr);
            if (headLines[i].fxName.includes("招标人另行发包专业工程")) {
                headLines[i].xmje = "/";
                headLines[i].rate = "/";
                dataList.push(...children);
                let xiaoJi = {};
                xiaoJi.fxName = "小计";
                xiaoJi.rate = "/";//费率
                xiaoJi.fwje = headLines[i].fwje;//金额
                dataList.push(xiaoJi);
            }
        }

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataList.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataList[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = dataList[countRow].fxName;
                    if (cell.value == "小计") {
                        ExcelUtil.setStyleForCellHorizontal(cell.style,cell,"center");
                    }
                }
                if (cell.col == 4) {
                    cell.value = dataList[countRow].xmje;//项目金额
                }
                if (cell.col == 6) {
                    cell.value = dataList[countRow].rate;//费率
                }
                if (cell.col == 7) {
                    cell.value = dataList[countRow].fwje;//金额
                }
            }
        }

        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合    计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = heJi;
    }
    // 表1-13 计日工表
    async writeDataToUnitSheet16(data, worksheet) {
        //数据行 dataType 2  标题行 1
        let dataList = [];
        let headLines = data.filter(item =>item.dataType==1);
        let total = 0;
        for (let i = 0; i < headLines.length; i++) {
            dataList.push(headLines[i])
            let children = data.filter(item => item.parentId==headLines[i].sequenceNbr);
            dataList.push(...children);
            let xiaoJi = {};
            xiaoJi.worksName = "小计";
            xiaoJi.specification = "/";
            xiaoJi.unit = "/";
            xiaoJi.tentativeQuantity = "/";
            xiaoJi.price = "/";
            xiaoJi.total = headLines[i].total;
            if (xiaoJi.total != null) {
                total += Number.parseFloat(xiaoJi.total);
            }
            dataList.push(xiaoJi);
        }

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < dataList.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = dataList[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = dataList[countRow].worksName;//名称
                }
                if (cell.col == 3) {
                    cell.value = dataList[countRow].specification;//规格型号
                }
                if (cell.col == 5) {
                    cell.value = dataList[countRow].unit;//计量单位
                }
                if (cell.col == 6) {
                    cell.value = dataList[countRow].tentativeQuantity;//暂定数量
                }
                if (cell.col == 8) {
                    cell.value = dataList[countRow].price;//综合单价
                }
                if (cell.col == 9) {
                    cell.value = dataList[countRow].total;//合价
                }
            }
        }
        //填充合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet,"合计");
        //确定合计行的行数
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[8].value = total.toFixed(2);

    }
    // 表1-14 招标人供应材料、设备明细表
    async writeDataToUnitSheet17(data, worksheet) {
        if (data == null || data.length == 0) {
            return;
        }
        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cellCur = rowObject._cells[j];
                if (cellCur.col == 1) {
                    cellCur.value = data[countRow].sort;
                }
                if (cellCur.col == 2) {
                    cellCur.value = data[countRow].materialName;
                }
                if (cellCur.col == 3) {
                    cellCur.value = data[countRow].specification;
                }
                if (cellCur.col == 5) {
                    cellCur.value = data[countRow].unit;
                }
                if (cellCur.col == 6) {
                    cellCur.value = data[countRow].totalNumber;
                }
                if (cellCur.col == 7) {
                    cellCur.value = data[countRow].marketPrice;
                }
                if (cellCur.col == 8) {
                    cellCur.value = data[countRow].total;
                }
                if (cellCur.col == 9) {
                    cellCur.value = data[countRow].qualityGrade;  //质量等级
                }
                if (cellCur.col == 11) {
                    cellCur.value = data[countRow].priceDate;  //供应时间
                }
                if (cellCur.col == 12) {
                    cellCur.value = data[countRow].deliveryLocation;  //送达地点
                }
                if (cellCur.col == 13) {
                    cellCur.value = data[countRow].description;  //备注
                }
            }
        }
    }
    // 表1-15 主要材料、设备明细表
    async writeDataToUnitSheet18(data, worksheet) {
        if (data == null || data.length == 0) {
            return;
        }
        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        let totalXiaoJi = 0;
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+2,list,'o');
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cellCur = rowObject._cells[j];
                if (cellCur.col == 1) {
                    cellCur.value = data[countRow].sort;
                }
                if (cellCur.col == 2) {
                    cellCur.value = data[countRow].materialCode;
                }
                if (cellCur.col == 3) {
                    cellCur.value = data[countRow].materialName;
                }
                if (cellCur.col == 5) {
                    cellCur.value = data[countRow].specification;
                }
                if (cellCur.col == 6) {
                    cellCur.value = data[countRow].unit;
                }
                if (cellCur.col == 7) {
                    cellCur.value = data[countRow].totalNumber;//数量
                }
                if (cellCur.col == 8) {
                    cellCur.value = data[countRow].marketPrice;
                }
                if (cellCur.col == 10) {
                    cellCur.value = data[countRow].total;
                    if (cellCur.value != null && cellCur.value !="/") {
                        totalXiaoJi += Number.parseFloat(cellCur.value);
                    }
                }
                if (cellCur.value == "材料" || cellCur.value == "/" || cellCur.value == "小计" || cellCur.value == "设备") {
                    // cellCur.alignment.horizontal = "center";
                    // worksheet.getCell(cellCur._address).alignment = { vertical: 'middle', horizontal: 'center' };
                    ExcelUtil.setStyleForCellHorizontal(cellCur.style,cellCur,"center");
                }
            }
        }

        if (worksheet.name=="表1-15 主要材料明细表"|| worksheet.name=="表1-15 设备明细表") {
            let heJiCell = ExcelUtil.findValueCell(worksheet,"小计");
            let row = worksheet.getRow(heJiCell.cell._row._number);
            row._cells[9].value = totalXiaoJi.toFixed(2);
        }
    }

    // 表1-16 分部分项工程量清单综合单价分析表  //仅展示清单定额
    async writeDataToUnitSheet116(data, worksheet) {

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = data[countRow].dispNo;
                }
                if (cell.col == 2){
                    cell.value = data[countRow].fxCode;
                }
                if (cell.col == 3) {
                    cell.value = data[countRow].name;//项目名称
                    if (cell.value!=null && cell.value.split("\n").length > 1) {
                        cell.value = cell.value.split("\n")[0];
                    }
                }
                if (cell.col == 4) {
                    cell.value = data[countRow].unit;//计量单位  缺失
                }
                if (cell.col == 5) {
                    cell.value = data[countRow].quantity;//数量
                }
                if (cell.col == 6) {
                    cell.value = data[countRow].quantity;//数量
                }
                if (cell.col == 7) {
                    cell.value = data[countRow].price;
                }
                if (cell.col == 8) {
                    cell.value = data[countRow].total;//合价
                }
                if (cell.col == 9) {
                    cell.value = data[countRow].rfee;//人工费
                }

                if (cell.col == 11) {
                    cell.value = data[countRow].cfee;
                }

                if (cell.col == 12) {
                    cell.value = data[countRow].jfee;
                }
                if (cell.col == 13) {
                    let managerFee = 0;
                    let profitFee = 0;
                    if (data[countRow].managerFee != null) {
                        managerFee = data[countRow].managerFee;
                    }
                    if (data[countRow].profitFee != null) {
                        profitFee = data[countRow].profitFee;
                    }
                    cell.value = NumberUtil.add(managerFee, profitFee).toFixed(2);//管理费和利润
                }
                if (cell.col == 14) {
                    //填充人工单价
                    cell.value = data[countRow].rfeePrice;
                }
            }
        }
    }
    // 表1-17 单价措施项目工程量清单综合单价分析表
    async writeDataToUnitSheet117(data, worksheet) {

        let orderNum = 0;
        let viceOrder = 0;
        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    if (data[countRow].kind == "03") { //如果是清单
                        orderNum++;
                        cell.value = orderNum;
                        viceOrder = 0;
                    }
                    if (data[countRow].kind == "04") { //如果是定额
                        viceOrder++;
                        cell.value = orderNum+"."+viceOrder;
                    }
                }
                if (cell.col == 2){
                    cell.value = data[countRow].fxCode;
                }
                if (cell.col == 3) {
                    cell.value = data[countRow].name;//项目名称
                    if (cell.value.split("\n").length > 1) {
                        cell.value = cell.value.split("\n")[0];
                    }
                }
                if (cell.col == 4) {
                    cell.value = data[countRow].unit;//计量单位
                }
                if (cell.col == 5) {
                    cell.value = data[countRow].quantity;//数量
                }
                if (cell.col == 7) {
                    cell.value = data[countRow].price;
                }
                if (cell.col == 8) {
                    cell.value = data[countRow].total;//合价
                }
                if (cell.col == 9) {
                    cell.value = data[countRow].rfee;//人工费
                }

                if (cell.col == 11) {
                    cell.value = data[countRow].cfee;
                }

                if (cell.col == 12) {
                    cell.value = data[countRow].jfee;
                }
                if (cell.col == 13) {
                    let managerFee = 0;
                    let profitFee = 0;
                    if (data[countRow].managerFee != null) {
                        managerFee = data[countRow].managerFee;
                    }
                    if (data[countRow].profitFee != null) {
                        profitFee = data[countRow].profitFee;
                    }
                    cell.value = (managerFee+profitFee).toFixed(2);//管理费和利润
                }
                if (cell.col == 14) {
                    cell.value = data[countRow].rfeePrice;
                }
            }
        }
    }
    // 表1-18 总价措施项目费分析表
    async writeDataToUnitSheet118(data, worksheet,constructProjectRcjs) {
        let orderNum = 0;//用来记录序号

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    if (data[countRow].kind == BranchProjectLevelConstant.qd) {
                        orderNum++;
                        cell.value = orderNum;
                    }
                }
                if (cell.col == 2){
                    cell.value = data[countRow].fxCode;
                }
                if (cell.col == 3) {
                    cell.value = data[countRow].name;
                }
                if (cell.col == 5) {
                    if (data[countRow].kind==BranchProjectLevelConstant.de && data[countRow].isCostDe != 0) {
                        //人材机明细消耗量的累加
                        if (constructProjectRcjs != null) {
                            let deRcjs = constructProjectRcjs.filter(item => item.deId == data[countRow].sequenceNbr);
                            let rateTotal = 0;
                            deRcjs.forEach(item => {
                                rateTotal += item.resQty;
                            })
                            cell.value = rateTotal.toFixed(4);//费率
                        }
                    }
                }
                if (cell.col == 7) {
                    if (data[countRow].total != null) {
                        cell.value = data[countRow].total;//金额
                    }
                }
                if (cell.col == 8) {
                    cell.value = data[countRow].rfee;//人工费
                }
                if (cell.col == 10) {
                    cell.value = data[countRow].cfee;
                }
                if (cell.col == 11) {
                    cell.value = data[countRow].jfee;
                }
                if (cell.col == 12) {
                    let managerFee = 0;
                    let profitFee = 0;
                    if (data[countRow].managerFee != null) {
                        managerFee = data[countRow].managerFee;
                    }
                    if (data[countRow].profitFee != null) {
                        profitFee = data[countRow].profitFee;
                    }
                    cell.value = (managerFee+profitFee).toFixed(2);//管理费和利润
                }
                if (cell.col == 13) {
                    cell.value = data[countRow].rfeePrice;
                }
                if (j == rowObject._cells.length - 1) {
                    //费用定额的话需要重新计算计算基数
                    if (data[countRow].kind==BranchProjectLevelConstant.de && data[countRow].isCostDe != 0) {
                        rowObject._cells[3].value = (rowObject._cells[6].value/(rowObject._cells[4].value/100)).toFixed(2);//计算基数
                    }
                }
            }
        }
    }
    // 材料、机械、设备增值税计算表
    // 材料、机械、设备增值税计算表（实体）
    // 材料、机械、设备增值税计算表（措施）
    // 增值税进项税额计算汇总表


    async fillSheetProjectName(worksheet,projectName,containValue) {
        let cellList = ExcelUtil.findContainValueCell(worksheet,containValue);
        let newName = containValue+projectName;
        for (let i = 0; i < cellList.length; i++) {
            let element = cellList[i];
            element.cell.value = newName;
        }
    }




    // 表1-18 总价措施项目费分析表
    async writeDataToUnitSheetHandRcj(data, worksheet) {
        let orderNum = 0;//用来记录序号

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < data.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];//从第五行开始写入数据
            let copyDistance = 3;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount+copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list=[];
                //复制当前数据插入行的格式到增加行
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount+1+1,list,'o');//这里加2 是因为新增的插入行是当前数据插入行的下一行
                await ExcelUtil.resetMerges(worksheet,headCount+2);
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0;m<rowNext._cells.length;m++){
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges,rowObject._cells[m]);
                    if (mergeName!=null){
                        let {top,left,bottom,right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([rowNext.number,left,rowNext.number,right]);
                        worksheet.mergeCells([rowNext.number,left,rowNext.number,right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;
            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = i + 1;
                }
                if (cell.col == 2){
                    cell.value = data[countRow].materialCode;
                }
                if (cell.col == 3) {
                    cell.value = data[countRow].materialName;
                }
                if (cell.col == 4) {    //规格型号
                    cell.value = data[countRow].specification;
                }
                if (cell.col == 5) {    //单位
                     cell.value = data[countRow].unit;
                }
                if (cell.col == 6) {    //数量
                    cell.value = data[countRow].totalNumber;
                }
            }
        }
    }


}
module.exports = {
    ZhaoBiaoUtil: new ZhaoBiaoUtil()
};
