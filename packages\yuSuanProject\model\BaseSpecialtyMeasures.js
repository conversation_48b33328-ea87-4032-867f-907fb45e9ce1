"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseSpecialtyMeasures = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
/**
 * 取费文件关联关系表
 */
let BaseSpecialtyMeasures = class BaseSpecialtyMeasures extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "template_name" }),
    __metadata("design:type", String)
], BaseSpecialtyMeasures.prototype, "templateName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "quota_standard" }),
    __metadata("design:type", String)
], BaseSpecialtyMeasures.prototype, "quotaStandard", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "specialty_code" }),
    __metadata("design:type", String)
], BaseSpecialtyMeasures.prototype, "specialtyCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "cslb_name" }),
    __metadata("design:type", String)
], BaseSpecialtyMeasures.prototype, "cslbName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "measures_type" }),
    __metadata("design:type", String)
], BaseSpecialtyMeasures.prototype, "measuresType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "bd_code" }),
    __metadata("design:type", String)
], BaseSpecialtyMeasures.prototype, "bdCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "data_order" }),
    __metadata("design:type", Number)
], BaseSpecialtyMeasures.prototype, "dataOrder", void 0);
BaseSpecialtyMeasures = __decorate([
    (0, typeorm_1.Entity)({ name: "base_specialty_measures" })
], BaseSpecialtyMeasures);
exports.BaseSpecialtyMeasures = BaseSpecialtyMeasures;
//# sourceMappingURL=BaseSpecialtyMeasures.js.map