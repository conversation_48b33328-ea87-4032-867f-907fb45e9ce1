"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitFeeFile = void 0;
const BaseModel_1 = require("./BaseModel");
/**
 * 单位取费文件
 */
class UnitFeeFile extends BaseModel_1.BaseModel {
    comparisonFieldValue() {
        return this.anwenRateAddMarkFlag == 1 && this.profitMarkFlag == 1 && this.feesMarkFlag == 1 && this.anwenRateAddMarkFlag == 1 && this.anwenRateBaseMarkFlag == 1;
    }
    setCostOverview(sequenceNbr, feeCode, feeFileId, feeFileCode, feeFileName, sortNo, managementFee, profit, anwenRateBase, anwenRateAdd, managementFeeBackUp, profitBackUp, anwenRateBaseBackUp, anwenRateAddBackUp) {
        this.sequenceNbr = sequenceNbr;
        this.feeCode = feeCode;
        this.feeFileId = feeFileId;
        this.feeFileCode = feeFileCode;
        this.feeFileName = feeFileName;
        this.sortNo = sortNo;
        this.managementFee = managementFee;
        this.profit = profit;
        this.anwenRateBase = anwenRateBase;
        this.anwenRateAdd = anwenRateAdd;
        this.managementFeeBackUp = managementFeeBackUp;
        this.profitBackUp = profitBackUp;
        this.anwenRateBaseBackUp = anwenRateBaseBackUp;
        this.anwenRateAddBackUp = anwenRateAddBackUp;
        return this;
    }
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, constructId, singleId, unitId, feeCode, feeFileId, feeFileCode, feeFileName, sortNo, managementFee, managementFeeBackUp, managementFeeMarkFlag, managementFeeMarkFlagBlue, profit, profitBackUp, profitMarkFlag, profitMarkFlagBlue, fees, feesBackUp, feesMarkFlag, feesMarkFlagBlue, anwenRateBase, anwenRateBaseBackUp, anwenRateBaseMarkFlag, anwenRateBaseMarkFlagBlue, anwenRateAdd, anwenRateAddBackUp, anwenRateAddMarkFlag, anwenRateAddMarkFlagBlue, additionalTax, unitFeeDescription, lastUpdatedValueCache) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.constructId = constructId;
        this.singleId = singleId;
        this.unitId = unitId;
        this.feeCode = feeCode;
        this.feeFileId = feeFileId;
        this.feeFileCode = feeFileCode;
        this.feeFileName = feeFileName;
        this.sortNo = sortNo;
        this.managementFee = managementFee;
        this.managementFeeBackUp = managementFeeBackUp;
        this.managementFeeMarkFlag = managementFeeMarkFlag;
        this.managementFeeMarkFlagBlue = managementFeeMarkFlagBlue;
        this.profit = profit;
        this.profitBackUp = profitBackUp;
        this.profitMarkFlag = profitMarkFlag;
        this.profitMarkFlagBlue = profitMarkFlagBlue;
        this.fees = fees;
        this.feesBackUp = feesBackUp;
        this.feesMarkFlag = feesMarkFlag;
        this.feesMarkFlagBlue = feesMarkFlagBlue;
        this.anwenRateBase = anwenRateBase;
        this.anwenRateBaseBackUp = anwenRateBaseBackUp;
        this.anwenRateBaseMarkFlag = anwenRateBaseMarkFlag;
        this.anwenRateBaseMarkFlagBlue = anwenRateBaseMarkFlagBlue;
        this.anwenRateAdd = anwenRateAdd;
        this.anwenRateAddBackUp = anwenRateAddBackUp;
        this.anwenRateAddMarkFlag = anwenRateAddMarkFlag;
        this.anwenRateAddMarkFlagBlue = anwenRateAddMarkFlagBlue;
        this.additionalTax = additionalTax;
        this.unitFeeDescription = unitFeeDescription;
        this.lastUpdatedValueCache = lastUpdatedValueCache;
    }
}
exports.UnitFeeFile = UnitFeeFile;
//# sourceMappingURL=UnitFeeFile.js.map