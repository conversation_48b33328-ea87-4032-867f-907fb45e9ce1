"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasePolicyDocument = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 政策文件
 */
let BasePolicyDocument = class BasePolicyDocument extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, areaId, cityName, pricesource, name, sketch, releaseDate, executeDate, fileDate, fileType, fileUrl, remark, zhygLevel1, zhygLevel2, zhygLevel3, selectFlag) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.areaId = areaId;
        this.cityName = cityName;
        this.pricesource = pricesource;
        this.name = name;
        this.sketch = sketch;
        this.releaseDate = releaseDate;
        this.executeDate = executeDate;
        this.fileDate = fileDate;
        this.fileType = fileType;
        this.fileUrl = fileUrl;
        this.remark = remark;
        this.zhygLevel1 = zhygLevel1;
        this.zhygLevel2 = zhygLevel2;
        this.zhygLevel3 = zhygLevel3;
        this.selectFlag = selectFlag;
    }
};
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "area_id", nullable: true }),
    __metadata("design:type", Number)
], BasePolicyDocument.prototype, "areaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "city_name", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "cityName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "pricesource", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "pricesource", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "name", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "sketch", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "sketch", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "release_date", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "releaseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "execute_date", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "executeDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_date", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "fileDate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "file_type", nullable: true }),
    __metadata("design:type", Number)
], BasePolicyDocument.prototype, "fileType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "file_url", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "fileUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "remark", nullable: true }),
    __metadata("design:type", String)
], BasePolicyDocument.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zhyg_level1", nullable: true }),
    __metadata("design:type", Number)
], BasePolicyDocument.prototype, "zhygLevel1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zhyg_level2", nullable: true }),
    __metadata("design:type", Number)
], BasePolicyDocument.prototype, "zhygLevel2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "zhyg_level3", nullable: true }),
    __metadata("design:type", Number)
], BasePolicyDocument.prototype, "zhygLevel3", void 0);
BasePolicyDocument = __decorate([
    (0, typeorm_1.Entity)({ name: "base_policy_document" }),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, String, Number, String, String, String, String, String, String, String, Number, String, String, Number, Number, Number, Number])
], BasePolicyDocument);
exports.BasePolicyDocument = BasePolicyDocument;
//# sourceMappingURL=BasePolicyDocument.js.map