"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitCostSummary = void 0;
const BaseModel_1 = require("./BaseModel");
class UnitCostSummary extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, unitId, code, name, calculateFormula, instructions, type, rate, price, remark, orderNum, dispNo, whetherPrint, whetherTax) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.unitId = unitId;
        this.code = code;
        this.name = name;
        this.calculateFormula = calculateFormula;
        this.instructions = instructions;
        this.type = type;
        this.rate = rate;
        this.price = price;
        this.remark = remark;
        this.orderNum = orderNum;
        this.dispNo = dispNo;
        this.whetherPrint = whetherPrint;
        this.whetherTax = whetherTax;
    }
}
exports.UnitCostSummary = UnitCostSummary;
//# sourceMappingURL=UnitCostSummary.js.map