<!--
 * @Descripttion: 
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2023-08-15 15:40:09
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-18 18:02:24
-->
<template>
  <div class="head">
    <div class="head-topic">
      <div class="loc">
        <img
          :src="getUrl('newCsProject/loc.png')"
          alt=""
        />
        <a-select
          :value="asideData.value"
          :size="asideData.size"
          style="width: 150px"
          :options="asideData.locationList"
        ></a-select>
      </div>
      <div class="title">
        <img
          :src="getUrl('newCsProject/logo.png')"
          alt=""
        />
        <!-- <span style="font-size:15px">学习版</span> -->
        <span class="version"> V{{ versionNow }} </span>

      </div>
      <user-info-show
        :loginType="store.loginUserInfo?.loginType"
        :userInfo="store.loginUserInfo?.userInfo"
        :infoList="store.loginUserInfo?.changeInfoList"
        @layoutOrChange="layoutOrChange"
        :type="'zkt'"
      ></user-info-show>
      <div class="operate">
        <div
          class="operate-icon"
          @click="toMin"
        >
          <img :src="getUrl('newCsProject/operate-minimize.png')" />
        </div>
        <div
          class="operate-icon"
          v-show="!isMax"
          @click="toMaX"
        >
          <img :src="getUrl('newCsProject/operate-maximize.png')" />
        </div>
        <div
          class="operate-icon"
          v-show="isMax"
          @click="toMaX"
        >
          <img :src="getUrl('newCsProject/operate-reduction.png')" />
        </div>
        <div
          class="operate-icon"
          @click="closeModel=true"
        >
          <img :src="getUrl('newCsProject/operate-close.png')" />
        </div>
      </div>
    </div>
    <div class="head-operate"></div>
    <header-menu class="header-menu"></header-menu>
    <a-modal
      dialogClass="modal"
      destroyOnClose
      :style="{ width: modalWidth }"
      :bodyStyle="{ padding: 0, width: '200px' }"
      centered
      :title="null"
      :footer="null"
      v-model:visible="visible"
      @cancel="closeAll"
      @ok="handleOk"
    >
      <login-modal v-model:visible="visible" />
    </a-modal>
  </div>
  <common-modal
    className="titleNoColor noHeader"
    title=" "
    width="350"
    height="200"
    v-model:modelValue="closeModel"
    :mask="false"
  >
    <div class="closeModel">
      <p class="closeModel-title">
        <icon-font
          class="icon-font"
          type="icon-querenshanchu"
        ></icon-font>
        您点击了关闭按钮，您是想：
      </p>
      <a-radio-group
        v-model:value="selectCloseType"
        class="closeModel-radio"
      >
        <a-radio :value="1">最小化到系统托盘区，不退出程序</a-radio>
        <a-radio :value="2">退出程序</a-radio>
      </a-radio-group>
      <p class="closeModel-btn">
        <vxe-button
          type="submit"
          status="primary"
          @click="sureClose()"
        >确定</vxe-button>
        <vxe-button
          type="reset"
          @click="closeModel=false"
        >取消</vxe-button>
      </p>
    </div>
  </common-modal>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, watchEffect } from 'vue';
import { getUrl } from '@/utils/index';
import system from '@/api/system';
import { message, Modal } from 'ant-design-vue';
import csProject from '../../../api/csProject';
import loginModal from './login-modal.vue';
import userInfoShow from './userInfoShow.vue';
import headerMenu from './header-menu.vue';
import { proModelStore } from '@/store/proModel.js';
import infoMode from '@/plugins/infoMode.js';

import { changeAgency, getVip, signIn, logout } from '@/api/auth';
const { ipcRenderer } = require('electron');
const isMax = ref(false);
const modalWidth = ref('1300px');
const visible = ref(false);
const proStore = proModelStore();
let userInfo = ref({
  //已登录身份
});
let showInfo = ref(null); //默认展示的身份码
let infoList = reactive([]);
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
//判断当前用户是否是vip
const judgeISvip = async code => {
  await getVip(code).then(res => {
    // console.log('res', res);
    userInfo.value.isvip = res.result?.vip;
  });
};
const saveLoginInfo = async type => {
  //保存用户登录信息
  let obj = {
    sequenceNbr: type === 'offlineLogin' ? null : userInfo.value?.sequenceNbr,
    identity: type === 'offlineLogin' ? null : userInfo.value?.agencyCode,
    loginType: type,
  };
  if (type !== 'offlineLogin') {
    let token = localStorage.getItem('token');
    store.SET_LAST_INFO({ agencyCode: userInfo.value?.agencyCode });
    // const bsToken = await signIn(token.replace(/bearer\s+/g, ''));
    // if (bsToken && bsToken.access_token) {
    // store.SET_TOKEN_LIST({
    //   agencyCode:userInfo.value?.agencyCode
    // });
    // } else {
    //   store.SET_TOKEN_LIST(null);
    // }
  } else {
    store.SET_LAST_INFO({ agencyCode: null });
  }
  let data = JSON.stringify(obj);
  console.log('saveIdInformation', data);
  csProject.saveIdInformation(data).then(res => {
    proStore.SET_Refresh(!proStore.isRefresh);
  });
};
watch(
  () => [store.userInfoList, showInfo.value],
  async () => {
    await getLoginInfo();
  }
);
const getLoginInfo = async () => {
  let userList = [];
  //用户上次展示身份
  if (store.lastInfo?.agencyCode) {
    showInfo.value = store.lastInfo?.agencyCode;
  }
  //登录方式
  if (store.userInfoList?.loginType) {
    if (store.userInfoList?.loginType === 'login') {
      if (store.userInfoList) {
        userList = store.userInfoList.userList;
      }
      let isFind = userList.find(item => item.agencyCode === showInfo.value);
      userInfo.value = showInfo.value && isFind ? isFind : userList[0];
      if (userList.length === 1) {
        infoList = [];
      } else {
        infoList = userList.filter(item => item.agencyCode !== userInfo.value.agencyCode);
      }
      await judgeISvip(userInfo.value.agencyCode);
    } else if (store.userInfoList?.loginType === 'offlineLogin') {
      userInfo.value = {};
      userInfo.value.showName = '离线登录';
      infoList = [];
    }
    const type = store.userInfoList?.loginType === 'offlineLogin' ? 'offlineLogin' : 'inlineLogin';
    store.SET_IS_LOGIN_USER_INFO({
      loginType: type, //当前登录信息
      userInfo: userInfo.value, //当前展示登录用户信息
      changeInfoList: infoList, //用户可切换的身份列表信息
    });
    localStorage.setItem('loginUserInfo', JSON.stringify(store.loginUserInfo)); //用来登录使用
    if (store.loginUserInfo) {
      localStorage.setItem('autoLoginInfo', JSON.stringify(store.loginUserInfo)); //用来自动登录用
    }
    await saveLoginInfo(type); //存储当前登录的用户信息
  } else {
    userInfo.value = {
      showName: '',
      imgSrc: '../../assets/img/logoBlue.png',
    };
    infoList = [];
  }
};
const toMin = () => {
  ipcRenderer.send('window-min');
};
const toMaX = () => {
  ipcRenderer.send('window-max');
  isMaxFun();
};
let versionNow = ref('1.0.0'); //当前系统版本号
const getAppVersion = () => {
  csProject.getAppVersion().then(res => {
    console.log('getAppVersion', res);
    versionNow.value = res;
  });
};
onMounted(() => {
  window.addEventListener('resize', isMaxFun);
  isMaxFun();
  getAppVersion();
  if (localStorage.getItem('loginUserInfo')) {
    store.SET_IS_LOGIN_USER_INFO(
      JSON.parse(localStorage.getItem('loginUserInfo'))
    );
    proStore.SET_Refresh(!proStore.isRefresh);
  }
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', isMaxFun);
});
const isMaxFun = () => {
  setTimeout(() => {
    let { innerWidth, innerHeight } = window;
    let { availWidth, availHeight } = screen;
    isMax.value = innerWidth === availWidth && innerHeight === availHeight;
    modalWidth.value = innerWidth < 1366 ? '453px' : '1300px';
  }, 100);
};
let closeModel = ref(false);
let selectCloseType = ref(1);
const sureClose = async () => {
  if (selectCloseType.value === 2) {
    let result = await csProject.changeIdentity();
    if (result.result?.length > 1) {
      closeModel.value = false; //弹出新的窗口关闭上一个
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '当前存在已打开的工作台项目，请关闭工作台项目后退出程序',
        confirm: () => {
          infoMode.hide();
        },
      });
    } else {
      localStorage.removeItem('searchHistory'); // 清除人材机索引搜索历史
      ipcRenderer.send('window-close', true);
      system.closeAllChildWindow().then(res => {});
    }
  } else {
    //true--退出,false---缩小到托盘
    closeModel.value = false;
    ipcRenderer.send('window-close', false);
  }
};
const asideData = reactive({
  value: '河北省',
  size: '',
  locationList: [
    {
      sequenceNbr: 130000,
      value: '河北省',
    },
  ], //位置获取的列表
  propTitle: '',
  storeIsShow: false,
});

//判断是否可以执行退出登录和切换身份操作
const layoutOrChange = async (type, user = null) => {
  let result = await csProject.changeIdentity();
  let list = result.result || []; //当前打开工作台窗口个数
  if (list.length > 1) {
    if (type === 'changeUserInfo') {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-ruotixing',
        infoText: '当前存在已打开的工作台项目，请关闭工作台项目后切换身份',
        confirm: () => {
          infoMode.hide();
        },
      });
    } else {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '当前存在已打开的工作台项目，请关闭工作台项目后退出登录',
        confirm: () => {
          infoMode.hide();
        },
        // close: () => {
        //   infoMode.hide();
        // },
      });
    }
  } else {
    finallyWay(type, user);
  }
};
const upDateUserList = () => {
  //退出登录更新存储的用户列表信息
  let list = localStorage.getItem('savedPasList');
  list = JSON.parse(list);
  if (list?.length > 0) {
    list[0].autoLogin = false;
    localStorage.setItem('savedPasList', JSON.stringify(list));
  }
};
//退出登录和身份切换接口联调
const finallyWay = (type, user = null) => {
  switch (type) {
    case 'layout':
      // csProject.logout().then(a => {
      //   console.log(a, '退出');
      //   toClose();
      // });
      //总控台退出逻辑修改为删除登录信息重新登录
      upDateUserList();
      localStorage.removeItem('loginUserInfo'); //清除用户信息
      store.SET_IS_LOGIN_USER_INFO(null);
      if (!visible.value) visible.value = true;
      // visible.value = true;
      break;
    case 'changeUserInfo':
      //此处进行身份切换
      changeAgency({ agencyCode: user.agencyCode }).then(res => {
        if (res.status === 200) {
          showInfo.value = user.agencyCode;
          store.SET_LAST_INFO({ agencyCode: user.agencyCode });
          judgeISvip(user.agencyCode);
          message.success('切换成功');
        } else {
          //退出登录清空数据
          message.error(res.message);
          // localStorage.removeItem('loginUserInfo');
          // localStorage.removeItem('token');
          store.SET_IS_LOGIN_USER_INFO(null);
        }
      });
      break;
    default:
  }
  localStorage.removeItem('constructSequenceNbr'); //清除工作台存储信息
};
const closeAll = () => {
  //关闭登录将关闭整个系统
  localStorage.removeItem('searchHistory'); // 清除人材机索引搜索历史
  // logout().then(res => {
  ipcRenderer.send('window-close', true);
  system.closeAllChildWindow().then(res => {});
  // });
};
</script>
<style lang="scss" scoped>
.modal {
  :deep(.ant-modal) {
    zoom: 0.8;
  }
}
.head {
  width: 100vw;
  height: 186px;
  position: relative;
  background: url(("@/assets/img/newCsProject/bg.png"));
  background-size: 100% 100%;
  user-select: none;

  &-topic {
    height: 40px;
    display: flex;
    .loc {
      width: 200px;
      padding: 18px 0px 2px 20px;
      img {
        width: 21px;
        height: 20px;
        margin-right: 5px;
      }
    }
    .title {
      -webkit-app-region: drag; // 设置可拖动
      width: calc(100vw - 450px);
      text-align: center;
      line-height: 50px;
      color: white;
      font-size: 16px;
      text-indent: 50px;
      img {
        position: relative;
        top: -2px;
      }
      .version {
        margin-left: 8px;
        //vertical-align: baseline;
      }
    }
    .operate {
      width: 150px;
      display: flex;
      height: 55px;
      align-items: center;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 25px;
      padding-right: 30px;
      opacity: 1;
      transition: opacity linear 0.2s;
      &-icon {
        width: 20px;
        height: 20px;
      }
      &-icon:hover {
        cursor: pointer;
        transition: opacity linear 0.2s;
        opacity: 0.6;
      }
    }
  }
}
.closeModel {
  &-title {
    font-size: 13px;
    font-weight: 500;
  }
  &-radio {
    margin-left: 16px;
    .ant-radio-wrapper {
      font-size: 12px;
      display: block;
    }
  }
  &-btn {
    margin-top: 20px;
    float: right;
  }
}
@media (max-width: 1366px) and (max-height: 768px) {
  .header-menu {
    zoom: 0.92;
  }
  .head {
    height: 175px;
  }
}
</style>
