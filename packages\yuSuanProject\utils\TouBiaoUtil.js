// exceljs 所需的 polyfills
// require('core-js/modules/es.promise');
// require('core-js/modules/es.string.includes');
// require('core-js/modules/es.object.assign');
// require('core-js/modules/es.object.keys');
// require('core-js/modules/es.symbol');
// require('core-js/modules/es.symbol.async-iterator');
// require('regenerator-runtime/runtime');

// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../vo/CellVo");
const SheetStyle = require("../vo/SheetStyle");
const {ObjectUtils} = require("./ObjectUtils");
const {ExcelUtil} = require("./ExcelUtil");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../../core');
class TouBiaoUtil {

    constructor() {
    }
    /********工程项目层级*************/
    //封面3 投标总价
    async writeDataToCover1(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName!=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"投  标  人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充造价咨询人
        let adviceCell = ExcelUtil.findValueCell(worksheet,"造价咨询人：");
        if (adviceCell != null) {
            let filterAdvice = data.filter(object => object.name=="造价咨询人")[0];
            let adviceRow = worksheet.getRow(adviceCell.cell._row._number);
            if (filterAdvice!=null && filterAdvice.remark != null) {
                adviceRow._cells[4].value = filterAdvice.remark;
            }
            ExcelUtil.traversalRowToCellBottom(adviceRow);
        }
        //填充编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    //扉页3
    async writeDataToCover2(data, worksheet) {
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招 标 人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程名称：");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName!=null && filterProjectName.remark != null) {
            projectRow._cells[2].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充投标总价 小写
        let xiaoXie = ExcelUtil.findValueCell(worksheet,"投标总价　");
        let filterXiaoXie = data.filter(object => object.name=="招标控制价小写")[0];
        let xiaoXieRow = worksheet.getRow(xiaoXie.cell._row._number);
        if (filterXiaoXie!=null && filterXiaoXie.remark != null) {
            xiaoXieRow._cells[4].value = filterXiaoXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(xiaoXieRow);
        //填充投标总价 大写
        let daXie = ExcelUtil.findValueCell(worksheet,"(大写)：");
        let filterDaXie = data.filter(object => object.name=="招标控制价大写")[0];
        let daXieRow = worksheet.getRow(daXie.cell._row._number);
        if (filterDaXie!=null && filterDaXie.remark != null) {
            daXieRow._cells[4].value = filterDaXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(daXieRow);
        //填充投标人
        let tbCell = ExcelUtil.findValueCell(worksheet,"投 标 人：");
        let filterTb = data.filter(object => object.name=="投标人")[0];
        let tbRow = worksheet.getRow(tbCell.cell._row._number);
        if (filterTb!=null && filterTb.remark != null) {
            tbRow._cells[4].value = filterTb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(tbRow);
        //填充委托代理人
        let agentCell = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell != null) {
            let filterAgent = data.filter(object => object.name=="投标人(承包人)法人或其授权人")[0];
            let agentRow = worksheet.getRow(agentCell.cell._row._number);
            if (filterAgent!=null && filterAgent.remark != null) {
                agentRow._cells[4].value = filterAgent.remark;
            }
            ExcelUtil.traversalRowToCellBottom(agentRow);
        }

        //造价员
        let priceCell = ExcelUtil.findValueCell(worksheet,"造价工程师　\n或造 价 员：");
        if (priceCell != null) {
            let engineer = data.filter(object => object.name=="造价工程师或造价员")[0];
            let priceRow = worksheet.getRow(priceCell.cell._row._number);
            if (engineer!=null && engineer.remark != null) {
                priceRow._cells[4].value = engineer.remark;
            }
            ExcelUtil.traversalRowToCellBottom(priceRow);
        }
        //编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    /********单位工程层级*************/
    //封面3 投标总价
    async writeUnitDataToCover1(data, worksheet) {
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName!=null && filterProjectName.remark != null) {
            projectRow._cells[3].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"投  标  人：");
        let filterZb = data.filter(object => object.name=="投标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }
    //扉页3 投标总价
    async writeUnitDataToCover2(data, worksheet) {
        //填充招标人
        let zbCell = ExcelUtil.findValueCell(worksheet,"招 标 人：");
        let filterZb = data.filter(object => object.name=="招标人")[0];
        let zbRow = worksheet.getRow(zbCell.cell._row._number);
        if (filterZb!=null && filterZb.remark != null) {
            zbRow._cells[4].value = filterZb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(zbRow);
        //填充工程名称
        let project = ExcelUtil.findValueCell(worksheet,"工程名称：");
        let filterProjectName = data.filter(object => object.name=="工程名称")[0];
        let projectRow = worksheet.getRow(project.cell._row._number);
        if (filterProjectName!=null && filterProjectName.remark != null) {
            projectRow._cells[2].value = filterProjectName.remark;
        }
        ExcelUtil.traversalRowToCellBottom(projectRow);
        //填充投标总价 小写
        let xiaoXie = ExcelUtil.findValueCell(worksheet,"投标总价　");
        let filterXiaoXie = data.filter(object => object.name=="招标控制价小写")[0];
        let xiaoXieRow = worksheet.getRow(xiaoXie.cell._row._number);
        if (filterXiaoXie!=null && filterXiaoXie.remark != null) {
            xiaoXieRow._cells[4].value = filterXiaoXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(xiaoXieRow);
        //填充投标总价 大写
        let daXie = ExcelUtil.findValueCell(worksheet,"(大写)：");
        let filterDaXie = data.filter(object => object.name=="招标控制价大写")[0];
        let daXieRow = worksheet.getRow(daXie.cell._row._number);
        if (filterDaXie!=null && filterDaXie.remark != null) {
            daXieRow._cells[4].value = filterDaXie.remark;
        }
        ExcelUtil.traversalRowToCellBottom(daXieRow);
        //填充投标人
        let tbCell = ExcelUtil.findValueCell(worksheet,"投 标 人：");
        let filterTb = data.filter(object => object.name=="投标人")[0];
        let tbRow = worksheet.getRow(tbCell.cell._row._number);
        if (filterTb!=null && filterTb.remark != null) {
            tbRow._cells[4].value = filterTb.remark;
        }
        ExcelUtil.traversalRowToCellBottom(tbRow);
        //填充委托代理人
        let agentCell = ExcelUtil.findValueCell(worksheet,"法定代表人或\n委托代理人：");
        if (agentCell != null) {
            let filterAgent = data.filter(object => object.name=="法定代表人或委托代理人")[0];
            let agentRow = worksheet.getRow(agentCell.cell._row._number);
            if (filterAgent!=null && filterAgent.remark != null) {
                agentRow._cells[4].value = filterAgent.remark;
            }
            ExcelUtil.traversalRowToCellBottom(agentRow);
        }

        //造价员
        let priceCell = ExcelUtil.findValueCell(worksheet,"造价工程师　\n或造 价 员：");
        if (priceCell != null) {
            let engineer = data.filter(object => object.name=="造价工程师")[0];
            let priceRow = worksheet.getRow(priceCell.cell._row._number);
            if (engineer!=null && engineer.remark != null) {
                priceRow._cells[4].value = engineer.remark;
            }
            ExcelUtil.traversalRowToCellBottom(priceRow);
        }
        //编制时间
        let importCell = ExcelUtil.findValueCell(worksheet,"编制时间：");
        let filterImport = data.filter(object => object.name=="编制时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport!=null && filterImport.remark != null) {
            importRow._cells[4].value = filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }


}
module.exports = {
    TouBiaoUtil: new TouBiaoUtil()
};
