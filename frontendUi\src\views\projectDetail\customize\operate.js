/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON>qi<PERSON>
 * @Date: 2023-08-24 15:23:17
 * @LastEditors: wangru
 * @LastEditTime: 2025-07-08 17:04:16
 */
import { ref } from 'vue';
import { getUrl } from '@/utils/index';

/**
 * showProjectType 预算没值或者有值存在ys显示，结算显示没值或者有值存在yssh显示
 */
const operateList = ref([
  // 1. 添加按钮的位置与层级：在编制菜单下，增加按钮【费用查看】，覆盖所有层级，含单位工程层级，所有单项工程层级（含一级到N级），工程项目层级，位置为功能栏第一个按钮，
  //        只有4个页签不同，单位工程层级的“分部分项页签”、“措施项目页签”、“其他项目页签”、“人材机汇总页签”，    在这些页签中，放置到【项目自检】的左边；
  {
    label: '费用查看',
    name: 'view-fee',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    public: true,
    showProjectType: ['ys'],
    iconType: 'icon-feiyongchakan',
    icon: getUrl('operate/icon-vertical-transport.png'),
    components: [
      'CostAnalysis',
      'basicInfo',
      'engineerFeature',
      'feeWithDrawalTable',
      'summaryExpense',
      'qtxmZlje',
      'qtxmZygczgj',
      'qtxmJrg',
      'qtxmZcbfwf',
    ],
    //'measuresItem', 'subItemProject', 'humanMachineSummary', 'qtxmStatistics',
  },
  //返回项目编辑
  {
    label: '应用编辑',
    name: 'use-edit',
    levelType: [3],
    windows: ['childPage'], //只应用与标准组价打开的子工作台   parentPage----非标准组价的工作台
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-yingyong',
    components: ['subItemProject', 'measuresItem', 'humanMachineSummary'],
  },
  {
    label: '返回项目编辑',
    name: 'return-proEdit',
    levelType: [3],
    windows: ['childPage'], //只应用与标准组价打开的子工作台
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-fanhuixiangmubianji',
    components: ['subItemProject', 'measuresItem', 'humanMachineSummary'],
  },
  // 项目概况类插入
  {
    label: '插入',
    name: 'insert',
    levelType: [1, 2, 3],
    windows: ['childPage', 'parentPage'],
    icon: getUrl('operate/icon-insert.png'),
    iconType: 'icon-cs-charu', //icon-font的type
    iconStyle: {
      width: '28px',
      position: 'relative',
      left: '-4px',
    },
    provideFun: 'insertHandle',
    parameter: null,
    components: [
      'basicInfo',
      'engineerFeature',
      'qtxmZlje',
      'qtxmZygczgj',
      'summaryExpense',
      'qtxmQzysp',
      'qtxmStatistics',
      'humanMachineSummary',
      // 'qtxmStatistics',-----暂时隐藏
    ],
  },

  {
    label: '锁定',
    name: 'lock',
    levelType: [1],
    windows: ['childPage', 'parentPage'],
    infoDec: '锁定/解锁工程概况【招标信息】下内容的编辑状态',
    icon: getUrl('operate/icon-lock.png'),
    iconType: 'icon-cs-suodingzhaobiaoxinxi',
    provideFun: 'resetLockHandle',
    parameter: null,
    components: ['basicInfo'],
  },
  {
    label: '删除',
    name: 'delete',
    levelType: [1, 2, 3],
    windows: ['childPage', 'parentPage'],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-cs-shanchu',
    provideFun: 'deleteHandle',
    components: ['basicInfo', 'engineerFeature'], //'qtxmStatistics'--功能未开发-暂时隐藏
  },
  // 其他项目插入
  {
    label: '插入',
    name: 'insert-op',
    levelType: [3],
    windows: ['childPage', 'parentPage'],
    iconType: 'icon-cs-charu',
    options: [
      {
        name: '数据行',
        kind: '01',
        isValid: true,
      },
      {
        name: '标题行',
        kind: '02',
        isValid: true,
      },
    ],
    type: 'select',
    icon: getUrl('operate/icon-insert.png'),
    iconStyle: {
      width: '28px',
      position: 'relative',
      left: '-4px',
    },
    provideFun: 'insertHandle',
    parameter: null,
    components: ['qtxmZcbfwf', 'qtxmJrg'],
  },
  {
    label: '费率表',
    name: 'feeExcel',
    levelType: [1, 2, 3],
    windows: ['childPage', 'parentPage'],
    icon: getUrl('operate/icon-export-table.png'),
    iconType: 'icon-biaodan-feishuaibiao',
    provideFun: 'feeExcel',
    iconStyle: {
      fontSize: '19px',
      height: '22px',
      marginTop: '2px',
    },
    labelStyle: {
      marginTop: '3px',
    },
    components: [], //暂时隐藏-'qtxmZcbfwf', 'qtxmJrg', 'qtxmZlje', 'qtxmZygczgj'
  },
  {
    label: '保存',
    name: 'save',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    icon: getUrl('operate/icon-save.png'),
    iconType: 'icon-cs-baocun',
    components: ['PreparationOfInstructions'],
  },
  {
    label: '导出报表',
    name: 'export-table',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    showProjectType: ['yssh', 'ys'],
    icon: getUrl('operate/icon-export-table.png'),
    iconType: 'icon-cs-daochubaobiao',
    components: ['humanMachineSummary'], //暂时隐藏'CostAnalysis', 'humanMachineSummary'
    setIndex: {
      humanMachineSummary: 3,
    },
  },
  {
    label: '固定安文费',
    name: 'fixed-awf',
    windows: ['childPage', 'parentPage'],
    levelType: [1],
    showProjectType: ['yssh', 'jieSuan', 'ys'],
    public: true,
    icon: getUrl('operate/icon-export-table.png'),
    iconType: 'icon-cs-daochubaobiao',
    infoDec: '地区性计取规则，固定安全生产、文明施工费后单位工程中将不再独立计取',
    components: [
      'CostAnalysis',
      'basicInfo',
      'engineerFeature',
      'feeWithDrawalTable',
      'humanMachineSummary',
    ],
  },
  {
    label: '统一应用',
    name: 'unify',
    disabled: false,
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2],
    iconType: 'icon-cs-tongyiyingyong',
    infoDec: '费率调整后需执行【统一应用】生效',
    icon: getUrl('operate/icon-unify.png'),
    components: ['feeWithDrawalTable'],
  },
  {
    label: '计税方式',
    name: 'taxation',
    levelType: [1],
    public: true,
    windows: ['childPage', 'parentPage'],
    iconType: 'icon-cs-jishuifangshi',
    icon: getUrl('operate/icon-taxation.png'),
    infoDec: '便捷用户切换当前工程项目的计税方式【一般计税/简易计税】',
    components: [
      'CostAnalysis',
      'basicInfo',
      'engineerFeature',
      'feeWithDrawalTable',
      'humanMachineSummary',
    ],
  },
  // 分部分项，措施项目操作
  {
    label: '插入',
    name: 'insert-subItem',
    windows: ['parentPage'],
    type: 'select',
    options: [
      {
        type: 0,
        name: '添加标题',
        kind: '01',
        isValid: true,
      },
      {
        type: 1,
        name: '添加子项',
        kind: '02',
        isValid: false,
      },
      {
        type: 2,
        name: '添加清单',
        kind: '03',
        isValid: false,
      },
      {
        type: 3,
        name: '添加子目',
        kind: '04',
        isValid: false,
      },
    ],
    levelType: [3],
    icon: getUrl('operate/icon-insert.png'),
    iconType: 'icon-cs-charu',
    iconStyle: {
      width: '28px',
      position: 'relative',
      left: '-4px',
    },
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
  },
  {
    label: '补充',
    name: 'supplement',
    windows: ['childPage', 'parentPage'],
    type: 'select',
    options: [
      {
        type: 2,
        name: '补充清单',
        kind: '03',
        isValid: false,
      },
      {
        type: 3,
        name: '补充子目',
        kind: '04',
        isValid: false,
      },
      {
        type: 3,
        name: '补充人材机',
        kind: '05',
        isValid: false,
      },
    ],
    levelType: [3],
    icon: getUrl('operate/icon-supplement.png'),
    iconType: 'icon-cs-buchong',
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
  },
  {
    label: '删除',
    name: 'delete-subItem',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    disabled: false,
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-cs-shanchu',
    components: ['subItemProject', 'measuresItem'],
  },
  {
    label: '统一调价',
    name: 'unifiedPriceAdjustment',
    windows: ['parentPage'],
    levelType: [1, 2, 3],
    type: 'select',
    options: [
      {
        name: '造价系数调整',
        kind: '01',
        isValid: true,
      },
    ],
    iconType: 'icon-chakanguanlian',
    icon: '',
    public: true,
    components: [
      'subItemProject',
      'measuresItem',
      'CostAnalysis',
      'basicInfo',
      'engineerFeature',
      'feeWithDrawalTable',
      'humanMachineSummary',
      'summaryExpense',
      'qtxmStatistics',
    ],
  },
  {
    label: '查看关联',
    name: 'viewAssociations',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-chakanguanlian',
    icon: '',
    public: true,
    components: ['subItemProject'],
    showProjectType: ['yssh'],
  },
  {
    label: '导入依据',
    name: 'import-based-on',
    windows: ['parentPage'],
    levelType: [3],
    options: [
      {
        name: '导入依据',
        kind: '01',
        isValid: false,
      },
      {
        name: '查看依据',
        kind: '02',
        isValid: false,
      },
      {
        name: '删除依据',
        kind: '03',
        isValid: false,
      },
    ],
    type: 'select',
    iconType: 'icon-daoruyiju',
    icon: '',
    components: ['subItemProject'],
    showProjectType: ['yssh'],
  },
  {
    label: '重点项过滤审核',
    name: 'key-item-filtering',
    windows: ['parentPage'],
    public: true,
    levelType: [3],
    iconType: 'icon-zhongdianxiangguolvshenhe',
    icon: '',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['yssh'],
  },
  {
    label: '修改送审',
    name: 'modify-submission-for-review',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-xiugaisongshen',
    icon: '',
    components: [
      'subItemProject',
      'measuresItem',
      'humanMachineSummary',
      'qtxmStatistics',
      'summaryExpense',
      'qtxmZcbfwf',
      'qtxmJrg',
      'qtxmZlje',
      'qtxmZygczgj',
    ],
    public: true,
    showProjectType: ['yssh'],
  },
  {
    label: '转为预算',
    name: 'convertTo',
    windows: ['parentPage'],
    levelType: [3],
    options: [
      {
        name: '审定转预算',
        kind: '01',
        isValid: true,
      },
      {
        name: '送审转预算',
        kind: '02',
        isValid: true,
      },
    ],
    type: 'select',
    public: true,
    iconType: 'icon-zhuanweiyusuan',
    icon: '',
    components: [
      'subItemProject',
      'measuresItem',
      'humanMachineSummary',
      'qtxmStatistics',
      'summaryExpense',
      'qtxmZcbfwf',
      'qtxmJrg',
      'qtxmZlje',
      'qtxmZygczgj',
    ],
    showProjectType: ['yssh'],
  },
  {
    label: '数据转换',
    name: 'dataConversion',
    windows: ['parentPage'],
    public: true,
    options: [
      {
        name: '同步送审数据到审定',
        kind: '01',
        isValid: true,
      },
      {
        name: '同步审定数据到送审',
        kind: '02',
        isValid: true,
      },
    ],
    type: 'select',
    levelType: [3],
    iconType: 'icon-shujuzhuanhuan',
    icon: '',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['yssh'],
  },
  {
    label: '对比匹配',
    name: 'comparative-match',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-duibipipei',
    icon: '',
    components: [
      'subItemProject',
      'measuresItem',
      'humanMachineSummary',
      'qtxmStatistics',
      'summaryExpense',
      'qtxmZcbfwf',
      'qtxmJrg',
      'qtxmZlje',
      'qtxmZygczgj',
    ],
    public: true,
    showProjectType: ['yssh'],
  },
  {
    label: '一键审取费',
    name: 'retrieval-fee',
    windows: ['parentPage'],
    public: true,
    levelType: [3],
    iconType: 'icon-yijianshenqufei',
    icon: '',
    components: [
      'subItemProject',
      'measuresItem',
      'humanMachineSummary',
      'qtxmStatistics',
      'summaryExpense',
      'qtxmZcbfwf',
      'qtxmJrg',
      'qtxmZlje',
      'qtxmZygczgj',
    ],
    showProjectType: ['yssh'],
  },
  {
    label: '整体锁定',
    name: 'lock-subItem',
    windows: ['parentPage'],
    levelType: [3],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-cs-suodingzhaobiaoxinxi',
    infoDec: '整体锁定/解锁清单行编辑状态',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys', 'jieSuan'],
  },
  {
    label: '编码重刷',
    name: 'code-reset',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    disabled: false,
    iconType: 'icon-cs-bianmazhongshua',
    infoDec: '刷新当前单位工程中所有清单编码流水号',
    icon: getUrl('operate/icon-code-reset.png'),
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys', 'jieSuan'],
  },
  {
    label: '清单库',
    name: 'qd-library',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    disabled: false,
    showProjectType: ['ys'],
    iconType: 'icon-qingdanku',
    icon: getUrl('operate/icon-code-reset.png'),
    components: ['subItemProject', 'measuresItem'],
    iconStyle: {
      fontSize: '17px',
      padding: '5px',
    },
  },
  {
    label: '定额库',
    name: 'de-library',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    disabled: false,
    showProjectType: ['ys'],
    iconType: 'icon-dingeku',
    icon: getUrl('operate/icon-code-reset.png'),
    components: ['subItemProject', 'measuresItem'],
    iconStyle: {
      fontSize: '17px',
      padding: '5px',
    },
  },
  {
    label: '颜色',
    name: 'select-color',
    type: 'select',
    windows: ['childPage', 'parentPage'],
    options: [
      {
        name: '无色',
        kind: 'none',
        isValid: true,
      },
      {
        name: '红色',
        kind: 'red',
        isValid: true,
      },
      {
        name: '绿色',
        kind: 'green',
        isValid: true,
      },
      {
        name: '橙色',
        kind: 'orange',
        isValid: true,
      },
      {
        name: '黄色',
        kind: 'yellow',
        isValid: true,
      },
      {
        name: '深蓝色',
        kind: 'blue',
        isValid: true,
      },
      {
        name: '淡紫色',
        kind: 'purple',
        isValid: true,
      },
      {
        name: '亮蓝色',
        kind: 'lightBlue',
        isValid: true,
      },
      {
        name: '深黄色',
        kind: 'deepYellow',
        isValid: true,
      },
    ],
    levelType: [3],
    disabled: false,
    iconType: 'icon-yansebiaoji',
    icon: getUrl('operate/icon-code-reset.png'),
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
  },
  {
    label: '装饰垂运',
    name: 'vertical-transport',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-cs-zairumoban',
    infoDec: '装饰装修工程垂直运输费业务计取',
    icon: getUrl('operate/icon-vertical-transport.png'),
    components: ['subItemProject', 'measuresItem'],
  },
  {
    label: '装饰超高',
    name: 'superelevation',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-cs-zhuangshichaogao',
    infoDec: '装饰装修工程超高费业务计取',
    icon: getUrl('operate/icon-superelevation.png'),
    components: ['subItemProject', 'measuresItem'],
  },
  {
    label: '安装费用',
    name: 'installation-costs',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-cs-anzhuangfeiyong',
    infoDec: '安装工程措施项目相关费用业务计取',
    icon: getUrl('operate/icon-installation-costs.png'),
    components: ['subItemProject', 'measuresItem'],
  },
  {
    label: '房修土建费用',
    name: 'civil-construction-costs-of-houses-JQ',
    windows: ['parentPage'],
    type: 'select',
    levelType: [3],
    // value: 1,
    public: true,
    options: [
      {
        type: 1,
        name: '超高费',
        kind: 1,
        isValid: true,
      },
      {
        type: 2,
        name: '垂直运输费',
        kind: 2,
        isValid: true,
      },
      {
        type: 3,
        name: '中小型机械使用费',
        kind: 3,
        isValid: true,
      },
      {
        type: 4,
        name: '工程水电费',
        kind: 4,
        isValid: true,
      },
    ],
    iconType: 'icon-fangxiutujianfeiyongjiqu',
    // infoDec: '安装工程措施项目相关费用业务计取',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
    iconStyle: {
      fontSize: '18px',
      padding: '4px',
    },
  },
  {
    label: '仿古建筑费用',
    name: 'antiqueBuildingCost',
    windows: ['parentPage'],
    type: 'select',
    levelType: [3],
    // value: 1,
    public: true,
    options: [
      {
        type: 1,
        name: '中小型机械使用费',
        kind: 410,
        isValid: true,
      },
    ],
    iconType: 'icon-fangxiutujianfeiyongjiqu',
    // infoDec: '安装工程措施项目相关费用业务计取',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
    iconStyle: {
      fontSize: '18px',
      padding: '4px',
    },
  },
  {
    label: '古建(明清)修缮费用',
    name: 'mingandQingDynastyConstructionCosts',
    windows: ['parentPage'],
    type: 'select',
    levelType: [3],
    // value: 1,
    public: true,
    options: [
      {
        type: 1,
        name: '超高费',
        kind: 520,
        isValid: true,
      },
      {
        type: 2,
        name: '垂直费',
        kind: 530,
        isValid: true,
      },
      {
        type: 3,
        name: '中小型机械使用费',
        kind: 510,
        isValid: true,
      },
    ],
    iconType: 'icon-fangxiutujianfeiyongjiqu',
    // infoDec: '安装工程措施项目相关费用业务计取',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
    iconStyle: {
      fontSize: '18px',
      padding: '4px',
    },
  },
  {
    label: '费用计取',
    name: 'cost-calculation',
    windows: ['parentPage'],
    type: 'menuList',
    menuList: [
      'vertical-transport', // 装饰垂运
      'superelevation', //装饰超高
      'installation-costs', //安装费用
      'civil-construction-costs-of-houses-JQ', //房修土建费用
      'mingandQingDynastyConstructionCosts',
      'antiqueBuildingCost',
      'calculate-commercial', //计算商品砼泵送增加费
    ],
    levelType: [3],
    infoDec: '',
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['subItemProject', 'measuresItem'],
    iconStyle: {
      width: '28px',
      position: 'relative',
    },
    hidden: true,
  },

  {
    label: '便捷组价',
    name: 'convenient-pricing',
    windows: ['parentPage'],
    type: 'menuList',
    menuList: [
      'component-matching', // 组价方案匹配
      'reuse-group-price', //复用组价
      'qd-group-price', //清单快速组价
      'partial-summary', //局部汇总
      'dataReplacement', //数据替换
    ],
    levelType: [3],
    infoDec: '',
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['subItemProject', 'measuresItem'],
    iconStyle: {
      width: '28px',
      position: 'relative',
    },
    hidden: true,
  },

  {
    label: '自动计算措施费用',
    name: 'calculation-measures',
    windows: ['parentPage'],
    levelType: [1, 2, 3],
    infoDec: '自动计算单位工程中其他总价措施费用',
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['measuresItem'],
    iconStyle: {
      width: '28px',
      position: 'relative',
    },
  },
  {
    label: '人材机分期调整',
    name: 'stage-adjustment',
    windows: ['parentPage'],
    levelType: [3],
    public: true,
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-cs-shanchu',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '工程量量差设置',
    name: 'diffSetting',
    windows: ['parentPage'],
    levelType: [3],
    public: true,
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-gongchengliangliangchashezhi',
    components: ['subItemProject'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '结算方式',
    name: 'settlementWay',
    windows: ['parentPage'],
    type: 'selectRadio',
    levelType: [3],
    value: 1,
    options: [
      {
        type: 1,
        name: '可调措施',
        kind: 1,
        isValid: true,
      },
      {
        type: 2,
        name: '总价包干',
        kind: 2,
        isValid: true,
      },
      {
        type: 3,
        name: '按实际发生',
        kind: 3,
        isValid: true,
      },
    ],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-jiesuanfangshi',
    components: ['measuresItem'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '复用合同清单',
    name: 'multiplexContracts',
    windows: ['parentPage'],
    levelType: [3],
    public: true,
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-fuyonghetongqingdan',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '依据文件',
    name: 'accordingDocument',
    windows: ['parentPage'],
    levelType: [3],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-yijuwenjian',
    components: ['subItemProject'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '关联合同清单',
    name: 'associationContracts',
    windows: ['parentPage'],
    levelType: [3],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-guanlianhetongqingdan',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '查看合同关联',
    name: 'viewAssociation',
    windows: ['parentPage'],
    levelType: [3],
    public: true,
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-chakanguanlian',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '展开到',
    name: 'expandLevel',
    type: 'select',
    windows: ['parentPage'],
    levelType: [3],
    options: [
      {
        type: 2,
        name: '展开所有',
        kind: 'all',
        isValid: true,
      },
      {
        type: 3,
        name: '一级分部',
        kind: '1',
        isValid: true,
      },
      {
        type: 3,
        name: '二级分部',
        kind: '2',
        isValid: true,
      },
      {
        type: 3,
        name: '三级分部',
        kind: '3',
        isValid: true,
      },
      {
        type: 3,
        name: '四级分部',
        kind: '4',
        isValid: true,
      },
      {
        type: 3,
        name: '清单',
        kind: 'qd',
        isValid: true,
      },
      {
        type: 3,
        name: '定额',
        kind: 'de',
        isValid: true,
      },
    ],
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-zhankaidao',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '工程量批量乘以系数',
    name: 'quantityBatch',
    windows: ['parentPage'],
    levelType: [3],
    public: true,
    icon: getUrl('operate/icon-delete.png'),
    iconType: 'icon-gongchengliangpiliangchengyixishu',
    components: ['subItemProject'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '计算商品砼泵送增加费',
    name: 'calculate-commercial',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-cs-zidongjisuancuoshifeiyong',
    icon: getUrl('operate/icon-calculation-measures.png'),
    components: ['subItemProject'],
    iconStyle: {
      width: '28px',
      position: 'relative',
    },
  },
  {
    label: '设置汇总范围',
    name: 'setting-aggregate-scope',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2],
    disabled: false,
    showProjectType: ['ys'],
    iconType: 'icon-jubufanwei',
    //infoDec: '待产品输出',
    icon: getUrl('operate/icon-carrying-price.png'),
    components: ['humanMachineSummary'],
    labelStyle: {
      color: 'black',
    },
  },
  {
    label: '从人材机汇总中选择',
    name: 'select-from-machineSummary',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    disabled: false,
    showProjectType: ['ys'],
    iconType: 'icon-jubufanwei',
    asideKey: [8, 9, 10], //侧边菜单列表
    //infoDec: '待产品输出',
    icon: getUrl('operate/icon-carrying-price.png'),
    components: ['humanMachineSummary'],
    labelStyle: {
      color: 'black',
    },
  },
  {
    label: '载价',
    name: 'batch-loadprice',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    disabled: true,
    showProjectType: ['ys', 'jieSuan'],
    iconType: 'icon-cs-zaijia',
    infoDec: '定期更新信息价、市场价数据供您参考使用',
    icon: getUrl('operate/icon-carrying-price.png'),
    components: ['humanMachineSummary'],
  },
  {
    label: '设置主要材料',
    name: 'set-main-materials',
    windows: ['childPage', 'parentPage'],
    levelType: [],
    disabled: false,
    iconType: 'icon-shezhizhuyaocailiao',
    components: ['humanMachineSummary'],
    iconStyle: {
      fontSize: '16px',
      padding: '5px',
    },
  },
  {
    label: '查找',
    windows: ['childPage', 'parentPage'],
    name: 'lookup',
    levelType: [3],
    disabled: false,
    iconType: 'icon-chazhao',
    components: ['humanMachineSummary'],
  },
  // {
  //   label: '载价报告',
  //   name: 'loadprice-report',
  //   levelType: [1, 3],
  // windows: ['childPage', 'parentPage'],
  //   disabled: true,
  //   icon: getUrl('operate/icon-carrying-price.png'),
  //   components: ['humanMachineSummary'],
  // },
  {
    label: '调整市场价系数', //暂时隐藏
    windows: ['childPage', 'parentPage'],
    name: 'market-price',
    levelType: [1, 2, 3],
    showProjectType: ['ys'],
    iconType: 'icon-cs-tiaozhengshichangjiaxishu',
    icon: getUrl('operate/icon-market-price.png'),
    components: ['humanMachineSummary'],
    iconStyle: {
      fontSize: '21px',
      padding: '3px',
    },
  },
  {
    label: '统一应用',
    name: 'unify-humanMachineSummary',
    disabled: true,
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2],
    iconType: 'icon-cs-tongyiyingyong',
    icon: getUrl('operate/icon-unify.png'),
    infoDec: '人材机数据调整后需执行【统一应用】生效',
    components: ['humanMachineSummary'],
  },

  {
    label: '批量选择调差材料',
    name: 'batch-adjustment-material',
    levelType: [1, 3],
    hidden: true,
    public: true,
    windows: ['parentPage'],
    iconType: 'icon-piliangxuanzetiaochacailiao',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '批量设置结算除税系数',
    name: 'batch-set-taxRemoval',
    levelType: [1, 3],
    hidden: true,
    public: true,
    windows: ['parentPage'],
    iconType: 'icon-piliangshezhichushuixishu',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '显示对应子目',
    name: 'corresponding-item',
    levelType: [1, 3],
    hidden: false,
    windows: ['parentPage'],
    iconType: 'icon-xianshiduiyingzimu',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '风险幅度范围',
    name: 'risk-range',
    levelType: [1, 3],
    hidden: true,
    windows: ['parentPage'],
    iconType: 'icon-fengxianfudufanwei',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '自动过滤调差材料',
    name: 'filter-adjustment-material',
    levelType: [1, 3],
    hidden: true,
    public: true,
    windows: ['parentPage'],
    iconType: 'icon-zidongguolvtiaochacailiao',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '价差取费设置',
    name: 'diff-price-setting',
    levelType: [1, 3],
    hidden: true,
    public: true,
    windows: ['parentPage'],
    iconType: 'icon-jiachaqufeishezhi',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '人材机参与调差',
    name: 'rcj-adjustment',
    levelType: [1, 3],
    hidden: true,
    windows: ['parentPage'],
    iconType: 'icon-rencaijicanyutiaocha',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '分期量查看',
    name: 'staged-dose',
    levelType: [1, 3],
    hidden: true,
    public: true,
    windows: ['parentPage'],
    iconType: 'icon-fenqiliangchakan',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '单期/多期调差设置',
    name: 'adjustment-setting',
    levelType: [1, 3],
    hidden: true,
    public: true,
    windows: ['parentPage'],
    iconType: 'icon-a-danqiduoqitiaochashezhi',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '四种结算人材机调整法',
    name: 'settlement-adjustment-method',
    levelType: [1, 3],
    hidden: true,
    windows: ['parentPage'],
    type: 'selectRadio',
    iconType: 'icon-chaetiaozheng',
    icon: getUrl('operate/icon-unify.png'),
    components: ['humanMachineSummary'],
    showProjectType: ['jieSuan'],
    value: 1,
    options: [
      {
        type: 1,
        name: '造价信息价格差额调整法',
        kind: 1,
        isValid: true,
      },
      {
        type: 2,
        name: '结算价与基期价差额调整法',
        kind: 2,
        isValid: true,
      },
      {
        type: 3,
        name: '结算价与合同价差额调整法',
        kind: 3,
        isValid: true,
      },
      {
        type: 4,
        name: '价格指数差额调整法',
        kind: 4,
        isValid: true,
      },
    ],
  },
  {
    label: '费用代码明细',
    name: 'charge-code',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    iconType: 'icon-cs-feiyongdaimamingxi',
    icon: getUrl('operate/icon-charge-code.png'),
    components: ['summaryExpense'],
  },
  {
    label: '安全生产、文明施工费明细',
    windows: ['childPage', 'parentPage'],
    name: 'anwen-fee',
    levelType: [3],
    iconType: 'icon-cs-anwenfeimingxi',
    icon: getUrl('operate/icon-anwen-fee.png'),
    components: ['summaryExpense'],
  },
  {
    label: '规费明细',
    windows: ['childPage', 'parentPage'],
    name: 'fees',
    levelType: [3],
    iconType: 'icon-cs-guifeimingxi',
    icon: getUrl('operate/icon-fees.png'),
    components: ['summaryExpense'],
  },
  {
    label: '价差规费明细',
    name: 'jcFees',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-xiangmuzijian',
    icon: getUrl('operate/icon-fees.png'),
    components: ['summaryExpense'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '价差安、文费明细',
    name: 'jcAnwenFee',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-xiangmuzijian',
    icon: getUrl('operate/icon-fees.png'),
    components: ['summaryExpense'],
    showProjectType: ['jieSuan'],
  },
  {
    label: '保存模板',
    name: 'save-on-mould',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    iconType: 'icon-cs-baocun',
    icon: getUrl('operate/icon-vertical-transport.png'),
    components: ['summaryExpense'], // 'measuresItem', 'qtxmStatistics'
  },
  {
    label: '载入模板',
    name: 'load-on-mould',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    showProjectType: ['ys', 'jieSuan'],
    iconType: 'icon-cs-zairumoban',
    icon: getUrl('operate/icon-vertical-transport.png'),
    components: ['summaryExpense', 'measuresItem', 'qtxmStatistics'],
  },
  {
    label: '批量替换费用表',
    name: 'batch-replace-fee',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    showProjectType: ['ys'],
    iconType: 'icon-piliangtihuanfeiyongbiao',
    icon: getUrl('operate/icon-vertical-transport.png'),
    components: ['summaryExpense'],
    iconStyle: {
      fontSize: '19px',
      padding: '4px',
    },
  },
  {
    label: '批量改主材',
    name: 'mainMaterials',
    windows: ['parentPage'],
    levelType: [3],
    public: true,
    iconType: 'icon-pilianggaizhucai',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
    iconStyle: {
      fontSize: '19px',
      padding: '4px',
    },
  },
  {
    label: '费用查看',
    name: 'view-fee',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    public: true,
    showProjectType: ['ys'],
    iconType: 'icon-feiyongchakan',
    icon: getUrl('operate/icon-vertical-transport.png'),
    components: ['measuresItem', 'subItemProject', 'humanMachineSummary', 'qtxmStatistics'],
  },
  {
    label: '项目自检',
    name: 'selfCheck',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    public: true,
    iconType: 'icon-xiangmuzijian',
    showProjectType: ['ys'],
    infoDec: '智能检查项目中存在的清单定额等数据问题',
    icon: getUrl('operate/icon-fees.png'),
    components: [
      'CostAnalysis',
      'basicInfo',
      'engineerFeature',
      'feeWithDrawalTable',
      'subItemProject',
      'measuresItem',
      'humanMachineSummary',
      'qtxmStatistics',
      'summaryExpense',
      'qtxmZlje',
      'qtxmZygczgj',
      'qtxmJrg',
      'qtxmZcbfwf',
    ],
  },
  {
    label: '合并相似材料',
    name: 'mergeMaterials',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    iconType: 'icon-hebingxiangsicailiao',
    showProjectType: ['ys'],
    components: ['humanMachineSummary'],
  },
  {
    label: '导入excel市场价',
    name: 'importExcel',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    iconType: 'icon-daoruexcel',
    showProjectType: ['ys'],
    components: ['humanMachineSummary'],
    iconStyle: {
      fontSize: '16px',
      padding: '5px',
    },
  },
  {
    label: '计取水、电费',
    name: 'utility-bills',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    showProjectType: ['ys', 'jieSuan'],
    iconType: 'icon-piliangshezhichushuixishu',
    infoDec: '便捷统计施工过程中产生的水、电扣除费用',
    icon: getUrl('operate/icon-fees.png'),
    components: ['summaryExpense'],
  },
  // 分部分项，措施项目操作
  {
    label: '组价方案匹配',
    name: 'component-matching',
    type: 'select',
    options: [
      {
        type: 0,
        name: '方案匹配',
        kind: '01',
        isValid: false,
      },
      {
        type: 1,
        name: '筛选组价数据',
        kind: '02',
        isValid: false,
      },
    ],
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    iconType: 'icon-zujiafanganpipei',
    infoDec: '智能识别分析清单特征内容，快速匹配定额组价方案',
    icon: getUrl('operate/icon-insert.png'),
    iconStyle: {
      width: '28px',
      position: 'relative',
      left: '0px',
    },
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
  },
  {
    label: '复用组价',
    name: 'reuse-group-price',
    type: 'select',
    options: [
      {
        type: 2,
        name: '自动复用组价',
        kind: 0,
        isValid: true,
      },
      {
        type: 3,
        name: '提取已有组价',
        kind: 1,
        isValid: true,
      },
      {
        type: 3,
        name: '提取已有清单',
        kind: 2,
        isValid: true,
      },
    ],
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    icon: getUrl('operate/icon-supplement.png'),
    iconType: 'icon-fuyongzujia',
    infoDec: '便捷复用当前或历史项目已完成的成果数据',
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
  },
  {
    label: '清单快速组价',
    name: 'qd-group-price',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    iconType: 'icon-qingdankuaisuzujia',
    infoDec: '通过清单指引快速完成单位工程编制',
    icon: getUrl('operate/icon-fees.png'),
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
  },
  {
    label: '局部汇总',
    name: 'partial-summary',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    iconType: 'icon-cs-shanchu',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
  },
  {
    label: '过滤',
    name: 'filter-list',
    type: 'selectCheck',
    options: [
      {
        type: 2,
        name: '只显示主要清单',
        kind: 1,
        isValid: true,
      },
      {
        type: 3,
        name: '只显示批注项目',
        kind: 2,
        isValid: true,
      },
      {
        type: 4,
        name: '锁定综合单价',
        kind: 3,
        isValid: true,
      },
    ],
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    icon: getUrl('operate/icon-supplement.png'),
    iconType: 'icon-guolv',
    parameter: null,
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
    iconStyle: {
      fontSize: '25px',
      padding: '0px',
      marginTop: '1px',
    },
    labelStyle: {
      marginTop: '0',
    },
  },
  {
    label: '整理子目',
    name: 'organize-subitems',
    type: 'select',
    windows: ['parentPage'],
    levelType: [3],
    iconStyle: {
      fontSize: '18px',
      padding: '4px',
    },
    options: [
      {
        type: 0,
        name: '分部整理',
        kind: 'fb',
        isValid: true,
      },
      {
        type: 1,
        name: '清单排序',
        kind: 'qd',
        isValid: true,
      },
    ],
    icon: getUrl('operate/icon-zhenglizimu.png'),
    iconType: 'icon-zhenglizimu',
    components: ['subItemProject'],
    showProjectType: ['ys'],
  },
  {
    label: '展开到',
    name: 'expandLevel',
    type: 'select',
    windows: ['parentPage'],
    levelType: [3],
    options: [
      {
        type: 2,
        name: '展开所有',
        kind: 'all',
        isValid: true,
      },
      {
        type: 3,
        name: '一级分部',
        kind: '1',
        isValid: false,
      },
      {
        type: 3,
        name: '二级分部',
        kind: '2',
        isValid: false,
      },
      {
        type: 3,
        name: '三级分部',
        kind: '3',
        isValid: false,
      },
      {
        type: 3,
        name: '四级分部',
        kind: '4',
        isValid: false,
      },
      {
        type: 3,
        name: '清单',
        kind: 'qd',
        isValid: true,
      },
      {
        type: 3,
        name: '定额',
        kind: 'de',
        isValid: true,
      },
      {
        type: 3,
        name: '主材设备',
        kind: 'zcsb',
        isValid: true,
      },
    ],
    icon: getUrl('operate/icon-zhankaidao.png'),
    iconType: 'icon-zhankaidao',
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
  },
  {
    label: '数据替换',
    name: 'dataReplacement',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    disabled: true,
    iconType: 'icon-tihuanshuju',
    infoDec: '批量替换相同清单组价方案',
    icon: getUrl('operate/icon-fees.png'),
    public: true,
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
  },
  {
    label: '删除空白行',
    name: 'delete-blank-row',
    windows: ['childPage', 'parentPage'],
    levelType: [3],
    disabled: false,
    iconType: 'icon-shanchukongbaihang',
    // infoDec: '',
    icon: getUrl('operate/icon-fees.png'),
    components: ['subItemProject', 'measuresItem'],
    showProjectType: ['ys'],
    iconStyle: {
      fontSize: '16px',
      height: '22px',
      marginTop: '5px',
    },
  },
  {
    label: '标准组价',
    name: 'standard-group-price',
    windows: ['parentPage'],
    levelType: [3],
    iconType: 'icon-biaozhunzujia',
    icon: getUrl('operate/icon-fees.png'),
    components: [], //'subItemProject'
    showProjectType: ['ys'],
  },
  {
    label: '人材机无价差',
    name: 'machine-no-spreads',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    disabled: false,
    showProjectType: ['ys'],
    iconType: 'icon-rencaijiwujiacha',
    asideKey: [0, 1, 2, 3, 4, 5, 6], //侧边菜单列表
    //infoDec: '待产品输出',
    icon: getUrl('operate/icon-carrying-price.png'),
    components: ['humanMachineSummary'],
    labelStyle: {
      color: 'black',
    },
  },

  // {
  //   label: '合并相似材料',
  //   name: 'mergeMaterials',
  //   windows: ['childPage', 'parentPage'],
  //   levelType: [1, 3],
  //   disabled: false,
  //   showProjectType: ['ys'],
  //   iconType: 'icon-tihuanshuju',
  //   icon: getUrl('operate/icon-fees.png'),
  //   components: ['humanMachineSummary'],
  // },
  // {
  //   label: '恢复消耗量',
  //   name: 'restore-consumption',
  //   windows: ['childPage', 'parentPage'],
  //   levelType: [1, 2, 3],
  //   disabled: false,
  //   public: true,
  //   showProjectType: ['ys'],
  //   iconType: 'icon-huifumorenfeishuai',
  //   icon: getUrl('operate/icon-fees.png'),
  //   components: ['subItemProject', 'measuresItem'],
  //   iconStyle: {
  //     fontSize: '16px',
  //     height: '22px',
  //     marginTop: '5px',
  //   },
  // },
  {
    label: '五金工具',
    name: 'hardware-tools',
    windows: ['childPage', 'parentPage'],
    levelType: [1, 2, 3],
    disabled: false,
    showProjectType: ['ys'],
    iconType: 'icon-wujingongju',
    icon: getUrl('operate/icon-fees.png'),
    components: ['humanMachineSummary', 'subItemProject', 'measuresItem'],
    iconStyle: {
      fontSize: '16px',
      height: '22px',
      marginTop: '5px',
    },
  },
]);

export const updateOperateByName = (name, callback) => {
  const info = operateList.value.find(item => item.name === name);
  callback(info);
};

export default operateList;
