"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeRelationVariableDescribe = void 0;
const typeorm_1 = require("typeorm");
/**
 * 12定额父级定额关联表
 */
let BaseDeRelationVariableDescribe = class BaseDeRelationVariableDescribe {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "list_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "listCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "job_content", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "jobContent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "deId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "libraryName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code_f", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "deCodeF", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name_f", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "deNameF", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "groupid", nullable: true }),
    __metadata("design:type", String)
], BaseDeRelationVariableDescribe.prototype, "groupid", void 0);
BaseDeRelationVariableDescribe = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_relation_variable_describe" })
], BaseDeRelationVariableDescribe);
exports.BaseDeRelationVariableDescribe = BaseDeRelationVariableDescribe;
//# sourceMappingURL=BaseDeRelationVariableDescribe.js.map