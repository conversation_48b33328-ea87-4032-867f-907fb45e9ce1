const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const {ObjectUtil} = require("../../../common/ObjectUtil");

/**
 * 补充清单 和 定额
 */
class GljSupplementController extends Controller {
    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 是否是标准人材机
     * code 人材机编码
     */
    isStandRcj(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.gongLiaoJiProject.gljSupplementService.isStandRcj(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }




    /**
     * 判断是否是本定额册下的标准定额
     */
    isMainStandQd(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.gongLiaoJiProject.gljSupplementService.isMainStandDeCode(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }
    /**
     * 判断是否是标准定额
     */
    isStandDe(args) {
        let {constructId, singleId, unitId, code,libraryCode} = args;
        let res = this.service.gongLiaoJiProject.gljSupplementService.isStandDeCode(constructId, singleId, unitId, code,libraryCode);

        return ResponseData.success(res);
    }


    /**
     * 是否是主定额库的标准编码
     */
    isMainLibStandRcj(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.gongLiaoJiProject.gljSupplementService.isMainLibStandRcj(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }



    /**
     * 人材机是否已经 在单位下 存在
     * code 人材机编码
     */
    isRcjExist(args) {
        let {constructId, singleId, unitId, code} = args;
        let res = this.service.gongLiaoJiProject.gljSupplementService.rcjCodeExist(constructId, singleId, unitId, code);

        return ResponseData.success(res);
    }

    /**
     * 从界面补充人材机
     * pointLine  选中行 可能是操作区的行数据 也可能是明细区域的行数据 反正再在哪里给哪里
     * pageInfo{
     *     name 名称
     *     code 编码
     *     kind 数字那个 1 2 3 。。。。。。  getTypeList 返回的
     *     type 文字那个 人工费 材料费  机械费 。。。。。。
     *     unit 单位
     *     specification 规格型号
     *     price 单价
     *     taxRemoval 出水系数
     * }
     */
    async spRcjByPage(args) {
        let {constructId, singleId, unitId, pointLine, pageInfo, region} = args;
        await this.service.gongLiaoJiProject.gljSupplementService.spRcjByEmpty(constructId, singleId, unitId, pointLine, pageInfo, null, region, null, pointLine.deId);
        return ResponseData.success(true);
    }

    /**
     * 根据编码补充人材机
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine  选中行 可能是操作区的行数据 也可能是明细区域的行数据 反正再在哪里给哪里
     * @param code      编码
     * @param region  region 0 操作区  1 明细区
     */
    async spByCode(args, redo="按编码补充- -人材机") {
        let {constructId, singleId, unitId, pointLine, code, deId, region, rcjId,type} = args;
        let libraryCode= ProjectDomain.getDomain(constructId).getProjectById(unitId).deLibrary;
        let params = {"materialCode": code,"libraryCode":libraryCode};
        let baseRcjModel = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjByCodeAndLib(params);
        let memoryRcj = await this.service.gongLiaoJiProject.gljRcjService.getRcjMemory(constructId,unitId);
        if(type === 1 && ObjectUtil.isNotEmpty(memoryRcj)){
               let rcj= memoryRcj.find(item=>  item.materialCode === code.concat('#1') );
               if(ObjectUtil.isNotEmpty(rcj)){
                   params = {"materialCode": code,"libraryCode":rcj.libraryCode};
                   baseRcjModel = await this.service.gongLiaoJiProject.gljBaseRcjService.queryRcjByCodeAndLib(params);
               }
        }
        let rcjDetailId = pointLine.sequenceNbr
        let result = await this.service.gongLiaoJiProject.gljRcjService.replaceRcjData(deId, baseRcjModel, constructId, singleId, unitId, deId, rcjDetailId, rcjId, null, {});
        // await this.service.gongLiaoJiProject.gljSupplementService.spRcjByEmpty(constructId, singleId, unitId, pointLine, pageInfo, null, region, null, pointLine.deId);
        return ResponseData.success(result);
    }

    /**
     * spRcjByEmpty 基础上 加一个参数 type
     * type 类型      1 分部分项 , 2 措施项目
     * @param args
     * @return {ResponseData}
     */
    async spRcjByEmpty(args) {
        let {constructId, singleId, unitId, pointLine, pageInfo, type, region, rootLineId, deItemId} = args;
        await this.service.gongLiaoJiProject.gljSupplementService.spRcjByEmpty(constructId, singleId, unitId, pointLine, pageInfo, type, region, rootLineId, deItemId);
    }

}
GljSupplementController.toString = () => '[class GljSupplementController]';
module.exports = GljSupplementController;
