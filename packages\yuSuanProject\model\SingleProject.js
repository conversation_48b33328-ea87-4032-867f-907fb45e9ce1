"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SingleProject = void 0;
const BaseModel_1 = require("./BaseModel");
/**
 * 单项工程
 */
class SingleProject extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, rcjKindSort, rcjWjcState, recDate, extend1, extend2, extend3, description, stlId, projectCode, projectName, jzmj, address, ssProvince, ssCity, total, bzDate, gfee, safeFee, sbf, cldeYear, zbkzj, zbj, constructId, creator, updater, deptId, status, createDate, updateDate, delFlag, tenantId, projectStatus, sbfTax, sbfCost, sortNo, reportStorageUrls, reportUrl, projectOverview, analysisOfPrices, chargeSetting, partialEntry, measurementItems, rcjCollect, otherProjct, costCollect, subSingleProjects, unitProjects) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.stlId = stlId;
        this.projectCode = projectCode;
        this.projectName = projectName;
        this.jzmj = jzmj;
        this.address = address;
        this.ssProvince = ssProvince;
        this.ssCity = ssCity;
        this.total = total;
        this.average = "0";
        this.bzDate = bzDate;
        this.gfee = gfee;
        this.safeFee = safeFee;
        this.sbf = sbf;
        this.cldeYear = cldeYear;
        this.zbkzj = zbkzj;
        this.zbj = zbj;
        this.constructId = constructId;
        this.creator = creator;
        this.updater = updater;
        this.deptId = deptId;
        this.status = status;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.delFlag = delFlag;
        this.tenantId = tenantId;
        this.projectStatus = projectStatus;
        this.sbfTax = sbfTax;
        this.sbfCost = sbfCost;
        this.sortNo = sortNo;
        this.reportStorageUrls = reportStorageUrls;
        this.reportUrl = reportUrl;
        this.projectOverview = projectOverview;
        this.analysisOfPrices = analysisOfPrices;
        this.chargeSetting = chargeSetting;
        this.partialEntry = partialEntry;
        this.measurementItems = measurementItems;
        this.rcjCollect = rcjCollect;
        this.otherProjct = otherProjct;
        this.costCollect = costCollect;
        this.subSingleProjects = subSingleProjects;
        this.unitProjects = unitProjects;
        this.unitcost = 0;
        this.rcjKindSort = rcjKindSort;
        this.rcjWjcState = rcjWjcState;
    }
}
exports.SingleProject = SingleProject;
//# sourceMappingURL=SingleProject.js.map