"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCarryingPriceInformation = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 载价信息表
 */
let BaseCarryingPriceInformation = class BaseCarryingPriceInformation extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "data_type" }),
    __metadata("design:type", String)
], BaseCarryingPriceInformation.prototype, "dataType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "data_type_name" }),
    __metadata("design:type", String)
], BaseCarryingPriceInformation.prototype, "dataTypeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "area_id" }),
    __metadata("design:type", String)
], BaseCarryingPriceInformation.prototype, "areaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "city_name" }),
    __metadata("design:type", String)
], BaseCarryingPriceInformation.prototype, "cityName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "file_date" }),
    __metadata("design:type", String)
], BaseCarryingPriceInformation.prototype, "fileDate", void 0);
BaseCarryingPriceInformation = __decorate([
    (0, typeorm_1.Entity)()
], BaseCarryingPriceInformation);
exports.BaseCarryingPriceInformation = BaseCarryingPriceInformation;
//# sourceMappingURL=BaseCarryingPriceInformation.js.map