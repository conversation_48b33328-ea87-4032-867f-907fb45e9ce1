"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProject = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProject extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, dispNo, sortNo, extraName, unit, amount, calculationBase, total, expression, jxTotal, csTotal, markSafa, markSj, unitId, spId, constructId, type, instructions, taxRemoval, jxTaxAmount, rate, putOntotalFlag) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.dispNo = dispNo;
        this.sortNo = sortNo;
        this.extraName = extraName;
        this.unit = unit;
        this.amount = amount;
        this.calculationBase = calculationBase;
        this.total = total;
        this.expression = expression;
        this.jxTotal = jxTotal;
        this.csTotal = csTotal;
        this.markSafa = markSafa;
        this.markSj = markSj;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
        this.type = type;
        this.instructions = instructions;
        this.taxRemoval = taxRemoval;
        this.jxTaxAmount = jxTaxAmount;
        this.rate = rate;
        this.putOntotalFlag = putOntotalFlag;
    }
}
exports.OtherProject = OtherProject;
//# sourceMappingURL=OtherProject.js.map