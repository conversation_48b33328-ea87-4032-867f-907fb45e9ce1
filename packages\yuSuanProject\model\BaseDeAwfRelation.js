"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeAwfRelation2022 = exports.BaseDeAwfRelation = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 定额表
 */
let BaseDeAwfRelation = class BaseDeAwfRelation extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fee_file_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation.prototype, "feeFileId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "qf_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "qf_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation.prototype, "qfName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "base_rate", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation.prototype, "baseRate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "add_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseDeAwfRelation.prototype, "addRate", void 0);
BaseDeAwfRelation = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_awf_relation" })
], BaseDeAwfRelation);
exports.BaseDeAwfRelation = BaseDeAwfRelation;
let BaseDeAwfRelation2022 = class BaseDeAwfRelation2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fee_file_id", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation2022.prototype, "feeFileId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation2022.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation2022.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "qf_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation2022.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "qf_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation2022.prototype, "qfName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "base_rate", nullable: true }),
    __metadata("design:type", String)
], BaseDeAwfRelation2022.prototype, "baseRate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "add_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseDeAwfRelation2022.prototype, "addRate", void 0);
BaseDeAwfRelation2022 = __decorate([
    (0, typeorm_2.Entity)({ name: "base_de_awf_relation_2022" })
], BaseDeAwfRelation2022);
exports.BaseDeAwfRelation2022 = BaseDeAwfRelation2022;
//# sourceMappingURL=BaseDeAwfRelation.js.map