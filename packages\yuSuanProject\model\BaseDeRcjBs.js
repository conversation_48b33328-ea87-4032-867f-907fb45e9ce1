"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeRcjBs = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
/**
 * base 泵送费基数费人材机
 */
let BaseDeRcjBs = class BaseDeRcjBs extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: 'library_code', nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjBs.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rcj_id', nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjBs.prototype, "rcjId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'material_code', nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjBs.prototype, "materialCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'material_name', nullable: true }),
    __metadata("design:type", String)
], BaseDeRcjBs.prototype, "materialName", void 0);
BaseDeRcjBs = __decorate([
    (0, typeorm_1.Entity)({ name: 'base_de_rcj_bs' })
], BaseDeRcjBs);
exports.BaseDeRcjBs = BaseDeRcjBs;
//# sourceMappingURL=BaseDeRcjBs.js.map