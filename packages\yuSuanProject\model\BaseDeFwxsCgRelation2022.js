"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeFwxsCgRelation2022 = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
/**
 * base 房修建筑费用记取基础数据表
 */
let BaseDeFwxsCgRelation2022 = class BaseDeFwxsCgRelation2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: 'storey', nullable: true }),
    __metadata("design:type", String)
], BaseDeFwxsCgRelation2022.prototype, "storey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'library_code', nullable: true }),
    __metadata("design:type", String)
], BaseDeFwxsCgRelation2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'de_code', nullable: true }),
    __metadata("design:type", String)
], BaseDeFwxsCgRelation2022.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'de_name', nullable: true }),
    __metadata("design:type", String)
], BaseDeFwxsCgRelation2022.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_xj_yb', nullable: true }),
    __metadata("design:type", Number)
], BaseDeFwxsCgRelation2022.prototype, "isXjYb", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'remark', nullable: true }),
    __metadata("design:type", String)
], BaseDeFwxsCgRelation2022.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'status', nullable: true }),
    __metadata("design:type", Number)
], BaseDeFwxsCgRelation2022.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'value', nullable: true }),
    __metadata("design:type", Number)
], BaseDeFwxsCgRelation2022.prototype, "value", void 0);
BaseDeFwxsCgRelation2022 = __decorate([
    (0, typeorm_1.Entity)({ name: 'base_de_fwxs_cg_relation_2022' })
], BaseDeFwxsCgRelation2022);
exports.BaseDeFwxsCgRelation2022 = BaseDeFwxsCgRelation2022;
//# sourceMappingURL=BaseDeFwxsCgRelation2022.js.map