"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SafeFee = void 0;
const BaseModel_1 = require("./BaseModel");
class SafeFee extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, unitId, costMajorName, costFeeBase, basicRate, addRate, feeAmount) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.unitId = unitId;
        this.costMajorName = costMajorName;
        this.costFeeBase = costFeeBase;
        this.basicRate = basicRate;
        this.addRate = addRate;
        this.feeAmount = feeAmount;
    }
}
exports.SafeFee = SafeFee;
//# sourceMappingURL=SafeFee.js.map