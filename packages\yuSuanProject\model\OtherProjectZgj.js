"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectZgj = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectZgj extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, dispNo, sortNo, parentId, name, attr, unit, price, taxRemoval, jxTotal, csPrice, csTotal, unitId, spId, constructId) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.dispNo = dispNo;
        this.sortNo = sortNo;
        this.parentId = parentId;
        this.name = name;
        this.attr = attr;
        this.unit = unit;
        this.price = price;
        this.taxRemoval = taxRemoval;
        this.jxTotal = jxTotal;
        this.csPrice = csPrice;
        this.csTotal = csTotal;
        this.description = description;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
    }
}
exports.OtherProjectZgj = OtherProjectZgj;
//# sourceMappingURL=OtherProjectZgj.js.map