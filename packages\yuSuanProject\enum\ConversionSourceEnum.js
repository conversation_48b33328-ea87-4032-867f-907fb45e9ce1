"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RCJKind = exports.StandardConvertMod = void 0;
var StandardConvertMod;
(function (StandardConvertMod) {
    // 默认以标准人材机执行
    StandardConvertMod[StandardConvertMod["Default"] = 1] = "Default";
    // 以当前人材机执行
    StandardConvertMod[StandardConvertMod["Current"] = 2] = "Current";
})(StandardConvertMod = exports.StandardConvertMod || (exports.StandardConvertMod = {}));
var RCJKind;
(function (RCJKind) {
    RCJKind[RCJKind["\u5176\u4ED6"] = 0] = "\u5176\u4ED6";
    RCJKind[RCJKind["\u4EBA\u5DE5"] = 1] = "\u4EBA\u5DE5";
    RCJKind[RCJKind["\u6750\u6599"] = 2] = "\u6750\u6599";
    RCJKind[RCJKind["\u673A\u68B0"] = 3] = "\u673A\u68B0";
    RCJKind[RCJKind["\u8BBE\u5907"] = 4] = "\u8BBE\u5907";
    // 材料细分
    RCJKind[RCJKind["\u4E3B\u6750"] = 5] = "\u4E3B\u6750";
    RCJKind[RCJKind["\u5546\u783C"] = 6] = "\u5546\u783C";
    RCJKind[RCJKind["\u783C"] = 7] = "\u783C";
    RCJKind[RCJKind["\u6D46"] = 8] = "\u6D46";
    RCJKind[RCJKind["\u5546\u6D46"] = 9] = "\u5546\u6D46";
    RCJKind[RCJKind["\u914D\u6BD4"] = 10] = "\u914D\u6BD4";
})(RCJKind = exports.RCJKind || (exports.RCJKind = {}));
//# sourceMappingURL=ConversionSourceEnum.js.map