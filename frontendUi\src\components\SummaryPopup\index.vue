<!--
 * @Descripttion: 关联定额
 * @Author: sunchen
 * @Date: 2023-12-25 10:34:45
 * @LastEditors: wangru
 * @LastEditTime: 2024-12-23 16:55:22
-->
<template>
  <common-modal
    className="dialog-comm resizeClass SummaryPopup-dialog"
    width="auto"
    @close="cancel"
    :mask="false"
    :lockScroll="false"
    :lockView="false"
    show-zoom
    resize
    v-model:modelValue="dialogVisible"
    :title="titleName"
  >
    <div class="quota-dialog-content">
      <section class="quota-header">
        <span><b>名称：</b>{{ HeaderData.materialName }}</span>
        <span><b>规格型号：</b>{{ HeaderData.specification }}</span>
        <span><b>单位：</b>{{ HeaderData.unit }}</span>
        <span><b>合计数量：</b>{{ HeaderData.totalNumber }}</span>
      </section>

      <div class="quota-content">
        <div
          class="tree-list"
          v-if="+InitLevelType <3 "
        >
          <a-tree
            v-if="treeData"
            class="quota-tree"
            :tree-data="treeData"
            v-model:selectedKeys="selectedKeys"
            defaultExpandAll
            @select="handleSelect"
            :height="280"
            :fieldNames="{ children: 'children', title: 'name', key: 'id' }"
          >
            <template #switcherIcon="{ switcherCls, children }">
              <down-outlined :class="switcherCls" />
            </template>
            <template #title="{ name }">
              <a-tooltip placement="rightTop">
                <template #title>{{ name }}</template>
                <span class="ellipsis"> {{ name }}</span>
              </a-tooltip>
            </template>
          </a-tree>
        </div>
        <div class="table-list">
          <vxe-table
            align="center"
            show-overflow
            show-header-overflow
            show-footer
            height="300"
            @cell-dblclick="handleCellDblclick"
            :scroll-x="{ enabled: true, gt: 10 }"
            :scroll-y="{ enabled: true, gt: 100 }"
            :row-class-name="
              ({ row }) => {
                return `level-${+row.kind}`;
              }
            "
            :data="tableData"
          >
            <vxe-column
              field="dispNo"
              width="70"
              title="序号"
            ></vxe-column>
            <vxe-column
              field="bdCode"
              width="100"
              title="编码"
            ></vxe-column>
            <vxe-column
              field="name"
              width="120"
              title="名称"
            ></vxe-column>
            <vxe-column
              field="projectAttr"
              width="300"
              show-overflow
              title="项目特征"
            ></vxe-column>
            <vxe-column
              v-if="projectStore.type === 'jieSuan'"
              field="jieSuanGcl"
              width="80"
              title="结算工程量"
            ></vxe-column>
            <vxe-column
              field="total"
              width="80"
              title="合计数量"
            ></vxe-column>
          </vxe-table>
        </div>
      </div>
      <div class="quota-footer">
        <icon-font
          class="icon"
          type="icon-querenshanchu"
          style="margin-right: 5px"
        />
        <span>双击数据行即可跳转</span>
      </div>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import {
  ref,
  reactive,
  watch,
  shallowRef,
  toRaw,
  onDeactivated,
  onUnmounted,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import xeUtils from 'xe-utils';
import { DownOutlined } from '@ant-design/icons-vue';
import infoMode from '@/plugins/infoMode.js';
import { projectDetailStore } from '@/store/projectDetail';

import { useReversePosition } from '@/hooks/useReversePosition.js';
import jiesuanApi from '@/api/jiesuanApi.js';
const { linkagePosition } = useReversePosition();

const store = projectDetailStore();
const props = defineProps({
  levelType: {
    type: Number,
    default: 1,
  },
  HeaderData: {
    type: Object,
    default: {},
  },
});

let InitLevelType = ref(0);
let num = 0;
const stopWatchingCount = watch(
  () => props.levelType,
  () => {
    if (num > 0) {
      stopWatchingCount();
      return;
    }
    num++;
    InitLevelType.value = props.levelType;
  },
  {
    immediate: true,
  }
);

const emits = defineEmits(['closeDialog']);
const route = useRoute();
const treeData = ref(null);
let expandedKeys = ref([]);
let selectedKeys = ref([]);
const dialogVisible = ref(false);
const dataStatus = ref(null);
const tableData = shallowRef([]);
let titleName = ref('关联定额');
let projectStore = projectDetailStore();

watch(
  () => props.HeaderData.sequenceNbr,
  () => {
    open();
  }
);

const getTreeList = async () => {
  let unitId = null;
  let singleId = null;
  // 工程项目级别
  if (InitLevelType.value < 3) {
    const params = {
      constructId: route.query.constructSequenceNbr,
      rcj: { ...props.HeaderData },
    };
    if (InitLevelType.value === 2) {
      params.singleId = store.currentTreeInfo?.id;
    }
    const res = await csProject.getConstructIdTree(
      JSON.parse(JSON.stringify(params))
    );
    if (res.status === 200) {
      treeData.value = xeUtils.toArrayTree(res.result, {
        idKey: 'id',
        parentIdKey: 'parentId',
        childrenKey: 'children',
      });
    }
    const { id, parentId } = findFistNode(treeData.value[0]);
    console.log(findFistNode(treeData.value[0]));
    unitId = id;
    singleId = parentId;
    selectedKeys.value = [id];
  }
  getTableList(unitId, singleId);
};

const handleSelect = (
  selectedKeys,
  { selected, selectedNodes, node, event }
) => {
  console.log('niode', node);
  if (node.children.length === 0 && selected) {
    getTableList(node.dataRef.id, node.dataRef.parentId);
  }
  if (selected) {
    expandedKeys.value = [node.dataRef.projectId];
  }
};

let postUnitId = ref(null);
let postSingleId = ref(null);
const getTableList = async (id = null, singleId = null) => {
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: singleId || store.currentTreeInfo?.parentId,
    unitId: id || store.currentTreeInfo?.id,
    levelType: store.currentTreeInfo?.levelType,
    rcj: {
      ...props.HeaderData,
    },
  };
  postUnitId.value = postData.unitId;
  postSingleId.value = postData.singleId;
  let res;
  if (projectStore.type === 'jieSuan') {
    res = await jiesuanApi.jieSuanGetRcjDe(
      JSON.parse(JSON.stringify(postData))
    );
  } else {
    res = await csProject.loadPriceSetController(
      JSON.parse(JSON.stringify(postData))
    );
  }
  console.log('测试111111111', res);
  if (res.status === 200) {
    tableData.value = res.result;
  }
};

const findFistNode = treeData => {
  let node = null;
  if (treeData?.children && treeData.children.length) {
    node = findFistNode(treeData.children[0]);
  } else {
    node = treeData;
  }

  return node;
};

const handleCellDblclick = async ({ row }) => {
  if ([0, 1].includes(+row.kind)) {
    return;
  }
  let postData = {
    constructId: route.query?.constructSequenceNbr,
    singleId: postSingleId.value,
    unitId: postUnitId.value,
    sequenceNbr: row.sequenceNbr,
  };

  const data = await csProject.existDe(postData);
  if (!data.result) return;
  const { exist, type } = data.result;
  if (exist) {
    const tabMenuName = type === 'fbfx' ? '分部分项' : '措施项目';
    linkagePosition({
      treeId: postUnitId.value,
      tabMenuName,
      rowId: row.sequenceNbr,
    });
  } else {
    beforeClick();
  }
};

const beforeClick = () => {
  infoMode.show({
    isSureModal: true,
    iconType: 'icon-qiangtixing',
    infoText: '未查询到当前数据',
    confirm: () => {
      infoMode.hide();
    },
  });
};

const cancel = () => {
  dialogVisible.value = false;
  dataStatus.value = null;
  emits('closeDialog');
};

const open = () => {
  getTreeList();
  if (projectStore.type === 'jieSuan') {
    titleName.value = '显示对应子目-' + props.HeaderData?.materialName;
  } else {
    titleName.value = '关联定额';
  }
  dialogVisible.value = true;
};

open();
</script>

<style lang="scss">
.SummaryPopup-dialog {
  .vxe-modal--content {
    padding-bottom: 15px !important;
  }
  .vxe-table {
    .level-1 {
      background-color: #d7d7d7;
    }
    .level-2 {
      background-color: #ececec;
    }
    .level-3 {
      background-color: #e9eefa;
    }
  }
  .quota-dialog-content {
    .quota-header {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      span {
        color: rgba(96, 96, 96, 1);
        margin-left: 30px;
        &:first-child {
          margin-left: 0;
        }
        b {
          font-weight: 400;
          color: rgba(0, 0, 0, 1);
        }
      }
    }
  }
  .quota-content {
    display: flex;
    align-items: center;
    .tree-list {
      height: 300px;
      width: 160px;
      padding: 10px 10px;
      background: rgba(255, 255, 255, 0.39);
      border: 1px solid #b9b9b9;
      margin-right: 12px;
    }
  }
  .quota-tree {
    width: 100%;
    position: relative;
    .ant-tree-treenode {
      width: 100%;
      overflow: hidden;
    }
    .ant-tree-node-content-wrapper {
      width: 100%;
      display: flex;
      overflow: hidden;
    }
    .ant-tree-title {
      width: 100%;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .quota-footer {
    display: flex;
    align-items: center;
    margin-top: 16px;
    span {
      font-size: 12px;
      font-weight: 400;
      color: #2a2a2a;
    }
  }
}
</style>
