"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectProvisional = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectProvisional extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, dispNo, sortNo, name, unit, amount, price, provisionalSum, taxRemoval, jxTotal, csPrice, csTotal, unitId, spId, constructId) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.dispNo = dispNo;
        this.sortNo = sortNo;
        this.name = name;
        this.unit = unit;
        this.amount = amount;
        this.price = price;
        this.provisionalSum = provisionalSum;
        this.taxRemoval = taxRemoval;
        this.jxTotal = jxTotal;
        this.csPrice = csPrice;
        this.csTotal = csTotal;
        this.description = description;
        this.unitId = unitId;
        this.spId = spId;
        this.constructId = constructId;
    }
}
exports.OtherProjectProvisional = OtherProjectProvisional;
//# sourceMappingURL=OtherProjectProvisional.js.map