<template>
  <!-- <div> -->
  <!-- <p class="title"><span class="text">费用代码</span></p> -->
  <div class="content">
    <a-textarea
      v-model:value="value"
      placeholder="请输入..."
      :rows="4"
      v-if="props.isTextArea"
      class="textarea"
      @keyup="value = value.replace(/[^0-9a-zA-Z|\_|\*|\+|\/|\.|(|)]/g, '')"
    />
    <div class="selectTree">
      <div class="tree">
        <a-tree
          v-model:expandedKeys="expandedkeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeData"
          @select="selectChildren"
          :defaultExpandAll="true"
        >
          <template #icon="{ key }">
            <template v-if="key !== '0-0'"></template>
          </template>
        </a-tree>
      </div>
    </div>
    <div class="table-content">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{
          isHover: true,
          isCurrent: props.isTextArea ? true : false,
        }"
        :data="tableData"
        height="auto"
        :scroll-y="{ scrollToTopOnChange: true }"
        @current-change="currentRow"
      >
        <vxe-column field="sortNo" width="60" title=""></vxe-column>
        <vxe-column field="code" title="费用代码"></vxe-column>
        <vxe-column field="name" title="费用名称"></vxe-column>
        <vxe-column field="price" :title="title"></vxe-column>
      </vxe-table>
    </div>
  </div>
  <!-- </div> -->
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import feePro from '@/api/feePro';
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
const expandedkeys = ref(['0-0']);
const selectedKeys = ref(['0-0']);
const treeData = ref([]);
const selectTree = ref('');
let title = ref('费用金额');
const selectChildren = e => {
  const select = treeData.value[0].children.filter(item => item.key === e[0]);
  selectTree.value = select && select[0] && select[0].id ? select[0].id : '';
  if (Number(selectTree.value) === 6) {
    title.value = '变量值';
  } else {
    title.value = '费用金额';
  }
  getTableData();
};
let tableData = ref([]);
let value = ref();
const props = defineProps({
  isTextArea: {
    type: Boolean,
  },
  textValue: {
    type: String,
  },
});
onMounted(() => {
  init();
});
const currentRow = row => {
  //选中行的费用代码自动连接到输入框内
  if (value.value) {
    value.value = `${value.value}+${row.row.code}`;
  } else {
    value.value = `${row.row.code}`;
  }
};
const getTableData = () => {
  const formdata = {
    type: selectTree.value + '',
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  console.log('---------单位工程的费用代码和对应金额传参', formdata);
  feePro.costCodePrice(formdata).then(res => {
    console.log('单位工程的费用代码和对应金额', res);
    if (res.status === 200) {
      res.result?.map((item, index) => (item.sortNo = index + 1));
      tableData.value = res.result ? res.result : [];
    }
  });
};
const getTypeList = () => {
  feePro
    .costCodeTypeList({
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId, //单项ID
      unitId: store.currentTreeInfo?.id, //单位ID
    })
    .then(res => {
      if (res.status === 200) {
        getKeyValue(res.result);
      }
    });
};
const getKeyValue = data => {
  let keyList = [];
  let valueList = [];
  data.map(item => {
    keyList.push(item.code);
    valueList.push(item.desc);
  });
  let tree = {
    title: '费用代码',
    key: '0-0',
    children: [],
  };
  keyList.map((item, index) => {
    tree.children.push({
      title: valueList[index],
      key: `0-0-${item}`,
      id: item,
    });
  });
  treeData.value = [tree];
};
const init = () => {
  value.value = props.textValue && props.textValue.calculateFormula;
  getTableData();
  getTypeList();
};
watch(
  () => props.textValue,
  () => {
    console.log('进入watch');
    if (props.isTextArea && store.tabSelectName === '费用汇总') {
      console.log('props.textValue', props.textValue);
      init();
    }
  }
);
watch(
  () => [store.currentTreeInfo, store.tabSelectName],
  () => {
    if (store.tabSelectName === '费用汇总') {
      expandedkeys.value = ['0-0'];
      selectedKeys.value = ['0-0'];
      selectTree.value = '';
      getTableData();
      getTypeList();
    }
  }
);
defineExpose({
  value,
});
</script>
<style lang="scss" scoped>
.title {
  width: 100%;
  background-color: #e7e7e7;
  height: 35px;
  text-align: left;
  margin-top: 15px;
  .text {
    display: inline-block;
    width: 128px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background-color: #f8fbff;
    border-top: 2px solid #4786ff;
  }
}
.content {
  display: flex;
  width: 100%;
  height: 90%;
  // height: calc(100% - 35px - 15px - 14px);
  justify-content: space-around;
  .textarea {
    flex: 1;
  }
  .selectTree {
    // width: 330px;
    flex: 2;
    height: 100%;
    // border: 1px solid #dcdfe6;
    margin: 0 10px;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    // background-color: #f8fbff;
    .tree {
      height: 100%;
      border: 1px solid #dcdfe6;
    }
  }
  .table-content {
    flex: 3;
    // height: 100%;
    // width: calc(100% - 400px);
    overflow: hidden;
    margin-left: 30;
  }
}
.content :deep(.ant-tree) {
  padding-top: 20px;
  height: 100%;
}
</style>
