"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtherProjectQzAndSuoPei = void 0;
const BaseModel_1 = require("./BaseModel");
class OtherProjectQzAndSuoPei extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, dispNo, sortNo, project, unit, amount, type, price, zhPrice, total, qzspRelyOn) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.dispNo = dispNo;
        this.sortNo = sortNo;
        this.project = project;
        this.unit = unit;
        this.amount = amount;
        this.type = type;
        this.price = price;
        this.zhPrice = zhPrice;
        this.total = total;
        this.qzspRelyOn = qzspRelyOn;
    }
}
exports.OtherProjectQzAndSuoPei = OtherProjectQzAndSuoPei;
//# sourceMappingURL=OtherProjectQzAndSuoPei.js.map