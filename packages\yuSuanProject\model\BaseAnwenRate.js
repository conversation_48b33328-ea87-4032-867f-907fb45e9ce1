"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAnwenRate2022 = exports.BaseAnwenRate = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 行政区域
 */
let BaseAnwenRate = class BaseAnwenRate extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "project_location" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "projectLocation", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "road_surface_num" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "roadSurfaceNum", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "floor_space" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "floorSpace", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "municipal_construction_cost" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "municipalConstructionCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "anwen_rate" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "anwenRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_code" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "del_flag" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "delFlag", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseAnwenRate.prototype, "remark", void 0);
BaseAnwenRate = __decorate([
    (0, typeorm_1.Entity)()
], BaseAnwenRate);
exports.BaseAnwenRate = BaseAnwenRate;
let BaseAnwenRate2022 = class BaseAnwenRate2022 extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, projectLocation, roadSurfaceNum, floorSpace, municipalConstructionCost, anwenRate, anwenRateYbjs, anwenRateJyjs, libraryCode, delFlag, remark) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.projectLocation = projectLocation;
        this.roadSurfaceNum = roadSurfaceNum;
        this.floorSpace = floorSpace;
        this.municipalConstructionCost = municipalConstructionCost;
        this.anwenRate = anwenRate;
        this.anwenRateYbjs = anwenRateYbjs;
        this.anwenRateJyjs = anwenRateJyjs;
        this.libraryCode = libraryCode;
        this.delFlag = delFlag;
        this.remark = remark;
    }
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "project_location" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "projectLocation", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "road_surface_num" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "roadSurfaceNum", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "floor_space" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "floorSpace", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "municipal_construction_cost" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "municipalConstructionCost", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "anwen_rate_ybjs" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "anwenRateYbjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "anwen_rate_jyjs" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "anwenRateJyjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_code" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "del_flag" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "delFlag", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseAnwenRate2022.prototype, "remark", void 0);
BaseAnwenRate2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_anwen_rate_2022" }),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String])
], BaseAnwenRate2022);
exports.BaseAnwenRate2022 = BaseAnwenRate2022;
//# sourceMappingURL=BaseAnwenRate.js.map