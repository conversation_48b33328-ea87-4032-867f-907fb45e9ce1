<!--
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-16 10:40:00
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-06-30 17:55:31
-->
<template>
  <div class="header">
    <div class="header-top">
      <div class="left-content">
        <div to="/" class="logo-link">
          <img :src="getUrl('newCsProject/logo.png')" alt="logo" class="logo" />
        </div>

        <a-tooltip>
          <template #title>保存(Ctrl+S)</template>
          <icon-font
            class="icon-font"
            type="icon-kuaisubaocun"
            @click="saveYsfFile(true)"
          ></icon-font>
        </a-tooltip>
        <div class="icon-font">
          <a-tooltip placement="bottom">
            <template #title>
              <span>申请远程协助</span>
            </template>
            <RemoteAssistance text="" icon="icon-shenqingyuanchengxiezhu"></RemoteAssistance>
            <!-- <icon-font type="icon-shenqingyuanchengxiezhu"></icon-font> -->
          </a-tooltip>
        </div>
        <!-- <div class="versionInfo">
          V{{ versionNow }}
        </div>-->
        <redoUndo></redoUndo>
      </div>

      <div class="title">
        <span>{{ projectPath || '建设工程计价管理软件' }}</span>
        <span v-if="!saveStatus"> *</span>
      </div>
      <!-- <a-menu v-model:selectedKeys="selectedKeys" mode="horizontal">
      <a-menu-item :key="item.key" v-for="item in menus">{{
        item.name
      }}</a-menu-item>
    </a-menu> -->
      <!-- <img src="@/assets/img/header-bg.png" class="header-bg" alt=""> -->
      <div class="right-menu">
        <user-info-show
          :loginType="userDetail?.loginType"
          :userInfo="userDetail?.userInfo"
          :infoList="userDetail?.changeInfoList"
          :infoVisible="true"
          @layoutOrChange="layoutOrChange"
          :type="'gzt'"
        ></user-info-show>
        <div class="operate">
          <div class="operate-icon" @click="toMin">
            <img :src="getUrl('newCsProject/operate-minimize.png')" />
          </div>
          <div class="operate-icon" v-show="!isMax" @click="toMaX">
            <img :src="getUrl('newCsProject/operate-maximize.png')" />
          </div>
          <div class="operate-icon" v-show="isMax" @click="toMaX">
            <img :src="getUrl('newCsProject/operate-reduction.png')" />
          </div>
          <div class="operate-icon" @click="toClose">
            <img :src="getUrl('newCsProject/operate-close.png')" />
          </div>
        </div>
      </div>
    </div>
    <div class="header-menu">
      <div
        ref="headerMenu"
        class="header-menu-item"
        :class="{ selected: keyValue === item.key }"
        v-for="item in menusList"
        :key="item.key"
      >
        <a-dropdown v-if="item.key === 'file'" :trigger="['click']">
          <div class="menu-label" @click="openChange(item)">
            {{ item.name }}
            <img v-if="item.dropdown" :src="getUrl('newCsProject/select.png')" alt="" />
          </div>
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item
                v-for="child in store.type === 'yssh'
                  ? item.children.filter(x => x.key === 'asideSave' || x.key === 'open')
                  : item.children.filter(x => x.key !== 'helpCenter')"
                :key="child.key"
                style="width: 140px"
                :disabled="['save'].includes(child.key) ? saveStatus : false"
              >
                <icon-font
                  :type="child.icon"
                  style="margin: 0 10px 0 0"
                  v-if="!(['asideSave'].includes(child.key) && store.type === 'jieSuan')"
                />
                <a
                  href="javascript:;"
                  v-if="!(['asideSave'].includes(child.key) && store.type === 'jieSuan')"
                  >{{ child.name }}</a
                >
              </a-menu-item>
              <a-sub-menu title="帮助中心">
                <template #icon>
                  <icon-font type="icon-bangzhuzhongxin1" style="margin: 0 10px 0 0" />
                </template>
                <a-menu-item @click="childClick(1)">政策文件</a-menu-item>
                <a-menu-item @click="childClick(2)">勘误说明</a-menu-item>
              </a-sub-menu>
            </a-menu>
          </template>
        </a-dropdown>
        <div class="menu-label" @click="clickMenu(item)" v-else>
          {{ item.name }}
        </div>
      </div>
      <div class="sub-title">
        <div class="list">
          <img src="@/assets/img/sub-title-icon.png" alt="icon" class="icon" /><span
            >公测快速反馈：</span
          ><span class="link" @click="openExternal('https://www.yunsuanfang.com/feedback')"
            >https://www.yunsuanfang.com/feedback</span
          >
        </div>
        <div class="list">
          <img
            src="@/assets/img/sub-title-icon2.png"
            alt="icon"
            class="icon"
            style="width: 10px; height: 15px"
          /><span>手机快速反馈：鼠标移入扫一扫</span>
          <a-popover placement="bottom">
            <template #content>
              <img :src="qrCodeUrl" alt="qr-code" class="qr-code-img" />
            </template>
            <img src="@/assets/img/sub-qr-code.png" alt="qr-code" class="qr-icon" />
          </a-popover>
        </div>
      </div>
    </div>
    <ConstructReadOnlyDialog ref="constructReadOnlyRef"></ConstructReadOnlyDialog>
  </div>
  <setUpPopup
    ref="setUpPopupRef"
    @closePopup="setUpVisible = false"
    v-if="setUpVisible"
  ></setUpPopup>

  <new-project-model
    v-model:visible="newProVisible"
    :showType="'新建预算项目'"
  ></new-project-model>
  <backupCenter  v-model:visible="backupVisible"/>
  <common-modal
    className="titleNoColor noHeaderHasclose  updataBg"
    title=" "
    width="700"
    :position="{ top: 'calc(50% - 200px)', left: 'calc(50% - 325px)' }"
    v-model:modelValue="updateValue"
    :mask="false"
    @close="afterUpdate"
  >
    <template #corner>
      <span class="versionClass">V{{ version }}</span>
    </template>
    <div class="updataInfo">
      <div>
        <p class="title">版本日志：</p>
        <p v-for="item in upDateInfoList">
          {{ item }}
        </p>
        <p><span>更新时间：</span>{{ releaseDate }}</p>
        <p class="info">
          （公测期间版本更新后，可能导致历史文件无法兼容，您可于云算房官网<span
            class="link"
            @click="shell.openExternal('https://www.yunsuanfang.com/download')"
            >https://www.yunsuanfang.com</span
          >下载历史软件版本）
        </p>
        <p class="info">点击【立即更新】后将关闭软件所有页面，请注意数据保存</p>
      </div>
    </div>
    <p class="update-btnList">
      <a-button @click="afterUpdate">暂不更新</a-button>
      <a-button type="primary" @click="onSubmit">立即更新</a-button>
    </p>
  </common-modal>
  <help-center-popup
    v-model:helpCenterVisible="helpCenterVisible"
    @closePopup="helpCenterVisible = false"
    :fileType="fileType"
  ></help-center-popup>
  <info-modal
    v-model:infoVisible="checkFileChange"
    :infoText="fileInfo"
    :isSureModal="true"
  ></info-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
  watch,
  computed,
  defineAsyncComponent,
  nextTick,
} from 'vue';
import { getUrl } from '@/utils/index';
import { ipcApiRoute, specialIpcRoute } from '@/api/main';
import feePro from '@/api/feePro';
import { checkisOnline } from '@/utils/publicInterface';
import csProject from '@/api/csProject';
import detailApi from '@/api/projectDetail';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import { useRouter, useRoute } from 'vue-router';
import infoMode from '@/plugins/infoMode.js';
import { useCheckProjectBefore } from '@/hooks/useCheckProjectBefore';
import userInfoShow from '@/views/csProject/header/userInfoShow.vue';
import QRCode from 'qrcode';
import { globalData } from '@/views/projectDetail/reportForm/reportFrom';
import RemoteAssistance from '@/components/RemoteAssistance/index.vue';
import { getUpdateJson } from '@/api/auth';
import redoApi from '@/api/redo';
import modalData from '@/modal/modalData';
import ConstructReadOnlyDialog from '@/components/ConstructReadOnlyDialog/index.vue';

let qrCodeUrl = ref('');
const getQRCode = async () => {
  const text = 'https://h5.yunsuanfang.com/userFeedback'; // 这里是你想生成二维码的链接
  qrCodeUrl.value = await QRCode.toDataURL(text);
  console.log('dataUrl', qrCodeUrl.value);
};
const layoutOrChange = async () => {
  // 关闭工作台的话需要所有当前打开的工作台都没有要保存的内容才可以关闭-否则弹框提示
  let result = await csProject.changeIdentity();
  let list = result.result?.filter(a => a !== 1) || []; //当前打开工作台窗口个数
  console.log('layoutOrChange1111111111---child', list);
  if (list?.length > 1) {
    //只有当前窗口打开且被编辑未保存  或打开多个窗口
    infoMode.show({
      isSureModal: true,
      iconType: 'icon-querenshanchu',
      infoText: '当前存在已打开的多个工作台项目，请关闭后退出',
      confirm: () => {
        // await closeChildWin();
        infoMode.hide();
      },
      // close: () => {
      //   //去进行保存
      //   infoMode.hide();
      // },
    });
  } else {
    saveYsfFile(); //保存当前窗口数据
    closeChildWin(); //关闭当前创库并退出
  }
  console.log('工作台关闭');
};
const closeChildWin = async () => {
  //关闭所有工作台并退出
  feePro.closeAllChildWindow().then(res => {});
  localStorage.removeItem('loginUserInfo'); //清除用户信息
  localStorage.removeItem('constructSequenceNbr'); //清除工作台存储信息
  // store.SET_IS_LOGIN_USER_INFO(null);
  //向主窗口发消息清空登录信息重新登录
  let mainId = await $ipc.invoke(ipcApiRoute.getWCid, 'main');
  $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, 'SingOut');
};
const { shell } = require('electron');
// 假设你有一个链接地址
// 使用 shell 模块的 openExternal 方法打开链接

const openExternal = link => {
  shell.openExternal(link);
};
const setUpPopup = defineAsyncComponent(() => import('@/components/Header/setUpPopup.vue'));

const { showInfo, isProjectComplete } = useCheckProjectBefore();

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let setUpVisible = ref(false);
const globalProperties = getCurrentInstance().appContext.config.globalProperties; // 获取全局挂载
const $ipc = globalProperties.$ipc;
const route = useRoute();
const isMax = ref(false);
const { ipcRenderer, webFrame } = require('electron');
const logoUrl = getUrl('logo.png');
const modalWidth = ref('1300px');
import newProjectModel from '@/components/SelfModel/newProjectModel.vue';
import shApi from '@/api/shApi.js';
import redo from '../../hooks/redo';
const router = useRouter();
const newProVisible = ref(false); //新建预算项目弹框
const store = projectDetailStore();
let userDetail = ref(null);
let status = ref(0); // -1:异常，1：有可用更新，2：没有可用更新，3：下载中, 4：下载完成
let version = ref('2.0.0'); //版本
let releaseDate = ref('2023-09-13 10:00:00'); //更新时间
let updateValue = ref(false); //立即更新弹框
let keyValue = ref('customize'); //切换tab栏
let saveStatus = ref(false);
let menus = ref([
  {
    name: '文件',
    key: 'file',
    dropdown: true,
    children: [
      {
        name: '打开',
        key: 'open',
        icon: 'icon-dakai',
      },
      {
        name: '新建',
        key: 'new',
        icon: 'icon-xinjian',
      },
      {
        name: '另存为',
        icon: 'icon-lingcunwei',
        key: 'asideSave',
      },
      {
        name: '保存',
        icon: 'icon-baocun',
        key: 'save',
      },
      {
        name: '备份中心',
        icon: 'icon-beifenzhongxin',
        key: 'backup',
      },
      {
        name: '设置',
        icon: 'icon-shezhi',
        key: 'setup',
      },
      {
        name: '帮助中心',
        icon: 'icon-bangzhuzhongxin1',
        key: 'helpCenter',
      },
      {
        name: '检查更新',
        icon: 'icon-jianchagengxin',
        key: 'checkUpdate',
      },
    ],
  },
  {
    name: '编制',
    key: 'customize',
  },
  {
    name: '报表',
    key: 'reportForm',
  },
]);
const menusList = computed(() => {
  const index = menus.value.findIndex(a => a.key === 'electronicLabel');
  if (store.projectType === 'DW') {
    if (index > -1) menus.value.splice(index, 1);
  } else {
    if (index === -1) {
      menus.value.push({
        name: '电子标',
        key: 'electronicLabel',
      });
    }
  }
  return menus.value;
});
let timer = ref(); //获取保存信息循环
let saveTimer = ref(); // 获取自动保存信息循环
let projectPath = ref(); // 工作台项目路径地址
let helpCenterVisible = ref(false); // 帮助中心弹框是否显示
let fileType = ref(1); // 帮助中心 1 政策文件 2 勘误说明

onMounted(async () => {
  isMaxFun();
  getQRCode();
  checkFile();
  // newmodal();
  getConstructFileMsg();
  window.addEventListener('resize', isMaxFun);
  // getAppVersion(); //获取左上角展示的系统版本号
  isNeedSave();

  if (localStorage.getItem('loginUserInfo')) {
    store.SET_IS_LOGIN_USER_INFO(JSON.parse(localStorage.getItem('loginUserInfo')));
  } else {
    sendTosubWindow('getLoginInfo');
  }
  listenerMain();

  if (store.type == 'yssh') {
    menus.value.push({
      name: '分析与报告',
      key: 'analysisAndReporting',
    });
  }
  startTimers();
  await store.SET_CONSTRUCT_READ_ONLY_STATUS(route.query.constructSequenceNbr);
  constructReadOnlyRef.value?.readOnlyTip();
});
const startTimers = () => {
  // timer.value = setInterval(() => {
  //   isNeedSave();
  // }, 3000);
  saveTimer.value = setInterval(
    () => {
      csProject.saveYsfFile(route.query.constructSequenceNbr).then(res => {});
    },
    1000 * 60 * 5
  );
};
watch(
  () => store.startMatch,
  (newVal, oldVal) => {
    if (newVal) {
      clearTimers();
    } else {
      startTimers();
    }
  }
);
watch(
  () => store.globalSettingInfo,
  (newVal, oldVal) => {
    let saveTime = +store.globalSettingInfo.wjdeccsjsz;
    clearInterval(saveTimer.value);
    if (!saveTime || saveTime < 0) {
      saveTimer.value = null;
      console.log('saveTimer.value-1', store.globalSettingInfo?.wjdeccsjsz);
      return;
    }
    saveTimer.value = setInterval(
      () => {
        console.log('saveTimer.value', saveTime);
        csProject.saveYsfFile(route.query.constructSequenceNbr).then(res => {});
      },
      1000 * 60 * saveTime
    );
  }
);
//let versionNow = ref('1.0.0'); //当前系统版本号
//const getAppVersion = () => {
// csProject.getAppVersion().then(res => {
//  console.log('getAppVersion', res);
//  versionNow.value = res;
// });
//};
onBeforeUnmount(() => {
  window.removeEventListener('resize', isMaxFun);
});
const newmodal = () => {
  redoApi.createModal({
    windowId: route.query.constructSequenceNbr,
    modal: JSON.parse(JSON.stringify(modalData.ys)),
  });
};
const toMin = () => {
  ipcRenderer.send('window-min-child', {
    id: route.query.constructSequenceNbr,
  });
};
const toMaX = () => {
  ipcRenderer.send('window-max-child', {
    id: route.query.constructSequenceNbr,
  });
  isMaxFun();
};
const isMaxFun = () => {
  setTimeout(() => {
    let { innerWidth, innerHeight } = window;
    let { availWidth, availHeight } = screen;
    isMax.value = innerWidth === availWidth && innerHeight === availHeight;
    modalWidth.value = innerWidth < 1366 ? '453px' : '1300px';
  }, 100);
};
const toClose = async () => {
  if (globalData.openEdit) {
    bus.emit('reportFormEdit', {
      data: true,
    });

    return;
  }
  await isNeedSave(); //关闭时候检测是否需要保存
  await csProject.removeCheck({ constructId: route.query.constructSequenceNbr }).then(res => {
    console.log('removeCheck', res);
  });
  if (!saveStatus.value) {
    infoMode.show({
      iconType: 'icon-qiangtixing',
      infoText: '当前文件未保存，是否保存？',
      confirm: () => {
        saveYsfFile();
        delSaveInfo();
        ipcRenderer.send('window-close-child', {
          id: route.query.constructSequenceNbr,
        });
        infoMode.hide();
      },
      close: () => {
        delSaveInfo();
        infoMode.hide();
        ipcRenderer.send('window-close-child', {
          id: route.query.constructSequenceNbr,
        });
      },
    });
  } else {
    //前提清除工作台部分信息
    delSaveInfo();
    ipcRenderer.send('window-close-child', {
      id: route.query.constructSequenceNbr,
    });
  }
};
const delSaveInfo = () => {
  let list = JSON.parse(localStorage.getItem('constructSequenceNbr')) || {};
  let key = getObjectKey(list, route.query.constructSequenceNbr);
  if (key) {
    delete list[key];
    localStorage.setItem('constructSequenceNbr', JSON.stringify(list));
  }
};
let constructReadOnlyRef = ref();

const saveYsfFile = (isLoading = false) => {
  //isLoading-true需要展示页面loading样式
  console.log('为啥不进来');
  if (constructReadOnlyRef.value?.readOnlyTip()) return;
  if (isLoading)
    store.SET_GLOBAL_LOADING({
      loading: true,
      info: '正在保存中...',
    });
  csProject
    .saveYsfFile(route.query.constructSequenceNbr)
    .then(res => {
      console.log(res);
      if (res?.result) {
        message.success('保存成功');
      }
    })
    .finally(() => {
      if (isLoading) store.SET_GLOBAL_LOADING({ loading: false });
    });
};
const openChange = item => {
  isNeedSave();
  clickMenu(item);
};
const isNeedSave = async () => {
  // console.log(
  //   'route.query.constructSequenceNbr',
  //   route.query.constructSequenceNbr
  // );
  await csProject.diffProject({ constructId: route.query.constructSequenceNbr }).then(res => {
    // console.log('diffProject', res);
    saveStatus.value = !res.result;
  });
};

let openLocalProStatus = ref(false);
let checkFileChange = ref(false);
let fileInfo = ref(null);
const checkFile = () => {
  $ipc.on('judgeSuffix', (event, arg) => {
    console.log('获取监听消息', event, arg);
    if (route.query.constructSequenceNbr) {
      //主窗口监听
      fileInfo.value = arg;
      checkFileChange.value = true;
    }
  });
};
const handleMenuClick = ({ key }) => {
  console.log('handleMenuClick', key);
  switch (key) {
    case 'open':
      // 打开
      if (openLocalProStatus.value) {
        message.info('已打开系统弹窗');
        return;
      }
      openLocalProStatus.value = true;
      let apiName;
      if (store.type === 'yssh') {
        apiName = shApi.shFileOpenProject();
      } else {
        apiName = feePro.openLocalFile();
      }
      apiName
        .then(res => {
          console.log('打开本地项目', res);
        })
        .finally(res => {
          console.log('🚀 ~ feePro.openLocalFile ~ res:', res);
          openLocalProStatus.value = false;
        });
      break;
    case 'new':
      // 新建-打开控制台新建预算项目弹框
      newProVisible.value = true;
      break;
    case 'asideSave':
      // 另存为
      // 保存
      fileSaveAs();
      break;
    case 'save':
      // 保存
      saveYsfFile(true);
      break;
    case 'backup':
      // 备份中心
      backup();
      break;
    case 'setup':
      setUpVisible.value = true;
      //设置
      break;
    case 'checkUpdate':
      //检查更新
      // updateValue.value = true; //测试
      checkForUpdater();
      break;
    case 'helpCenter':
      helpCenterVisible.value = true;
      break;
  }
};

/**
 * 打开备份中心
 * @function backup
 * @returns {void}
 */
const backup = () => {
  backupVisible.value = true;
};
// 帮助中心子级点击事件
const childClick = type => {
  fileType.value = type;
  helpCenterVisible.value = true;
};

const getObjectKey = (object, value) => {
  return Object.keys(object).find(key => object[key] == value);
};
const fileSaveAs = () => {
  let apiName = csProject.fileSaveAs;
  if (store.type === 'yssh') {
    apiName = shApi.shFileSaveAs;
  }
  apiName(route.query.constructSequenceNbr).then(res => {
    if (res.status == 200) {
      if (res.result === 0) {
        console.log('点击了取消');
        return;
      } else if (res.result === 2) {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-qiangtixing',
          infoText: '文件已打开，不能覆盖!',
          confirm: () => {
            infoMode.hide();
          },
        });
      } else {
        //另存为成功需要刷新总控台项目列表
        let old = route.query.constructSequenceNbr;
        console.log('fileSaveAs', route, router, route.query.constructSequenceNbr);
        let list = JSON.parse(localStorage.getItem('constructSequenceNbr')) || {};
        let key = getObjectKey(list, old);
        if (key) {
          list[key] = res.result;
        } else {
          list[old] = res.result;
        }
        localStorage.setItem('constructSequenceNbr', JSON.stringify(list));
        location.reload();
        console.log(old, route.query.constructSequenceNbr, window);
      }
    }
  });
};
const getData = time => {
  let date = new Date(new Date(time).getTime() + 8 * 3600 * 1000);
  date = date.toJSON();
  date = date && date.substring(0, 19).replace('T', ' ');
  return date;
};
//检查更新
const checkForUpdater = async () => {
  const isOnline = await checkisOnline(true);
  if (isOnline) {
    sendTosubWindow('checkUpdate');
    listenerMain(true);
  }
};
//更新下载
const onSubmit = () => {
  // showSchedule.value = true;
  feePro.downloadApp().then(res => {
    console.log('点击下载', res);
    updateValue.value = false;
    sendTosubWindow('downloadApp');
    //关闭所有工作台页面---打开首页的进度条进行下载
  });
};
const sendTosubWindow = async type => {
  // 向主窗口发送下载请求-并关闭子窗口
  let mainId = await $ipc.invoke(ipcApiRoute.getWCid, 'main');
  if (type === 'checkUpdate') {
    //点击检查更新向父窗口发送检查更新消息
    $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, 'checkUpdate');
  } else if (type === 'downloadApp') {
    //点击立即更新向主窗口发送消息关闭所有子窗口，主窗口展示进度条
    $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, 'downloadApp');
    feePro.closeAllChildWindow().then(res => {});
  } else if (type === 'getLoginInfo') {
    //向主窗口获取登录信息
    $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, 'getLoginInfo');
  }
};
const isJson = str => {
  //判断字符串是否可以使用JSON.parse规范转化
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};
const afterUpdate = () => {
  //暂不更新
  updateValue.value = false;
  $ipc.removeAllListeners(specialIpcRoute.appUpdater);
};
let upDateInfoList = ref([]);
const getUpdateInfo = async () => {
  let res = await getUpdateJson();
  let target = version.value.split('.').join('');
  upDateInfoList.value = res[target];
  updateValue.value = true;
};
const listenerMain = (bol = false) => {
  //监听主窗口消息
  $ipc.on(specialIpcRoute.window1ToWindow2, (event, arg) => {
    // bol && message.info(`接受主窗口发来的消息-------------${arg}`); //测试
    if (!isJson(arg)) return;
    let result = arg ? JSON.parse(arg) : null;
    console.log('检查更新', result);
    if (bol && result.status !== 1) {
      message.info('您当前使用的是最新版本');
      $ipc.removeAllListeners(specialIpcRoute.window1ToWindow2);
    } else if (bol && result.status === 1) {
      status.value = result.status;
      releaseDate.value = getData(result.releaseDate);
      version.value = result.version;
      getUpdateInfo();
      $ipc.removeAllListeners(specialIpcRoute.window1ToWindow2);
    }
  });
};
watch(
  () => store.loginUserInfo,
  () => {
    //报表-编制来回切换会导致登录数据丢失
    if (store.loginUserInfo) getUserInfo();
  }
);
watch(
  () => store.isFileSaveAs,
  async () => {
    if (store.isFileSaveAs) {
      let mainId = await $ipc.invoke(ipcApiRoute.getWCid, 'main');
      //另存为成功刷新最近列表
      $ipc.sendTo(mainId, specialIpcRoute.window2ToWindow1, 'refreshAsideList');
      getConstructFileMsg();
      store.isFileSaveAs = false;
    }
  }
);
const getUserInfo = () => {
  // if (store.loginUserInfo) {
  localStorage.setItem('loginUserInfo', JSON.stringify(store.loginUserInfo));
  if (store.loginUserInfo) {
    localStorage.setItem('autoLoginInfo', JSON.stringify(store.loginUserInfo));
  }
  userDetail.value = { ...store.loginUserInfo };
  // }
};
const clickMenu = ({ key }) => {
  console.log(key, keyValue);
  if (keyValue.value === key) return; //key值未切换不走下面流程
  if (
    (keyValue.value === 'file' && key === 'customize') ||
    (keyValue.value === 'customize' && key === 'file')
  ) {
    keyValue.value = key; //文件-》编制  ，编制-》文件不需要切换路由
    return;
  }
  if (key === 'reportForm' && !showInfo()) return;
  keyValue.value = key;
  let path =
    key === 'customize' || key === 'reportForm' || key === 'electronicLabel'
      ? `/projectDetail/${key}`
      : key === 'file'
        ? '/projectDetail/customize'
        : '/projectDetail';

  if (key == 'analysisAndReporting') {
    path = '/projectDetail/analysisAndReporting';
  } else if (key === 'reportForm' && store.type == 'yssh') {
    path = '/projectDetail/reportFormSh';
  } else if (key === 'reportForm' && store.type == 'jieSuan') {
    path = '/projectDetail/reportFormJieSuan';
  }

  let query = {
    constructSequenceNbr: route.query.constructSequenceNbr,
    type: route.query?.type || 'ys',
  };
  if (key === 'analysisAndReporting') {
    query.constructId = store.currentTreeGroupInfo?.constructId;
    query.ssConstructId = store.currentTreeGroupInfo?.ssConstructId;
  }
  store.$state.tabSelectName = ''; // 切换重置一下
  router.push({
    path,
    query,
  });
  // 跳转清除存的树信息，不然有些地方缺少判断，直接报错
  // store.currentTreeInfo = null;
  // store.currentTreeGroupInfo = null;
};

const triggerMenu = () => {
  keyValue.value = 'customize';
  let path = '/projectDetail/customize';
  let query = {
    constructSequenceNbr: route.query.constructSequenceNbr,
    type: route.query?.type || 'ys',
  };
  router.push({
    path,
    query,
  });
};

const triggerReportMenu = () => {
  keyValue.value = 'reportForm';
  let path = '/projectDetail/reportForm';
  let query = {
    constructSequenceNbr: route.query.constructSequenceNbr,
    type: route.query?.type || 'ys',
  };
  router.push({
    path,
    query,
  });
};

const getConstructFileMsg = () => {
  detailApi.getConstructFileMsg({ constructId: route.query.constructSequenceNbr }).then(res => {
    if (res.status === 200 && res.result) {
      projectPath.value = res.result?.path;
    }
  });
};
onBeforeUnmount(() => {
  clearTimers();
});
const clearTimers = () => {
  clearInterval(timer.value);
  timer.value = null;
  clearInterval(saveTimer.value);
  saveTimer.value = null;
};
defineExpose({
  triggerMenu,
  triggerReportMenu,
});
</script>
<style lang="scss">
.updataBg {
  .vxe-modal--content {
    padding: 20px 0px 0px 0px !important;
  }
  .vxe-modal--header {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 120px !important;
    background: url(@/assets/img/updateBg.png) 100% 0;
    background-size: 100%;
    .vxe-modal--close-btn,
    .vxe-modal--zoom-btn {
      position: absolute;
      top: 20%;
      transform: translateY(-50%);
      color: #fff !important;
      font-size: 15px !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.header {
  width: 100vw;
  position: relative;
  height: 100%;
  background: #2867c7;
  user-select: none;
  &-top {
    height: 32px;
    display: flex;
    position: relative;
    z-index: 99;
    .left-content {
      display: flex;
      align-items: center;
      .versionInfo {
        color: #ffffff;
        margin-left: 5px;
      }
    }
    .logo-link {
      height: 100%;
      -webkit-app-region: drag; // 设置可拖动
      img {
        width: 139px;
        height: 19px;
        // transform: scale(0.90);
        vertical-align: top;
        margin-top: 8px;
        margin-left: 18px;
      }
    }
    .icon-font {
      margin-left: 10px;
      &:hover {
        cursor: pointer;
      }
    }
  }
  &-menu {
    height: 24px;
    width: 100%;
    line-height: 24px;
    font-size: 12px;
    color: white;
    display: flex;
    padding: 0 10px;
    &-item:hover {
      background: rgba(30, 76, 149, 0.39);
    }
    .selected {
      background: #1e4c95;
      border-radius: 4px 4px 0px 0px;
    }
    &-item {
      height: 24px;
      padding: 0 16px;
      cursor: pointer;
      .menu-label {
        height: auto;
        img {
          width: 13px;
          position: relative;
          top: -2px;
        }
      }
    }
  }

  .title {
    -webkit-app-region: drag; // 设置可拖动
    width: calc(100vw - 460px);
    text-align: center;
    line-height: 32px;
    color: white;
    font-size: 12px;
    text-indent: 10px;
    white-space: nowrap; /* 强制不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
    border-collapse: collapse;
    img {
      position: relative;
      top: 0px;
    }
  }
}
.sub-title {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  border-collapse: collapse;
  width: 565px;
  .list {
    padding: 0 10px;
    font-size: 0;
    color: #a0ffff;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
  }
  span {
    font-size: 12px;
    color: #a0ffff;
    line-height: 17px;
    text-indent: 0;
    white-space: nowrap;
  }
  .link {
    cursor: pointer;
    text-decoration-line: underline;
  }
  .icon {
    display: block;
    width: 15px;
    height: 17px;
    margin-right: 3px;
  }
  .qr-icon {
    display: block;
    width: 15px;
    height: 15px;
    margin-left: 3px;
  }
}
.qr-code-img {
  display: block;
  width: 77px;
  height: 77px;
  margin: -12px -16px;
}
.versionClass {
  color: #ffffff;
  position: absolute;
  left: 250px;
  font-size: 14px;
  top: 75px;
  font-weight: 200;
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
}
.updataInfo {
  div {
    max-width: 100%;
    margin: 0px 5px 25px 20px;
    .title {
      font-weight: 800;
      font-size: 13px;
    }
    .info {
      color: #de3f3f;
    }
    .link {
      cursor: pointer;
      color: #2867c7;
      text-decoration: underline;
    }
    p {
      font-size: 12px;
      line-height: 14px;
      color: #2a2a2a;
    }
  }
}
.update-btnList {
  display: flex;
  width: 200px;
  margin: 0 auto 20px;
  justify-content: space-between;
}
.left-content {
  position: relative;
  z-index: 2;
  // width: 225px;
}
.right-menu {
  padding-right: 12px;
  text-align: right;
  display: flex;
}
.user-info {
  cursor: pointer;
  line-height: 32px;
  .avatar {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #dfdfdf;
    vertical-align: super;
  }
  span {
    display: inline-block;
    width: 62px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-right: 3px;
    color: #fff;
  }
}
.operate {
  width: 120px;
  display: flex;
  height: 18px;
  align-items: center;
  margin-left: 20px;
  flex-direction: row;
  justify-content: space-between;
  /* padding-top: 5px; */
  line-height: 32px;
  opacity: 1;
  transition: opacity linear 0.2s;
  &-icon {
    width: 20px;
    height: 20px;
  }
  &-icon:hover {
    cursor: pointer;
    transition: opacity linear 0.2s;
    opacity: 0.6;
  }
}
@media (min-width: 1100px) {
  //页面缩小公测链接右移-软件最小980px-当拉升到1100px公测链接设置定位
  .sub-title {
    position: absolute;
    top: 35px;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
