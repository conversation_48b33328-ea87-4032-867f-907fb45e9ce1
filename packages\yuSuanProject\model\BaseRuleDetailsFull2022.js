"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRuleDetailFull2022 = void 0;
const typeorm_1 = require("typeorm");
const BaseRuleDetailFull_1 = require("./BaseRuleDetailFull");
let BaseRuleDetailFull2022 = class BaseRuleDetailFull2022 extends BaseRuleDetailFull_1.BaseRuleDetailFull {
};
BaseRuleDetailFull2022 = __decorate([
    (0, typeorm_1.Entity)({
        name: "base_rule_details_full_2022",
    })
], BaseRuleDetailFull2022);
exports.BaseRuleDetailFull2022 = BaseRuleDetailFull2022;
//# sourceMappingURL=BaseRuleDetailsFull2022.js.map