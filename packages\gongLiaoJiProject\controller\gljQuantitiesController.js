const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require('../constants/FunctionTypeConstants');
const { ObjectUtils } = require('../utils/ObjectUtils');
const DeTypeConstants = require("../constants/DeTypeConstants");

class GljQuantitiesController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    async getList(args) {
        let {constructId, unitId, deId} = args;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let deMap = unitQuantiesMap?.get(deId);
        if (ObjectUtils.isEmpty(deMap) || ObjectUtils.isEmpty(deMap.quantities)) {
            let deLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, deId);
            if (ObjectUtils.isNotEmpty(deLine)
                && deLine.type !== DeTypeConstants.DE_TYPE_DEFAULT
                && deLine.type !== DeTypeConstants.DE_TYPE_EMPTY
                && deLine.type !== DeTypeConstants.DE_TYPE_FB
                && deLine.type !== DeTypeConstants.DE_TYPE_ZFB
            ) {
                await this.service.gongLiaoJiProject.gljInitDeService.initDeQuantities(deLine);
            }
            deMap = unitQuantiesMap?.get(deId);
        }
        if (ObjectUtils.isNotEmpty(deMap)){
            let result = deMap.quantities.filter(item=> item.variables !== 'GCGM')
            return ResponseData.success(result);
        }
        return ResponseData.success([]);
    }

    /**
     * 工程量明细 新增
     * @param param
     * @returns {ResponseData}
     */
    async insert(param, redo="插入- -工程量明细") {
        let {constructId, singleId, unitId, type, lineId, selectId}  = param;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineId);
        let newLine = await this.service.gongLiaoJiProject.gljQuantitiesService.addAfter(pointLine, selectId);
        newLine.quotaListId = pointLine.quotaListId;
        return ResponseData.success(newLine);
    }

    /**
     * 工程量明细 新增
     * @param param
     * @returns {ResponseData}
     */
    async copy(param, redo="复制插入- -工程量明细") {
        let {constructId, singleId, unitId, type, lineId, selectId}  = param;
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineId);
        let quantities = await this.service.gongLiaoJiProject.gljQuantitiesService.getQuantitiesList(pointLine);
        let newLine = await this.service.gongLiaoJiProject.gljQuantitiesService.addAfter(pointLine, selectId);
        newLine.quotaListId = pointLine.quotaListId;
        newLine.mathFormula = quantities.filter(item => item.sequenceNbr === selectId)[0].mathFormula;
        newLine.accumulateFlag = 1
        let args = {constructId, singleId, unitId, type, quotaListId: lineId, pointLine: newLine};
        await this.updateQuantityData(args);
        return ResponseData.success(newLine);
    }

    /**
     * 工程量明细 追加
     * @param param
     * @returns {ResponseData}
     */
    async append(param, redo="追加- -工程量明细") {
        let {constructId, singleId, unitId, type, lineId, pointLine}  = param;
        let deLine = await this.service.gongLiaoJiProject.gljProjectCommonService.findDeByDeId2(constructId, unitId, lineId);
        let originalQuantity = deLine?.originalQuantity;

        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let quotaLine = unitQuantiesMap.get(lineId);
        let quantities = await this.service.gongLiaoJiProject.gljQuantitiesService.getQuantitiesList(quotaLine);
        let args0 = {constructId, singleId, unitId, type, quotaListId: lineId, pointLine: pointLine};
        await this.updateQuantityData(args0);

        let newLine = await this.service.gongLiaoJiProject.gljQuantitiesService.add(quotaLine, quantities[0].sequenceNbr);
        newLine.quotaListId = quotaLine.quotaListId;
        newLine.mathFormula = String(originalQuantity);
        newLine.accumulateFlag = 1
        let args = {constructId, singleId, unitId, type, quotaListId: lineId, pointLine: newLine};
        await this.updateQuantityData(args);
        return ResponseData.success(newLine);
    }

    /**
     * 移动
     * @param param
     * @returns {ResponseData}
     */
    async move(param, redo="移动- -工程量明细"){
        let {constructId, singleId, unitId, type, lineInfo, direction}  = param;
        // let pointLine = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === lineInfo.quotaListId);
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineInfo.quotaListId);
        let qtId = lineInfo.sequenceNbr;
        if (direction === 1) { // 0上 1下
            await this.service.gongLiaoJiProject.gljQuantitiesService.moveDown(pointLine, qtId);
        } else {
            await this.service.gongLiaoJiProject.gljQuantitiesService.moveUp(pointLine, qtId);
        }
        return ResponseData.success(true);
    }


    /**
     * 工程量明细
     * @param param
     * @returns {Promise<ResponseData>}
     */
    async delete(param, redo="删除- -工程量明细") {
        let {constructId, singleId, unitId, type, lineId, deleteId} = param;
        // let pointLine = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === lineId);
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineId);
        let newLine = await this.service.gongLiaoJiProject.gljQuantitiesService.delete(pointLine, deleteId);
        return ResponseData.success(true);
    }

    /**
     * 工程量明细处修改数据
     * @param param
     * @returns {Promise<ResponseData>}
     */
    async updateQuantityData(param, redo="编辑- -工程量明细") {
        let {constructId, singleId, unitId, type, quotaListId, pointLine} = param;
        let {sequenceNbr, mathFormula, mathIllustrate, mathResult, variables, accumulateFlag} = pointLine;
        // let quotaLine = ProjectDomain.getDomain(constructId).getDeDomain().getDe(item => item.sequenceNbr === quotaListId);
        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let quotaLine = unitQuantiesMap.get(quotaListId);
        if (ObjectUtils.isNotEmpty(variables) && variables.toUpperCase() === "GCGM") {
            return ResponseData.fail("编辑内容不合法，请重新输入");
        }
        if (ObjectUtils.isEmpty(pointLine.mathFormula)) {
            let line = quotaLine.quantities.find(item => item.sequenceNbr === sequenceNbr);
            line.accumulateFlag = pointLine.accumulateFlag;
            line.mathIllustrate = pointLine.mathIllustrate;
            line.variables = pointLine.variables?.toUpperCase();
            return ResponseData.success(true);
        }else {
            if (!ObjectUtils.combinedValidation(pointLine.mathFormula)) {
                return ResponseData.fail("编辑内容不合法，请重新输入");
            }
        }

        await this.service.gongLiaoJiProject.gljQuantitiesService.upDate(quotaLine, sequenceNbr, mathFormula, mathIllustrate, mathResult, variables, accumulateFlag, type, constructId, singleId, unitId);
        return ResponseData.success(true);
    }

    async paste(param, redo="粘贴- -工程量明细") {
        let {constructId, singleId, unitId, type, lineInfo, pasteId} = param;

        let quantitiesMap = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_QUANTITIES);
        let unitQuantiesMap = quantitiesMap.get(unitId)
        let pointLine = unitQuantiesMap.get(lineInfo.quotaListId);
        let qtId = lineInfo.sequenceNbr;
        await this.service.gongLiaoJiProject.gljQuantitiesService.paste(pointLine, qtId, pasteId);
        return ResponseData.success(true);
    }

}

GljQuantitiesController.toString = () => '[class GljQuantitiesController]';
module.exports = GljQuantitiesController;
