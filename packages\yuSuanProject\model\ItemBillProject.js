"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemBillProject = void 0;
const BaseModel_1 = require("./BaseModel");
class ItemBillProject extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, parentId, kind, dispNo, displayStatu, displaySign, optionMenu, zjfPrice, zjfPriceFormula, zjfTotal, price, total, rfee, totalRfee, cfee, totalCfee, jfee, totalJfee, managerFee, totalManagerFee, profitFee, totalProfitFee, zcfee, totalZcfee, bdCode, bdName, projectAttr, costMajorName, costFileCode, measureType, fbId, unitId, spId, quantity, quantityExpression, quantityExpressionNbr, unit, unpricedCfee, rfeePrice, zhygfl, releaseDate, ssProvince, ssCity, sortNo, remark, creator, updater, deptId, status, createDate, updateDate, delFlag, tenantId, bzjd, taxMode, qdStandard, biddingType, standardId, isCostDe, isStandard, zjcsClassCode, isAutoCost, rcjFlag, isSupplement, children, closeFlag, sortNoInitDiffValue, libraryCode, matchStatus, baseNum, costDeMatchType, lockPriceFlag) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.parentId = parentId;
        this.sequenceNbr = sequenceNbr;
        this.kind = kind;
        this.dispNo = dispNo;
        this.displayStatu = displayStatu;
        this.displaySign = displaySign;
        this.optionMenu = optionMenu;
        this.zjfPrice = zjfPrice;
        this.zjfPriceFormula = zjfPriceFormula;
        this.zjfTotal = zjfTotal;
        this.price = price;
        this.total = total;
        this.rfee = rfee;
        this.totalRfee = totalRfee;
        this.cfee = cfee;
        this.totalCfee = totalCfee;
        this.jfee = jfee;
        this.totalJfee = totalJfee;
        this.managerFee = managerFee;
        this.totalManagerFee = totalManagerFee;
        this.profitFee = profitFee;
        this.totalProfitFee = totalProfitFee;
        this.zcfee = zcfee;
        this.totalZcfee = totalZcfee;
        this.bdCode = bdCode;
        this.name = bdName;
        this.projectAttr = projectAttr;
        this.costMajorName = costMajorName;
        this.costFileCode = costFileCode;
        this.measureType = measureType;
        this.fbId = fbId;
        this.unitId = unitId;
        this.spId = spId;
        this.quantity = quantity;
        this.quantityExpression = quantityExpression;
        this.quantityExpressionNbr = quantityExpressionNbr;
        this.unit = unit;
        this.unpricedCfee = unpricedCfee;
        this.rfeePrice = rfeePrice;
        this.zhygfl = zhygfl;
        this.releaseDate = releaseDate;
        this.ssProvince = ssProvince;
        this.ssCity = ssCity;
        this.sortNo = sortNo;
        this.remark = remark;
        this.creator = creator;
        this.updater = updater;
        this.deptId = deptId;
        this.status = status;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.delFlag = delFlag;
        this.tenantId = tenantId;
        this.bzjd = bzjd;
        this.taxMode = taxMode;
        this.qdStandard = qdStandard;
        this.biddingType = biddingType;
        this.standardId = standardId;
        this.isCostDe = isCostDe;
        this.isStandard = isStandard;
        this.zjcsClassCode = zjcsClassCode;
        this.isAutoCost = isAutoCost;
        this.rcjFlag = rcjFlag;
        this.isSupplement = isSupplement;
        this.children = children;
        this.closeFlag = closeFlag;
        this.sortNoInitDiffValue = sortNoInitDiffValue;
        this.libraryCode = libraryCode;
        this.matchStatus = matchStatus;
        this.baseNum = baseNum;
        this.costDeMatchType = costDeMatchType;
        this.lockPriceFlag = lockPriceFlag;
    }
}
exports.ItemBillProject = ItemBillProject;
//# sourceMappingURL=ItemBillProject.js.map