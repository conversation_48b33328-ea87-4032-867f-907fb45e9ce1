"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstructProjectRcj = void 0;
const BaseModel_1 = require("./BaseModel");
/**
 * 工程项目人材机表(JieSuanConstructProjectRcj)DTO类
 * 与BS保持一致
 */
class ConstructProjectRcj extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, materialCode, standardId, materialName, specification, unit, unitId, informationPrice, dispNo, recommendPrice, marketPriceOrigin, marketSourcePriceOrigin, informationPriceOrigin, informationSourcePriceOrigin, recommendPriceOrigin, recommendSourcePriceOrigin, marketPriceBeforeLoading, marketSourcePriceBeforeLoading, sourcePrice, iscount, priceDifferenc, priceDifferencSum, brand, manufactor, producer, deliveryLocation, qualityGrade, taxRemoval, taxRemovalBackUp, csMarketPrice, kind, libraryCode, materialCategory, threeMaterialCategories, area, type, priceDate, updateDate, recUserId, constructId, donorMaterialPrice, donorMaterialName, donorSpecification, ifDonorMaterial, donorMaterialNumber, donorMaterialNumberManage, kindBackUp, materialSequenceNbr, ifLockStandardPrice, ifLockQuantity, provisionalEstimateName, provisionalEstimatePrice, provisionalEstimateSpecification, provisionalEstimateSequenceNbr, ifProvisionalEstimate, deId, resQty, confficientInitQty, initResQty, levelMark, totalNumber, total, rcjDetailsDTOs, markSum, jxTotal, referenceRecord, labelDe, edit, supplementDeRcjFlag, rcjFlag, addRcjType, resQtyChangeType, kind3dType, materialReplaceHistory, isExecuteLoadPrice, loadPrice, kind7Sort, unitPostil, unitPostilState, singlePostil, singlePostilState, constructPostil, constructPostilState, isCbrRcj, highlight, informationSourcePrice, marketSourcePrice, recommendSourcePrice, consumerResQty, dePrice, marketPrice, marketPriceFormula, tempDeleteFlag, tempDeleteBackupResQty, isFyrcj, isChangeAva, addFromConversionRuleId, changeResQtyRuleIds, ruleIds, freezeRuleIds, defaultMaterialCode, kindSc, transferFactor, priceBaseJournal, priceBaseJournalTax, priceMarket, priceMarketTax, postilState, priceMarketFormula, priceMarketTaxFormula, taxRate, isSupplement, rcjDeNumberMap, isLock) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.materialCode = materialCode;
        this.standardId = standardId;
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.unitId = unitId;
        this.informationPrice = informationPrice;
        this.dispNo = dispNo;
        this.recommendPrice = recommendPrice;
        this.marketPriceOrigin = marketPriceOrigin;
        this.marketSourcePriceOrigin = marketSourcePriceOrigin;
        this.informationPriceOrigin = informationPriceOrigin;
        this.informationSourcePriceOrigin = informationSourcePriceOrigin;
        this.recommendPriceOrigin = recommendPriceOrigin;
        this.recommendSourcePriceOrigin = recommendSourcePriceOrigin;
        this.marketPriceBeforeLoading = marketPriceBeforeLoading;
        this.marketSourcePriceBeforeLoading = marketSourcePriceBeforeLoading;
        this.sourcePrice = sourcePrice;
        this.iscount = iscount;
        this.priceDifferenc = priceDifferenc;
        this.priceDifferencSum = priceDifferencSum;
        this.brand = brand;
        this.manufactor = manufactor;
        this.producer = producer;
        this.deliveryLocation = deliveryLocation;
        this.qualityGrade = qualityGrade;
        this.taxRemoval = taxRemoval;
        this.taxRemovalBackUp = taxRemovalBackUp;
        this.csMarketPrice = csMarketPrice;
        this.kind = kind;
        this.libraryCode = libraryCode;
        this.materialCategory = materialCategory;
        this.threeMaterialCategories = threeMaterialCategories;
        this.area = area;
        this.type = type;
        this.priceDate = priceDate;
        this.updateDate = updateDate;
        this.recUserId = recUserId;
        this.constructId = constructId;
        this.donorMaterialPrice = donorMaterialPrice;
        this.donorMaterialName = donorMaterialName;
        this.donorSpecification = donorSpecification;
        this.ifDonorMaterial = ifDonorMaterial;
        this.donorMaterialNumber = donorMaterialNumber;
        this.donorMaterialNumberManage = donorMaterialNumberManage;
        this.kindBackUp = kindBackUp;
        this.materialSequenceNbr = materialSequenceNbr;
        this.ifLockStandardPrice = ifLockStandardPrice;
        this.ifLockQuantity = ifLockQuantity;
        this.provisionalEstimateName = provisionalEstimateName;
        this.provisionalEstimatePrice = provisionalEstimatePrice;
        this.provisionalEstimateSpecification = provisionalEstimateSpecification;
        this.provisionalEstimateSequenceNbr = provisionalEstimateSequenceNbr;
        this.ifProvisionalEstimate = ifProvisionalEstimate;
        this.deId = deId;
        this.resQty = resQty;
        this.confficientInitQty = confficientInitQty;
        this.initResQty = initResQty;
        this.levelMark = levelMark;
        this.totalNumber = totalNumber;
        this.total = total;
        this.rcjDetailsDTOs = rcjDetailsDTOs;
        this.markSum = markSum;
        this.jxTotal = jxTotal;
        this.referenceRecord = referenceRecord;
        this.labelDe = labelDe;
        this.edit = edit;
        this.supplementDeRcjFlag = supplementDeRcjFlag;
        this.rcjFlag = rcjFlag;
        this.addRcjType = addRcjType;
        this.resQtyChangeType = resQtyChangeType;
        this.kind3dType = kind3dType;
        this.materialReplaceHistory = materialReplaceHistory;
        this.isExecuteLoadPrice = isExecuteLoadPrice;
        this.loadPrice = loadPrice;
        this.highlight = highlight;
        this.informationSourcePrice = informationSourcePrice;
        this.marketSourcePrice = marketSourcePrice;
        this.recommendSourcePrice = recommendSourcePrice;
        this.consumerResQty = consumerResQty;
        this.dePrice = dePrice;
        this.marketPrice = marketPrice;
        this.tempDeleteFlag = tempDeleteFlag;
        this.tempDeleteBackupResQty = tempDeleteBackupResQty;
        this.isFyrcj = isFyrcj;
        this.isChangeAva = isChangeAva;
        this.addFromConversionRuleId = addFromConversionRuleId;
        this.changeResQtyRuleIds = changeResQtyRuleIds;
        this.ruleIds = ruleIds;
        this.freezeRuleIds = freezeRuleIds;
        this.defaultMaterialCode = defaultMaterialCode;
        this.kindSc = kindSc;
        this.transferFactor = transferFactor;
        this.priceBaseJournal = priceBaseJournal;
        this.priceBaseJournalTax = priceBaseJournalTax;
        this.priceMarket = priceMarket;
        this.priceMarketTax = priceMarketTax;
        this.priceMarketFormula = priceMarketFormula;
        this.priceMarketTaxFormula = priceMarketTaxFormula;
        this.marketPriceFormula = marketPriceFormula;
        this.taxRate = taxRate;
        this.isSupplement = isSupplement;
        this.rcjDeNumberMap = rcjDeNumberMap;
        this.isLock = isLock;
        this.kind7Sort = kind7Sort;
        this.isCbrRcj = isCbrRcj;
        this.unitPostil = unitPostil;
        this.unitPostilState = unitPostilState;
        this.singlePostil = singlePostil;
        this.singlePostilState = singlePostilState;
        this.constructPostil = constructPostil;
        this.constructPostilState = constructPostilState;
    }
}
exports.ConstructProjectRcj = ConstructProjectRcj;
//# sourceMappingURL=ConstructProjectRcj.js.map