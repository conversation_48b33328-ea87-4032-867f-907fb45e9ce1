const { Controller } = require('../../../core');
const { ResponseData } = require('../utils/ResponseData');
const GljFxtjCostMatchContext = require('../service/costMatch/fxtjCostMatch/gljFxtjCostMatchContext');
const CostDeMatchConstants = require('../constants/CostDeMatchConstants');
const GljFgjzZxxjxFeeMatchHandle = require('../service/costMatch/fgjzCostMatch/gljFgjzZxxjxFeeMatchHandle');
const GljGjmqCostMatchContext = require('../service/costMatch/gjmqCostMatch/gljGjmqCostMatchContext');
/**
 *
 */
class GljConstructCostMathController extends Controller {


  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 装饰垂直运输----装饰垂运层高下拉框
   */
  async storeyList(args) {
    const result = await this.service.gongLiaoJiProject.gljConstructCostMathService.storeyList(args);
    return ResponseData.success(result);
  }

  /**
   * 装饰垂直运输----获取分部分项 单价措施下指定定额册的定额
   */
  async conditionDeList(arg) {
    const result = await this.service.gongLiaoJiProject.gljConstructCostMathService.conditionDeList(arg);
    return ResponseData.success(result);
  }

  /**
   * 装饰垂直运输----装饰垂运记取
   */
  async czysCostMath(arg, redo="记取- -装饰垂运") {
    const result = await this.service.gongLiaoJiProject.gljConstructCostMathService.czysCostMath(arg);
    return ResponseData.success(result);
  }


  /**
   * 装饰垂直运输----装饰垂运记取参数缓存
   */
  async cyCostMathCache(arg) {
    const result = await this.service.gongLiaoJiProject.gljConstructCostMathService.cyCostMathCache(arg);
    return ResponseData.success(result);
  }

  //--------------------------------------------------------------------------------------------------------------------


  /**
   * 超高费列表数据
   */
  async cgViewData(arg) {
    arg.feeType = CostDeMatchConstants.CG;
    return ResponseData.success(await this.service.gongLiaoJiProject.gljConstructCostMathService.cgViewData(arg));
  }

  /**
   * 获取超高费缓存
   */
  async cgCostMathCache(arg) {
    const result = await this.service.gongLiaoJiProject.gljConstructCostMathService.cgCostMathCache(arg);
    return ResponseData.success(result);
  }

  /**
   * 超高费记取
   */
  async cgCostMath(arg, redo="记取- -装饰超高") {
    const result = await this.service.gongLiaoJiProject.gljConstructCostMathService.cgCostMath(arg);
    return ResponseData.success(result);
  }

  /**
   * 获取超高费的可用清单
   */
  async cgQdList(arg) {
    const result = await this.service.gongLiaoJiProject.gljConstructCostMathService.cgQdList(arg);
    return ResponseData.success(result);
  }

  // -------------------------------------------------------------------------------------------------------------------

  /**
   * 获取泵送增加费弹窗数据
   */
  async getPumpingAddFeeViewData(args) {
    return ResponseData.success(await this.service.gongLiaoJiProject.gljPumpingAddFeeService.getPumpingAddFeeViewData(args));
  }

  /**
   * 计算泵送增加费
   */
  async calculationPumpingAddFee(args) {
    return ResponseData.success(await this.service.gongLiaoJiProject.gljPumpingAddFeeService.calculationPumpingAddFee(args));
  }

  //--------------------------------------------------------------------------------------------------------------------

  /**
   * 安装费用----费用记取列表
   */
  async azCostMathList(arg) {
    const result = await this.service.gongLiaoJiProject.gljAzCostMathService.azCostMathList(arg);
    return ResponseData.success(result);
  }

  /**
   * 安装费用----基数定额列表查询
   */
  async baseDeList(arg) {
    const result = await this.service.gongLiaoJiProject.gljAzCostMathService.baseDeList(arg);
    return ResponseData.success(result);
  }

  /**
   * 安装费用----清单列表查询
   */
  async qdList(arg) {
    const result = await this.service.gongLiaoJiProject.gljAzCostMathService.qdList(arg);
    return ResponseData.success(result);
  }

  /**
   * 安装费用----记取接口
   */
  async azCostMath(arg,redo="记取- -安装费用") {
    const result = await this.service.gongLiaoJiProject.gljAzCostMathService.azCostMath(arg);
    return ResponseData.success(result);
  }

  /**
   * 安装费用----安装记取缓存
   */
  async azCostMathCache(arg) {
    const result = await this.service.gongLiaoJiProject.gljAzCostMathService.azCostMathCache(arg);
    return ResponseData.success(result);
  }

  /**
   * 查询安装的基数定额对应的章节列表
   */
  async queryBaseDeChapter(args) {
    return ResponseData.success(await this.service.gongLiaoJiProject.gljAzCostMathService.queryBaseDeChapter(args));
  }

  // -------------------------------------------------------------------------------------------------------------------

  /**
   * 记取总价措施
   */
  async zjcsCostMath(arg, redo="自动计算- -措施费用") {
    const result = await this.service.gongLiaoJiProject.gljZjcsCostMatchService.zjcsCostMath(arg);
    return ResponseData.success(result);
  }

  /**
   * 清除总价措施记取
   */
  async clearZjcsCost(arg, redo="清除- -措施费") {
    const result = await this.service.gongLiaoJiProject.gljZjcsCostMatchService.clearZjcsCost(arg);
    return ResponseData.success(result);
  }

  /**
   * 记取总价措施-获取总价措施费用分类列表
   */
  async zjcsClassList(arg) {
    const result = await this.service.gongLiaoJiProject.gljZjcsCostMatchService.zjcsClassList(arg);
    return ResponseData.success(result);
  }

  /**
   * 高台施工增加高度
   */
  async gtfResource() {
    const result = await this.service.gongLiaoJiProject.gljZjcsCostMatchService.gtfResource();
    return ResponseData.success(result);
  }


  /**
   * 总价措施----总价措施参数缓存
   */
  async zjcsCostMathCache(arg) {
    const result = await this.service.gongLiaoJiProject.gljZjcsCostMatchService.zjcsCostMathCache(arg);
    return ResponseData.success(result);
  }

  async queryCalculateBaseDropDownList(arg) {
    const result = await this.service.gongLiaoJiProject.gljZjcsCostMatchService.queryCalculateBaseDropDownList(arg);
    return ResponseData.success(result);
  }

  async queryParticipationZjcsMatch(arg) {
    const result = await this.service.gongLiaoJiProject.gljZjcsCostMatchService.queryParticipationZjcsMatch(arg);
    return ResponseData.success(result);
  }
  //--------------------------------------------------------------------------------------------------------------------

  /**
   * 获取页面展示的数据  包括【列表基数定额】和【点击记取位置对应的清单数据】
   */
  async getViewData(args) {
    // feeType代表费用类型
    const { feeType } = args;
    return ResponseData.success(await new GljFxtjCostMatchContext(feeType).getViewData(args));
  }


  /**
   * 记取
   */
  async fxtjCostMatch(args, redo="记取- -房修土建费用") {
    // feeType代表费用类型
    const { feeType } = args;
    return ResponseData.success(await new GljFxtjCostMatchContext(feeType).fxtjCostMatch(args));
  }

  /**
   * 获取缓存
   */
  async getFxtjCache(args) {
    // feeType代表费用类型
    const { feeType } = args;
    return ResponseData.success(await new GljFxtjCostMatchContext(feeType).getFxtjCache(args));
  }

  // -------------------------------------------------------------------------------------------------------------------

  /**
   * 获取水电费列表数据
   */
  async getWaterElectricCostData(args) {
    return ResponseData.success(await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.getWaterElectricCostData(args));
  }

  /**
   * 水电费页面临时更新水电费数据
   */
  async updateWaterElectricCostData(args, redo="临时编辑- -水电费") {
    return ResponseData.success(await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.updateWaterElectricCostData(args));
  }

  /**
   * 保存水电费数据
   */
  async saveWaterElectricCostData(args, redo="记取- -水电费") {
    return ResponseData.success(await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.saveWaterElectricCostData(args));
  }

  /**
   * 仿古建筑
   */
   /**
   * 获取页面展示的数据  包括【列表基数定额】和【点击记取位置对应的清单数据】
   */
  async getFgjzViewData(args) {
    // feeType代表费用类型
    const { feeType } = args;
    if(feeType === CostDeMatchConstants.ZXXJX){
      args.feeType = CostDeMatchConstants.FGJZ_ZXXJX
    }
    let res = await new GljFgjzZxxjxFeeMatchHandle().getViewData(args)
    return ResponseData.success(res);
  }


  /**
   * 记取
   */
  async fgjzCostMatch(args) {  
    const { feeType } = args;
    if(feeType === CostDeMatchConstants.ZXXJX){
      args.feeType = CostDeMatchConstants.FGJZ_ZXXJX
    } 
    return ResponseData.success(await new GljFgjzZxxjxFeeMatchHandle().costMatch(args));
  }

  /**
   * 获取缓存
   */
  async getFgjzCache(args) {
    const { feeType } = args;
    if(feeType === CostDeMatchConstants.ZXXJX){
      args.feeType = CostDeMatchConstants.FGJZ_ZXXJX
    } 
    return ResponseData.success(await new GljFgjzZxxjxFeeMatchHandle().getCache(args));
  }
  
  // ----------------------------------------------------------------------
  /**
   * 明清古建
   * -----------------------------------------------------------------------
   */
   /**
   * 获取页面展示的数据  包括【列表基数定额】和【点击记取位置对应的清单数据】
   */
  async getGjmqViewData(args) {
    // feeType代表费用类型
    const { feeType } = args;
    if(feeType === CostDeMatchConstants.CG){
      args.feeType = CostDeMatchConstants.GJMQ_CG;
    } 
    if(feeType === CostDeMatchConstants.CZYS){
      args.feeType = CostDeMatchConstants.GJMQ_CZYS;
    } 
    if(feeType === CostDeMatchConstants.ZXXJX){
      args.feeType = CostDeMatchConstants.GJMQ_ZXXJX;
    } 
    return ResponseData.success(await new GljGjmqCostMatchContext(args.feeType).getViewData(args));
  }


  /**
   * 记取
   */
  async gjmqCostMatch(args) {
    // feeType代表费用类型
    const { feeType } = args;
    if(feeType === CostDeMatchConstants.CG){
      args.feeType = CostDeMatchConstants.GJMQ_CG;
    } 
    if(feeType === CostDeMatchConstants.CZYS){
      args.feeType = CostDeMatchConstants.GJMQ_CZYS;
    } 
    if(feeType === CostDeMatchConstants.ZXXJX){
      args.feeType = CostDeMatchConstants.GJMQ_ZXXJX;
    } 
    return ResponseData.success(await new GljGjmqCostMatchContext(args.feeType).costMatch(args));
  }

  /**
   * 获取缓存
   */
  async getGjmqCache(args) {
    // feeType代表费用类型
    const { feeType } = args;
    if(feeType === CostDeMatchConstants.CG){
      args.feeType = CostDeMatchConstants.GJMQ_CG;
    } 
    if(feeType === CostDeMatchConstants.CZYS){
      args.feeType = CostDeMatchConstants.GJMQ_CZYS;
    } 
    if(feeType === CostDeMatchConstants.ZXXJX){
      args.feeType = CostDeMatchConstants.GJMQ_ZXXJX;
    } 
    return ResponseData.success(await new GljGjmqCostMatchContext(args.feeType).getCache(args));
  }
  // --------------------------------------------------------------------------------------
}

GljConstructCostMathController.toString = () => '[class GljConstructCostMathController]';
module.exports = GljConstructCostMathController;