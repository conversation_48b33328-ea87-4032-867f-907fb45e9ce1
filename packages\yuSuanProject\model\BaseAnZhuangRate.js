"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAnZhuangRate2022 = exports.BaseAnZhuangRate = void 0;
const typeorm_1 = require("typeorm");
const typeorm_2 = require("typeorm");
/**
 * 定额表
 */
let BaseAnZhuangRate = class BaseAnZhuangRate {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fee_code", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "feeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fee_name", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "feeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "class_level1", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "classLevel1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "class_level2", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "classLevel2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "allocation_method", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate.prototype, "allocationMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "calculate_base", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "calculateBase", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate.prototype, "rate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "r_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate.prototype, "rRate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "c_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate.prototype, "cRate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "j_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate.prototype, "jRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_list", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "relationList", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_list_id", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "relationListId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "layer_interval", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "layerInterval", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "height_range", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate.prototype, "heightRange", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "is_default", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "sort_number", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate.prototype, "sortNumber", void 0);
BaseAnZhuangRate = __decorate([
    (0, typeorm_2.Entity)({ name: "base_anzhuang_rate" })
], BaseAnZhuangRate);
exports.BaseAnZhuangRate = BaseAnZhuangRate;
let BaseAnZhuangRate2022 = class BaseAnZhuangRate2022 {
};
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: "sequence_nbr" }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fee_code", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "feeCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "fee_name", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "feeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "class_level1", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "classLevel1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "class_level2", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "classLevel2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "allocation_method", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate2022.prototype, "allocationMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "calculate_base", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "calculateBase", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate2022.prototype, "rate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "r_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate2022.prototype, "rRate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "c_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate2022.prototype, "cRate", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "j_rate", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate2022.prototype, "jRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_list", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "relationList", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_list_id", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "relationListId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "layer_interval", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "layerInterval", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "height_range", nullable: true }),
    __metadata("design:type", String)
], BaseAnZhuangRate2022.prototype, "heightRange", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "is_default", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate2022.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)("decimal", { name: "sort_number", nullable: true }),
    __metadata("design:type", Number)
], BaseAnZhuangRate2022.prototype, "sortNumber", void 0);
BaseAnZhuangRate2022 = __decorate([
    (0, typeorm_2.Entity)({ name: "base_anzhuang_rate_2022" })
], BaseAnZhuangRate2022);
exports.BaseAnZhuangRate2022 = BaseAnZhuangRate2022;
//# sourceMappingURL=BaseAnZhuangRate.js.map