const WildcardMap = require('../core/container/WildcardMap');
const {Service} = require("../../../core");
const gljFydm = require("../jsonData/glj_fydm.json");
const {Snowflake} = require("../utils/Snowflake");
const ProjectDomain = require("../domains/ProjectDomain");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");
const CostCodeTypeEnum = require("../enums/CostCodeTypeEnum");
const ResourceKindConstants = require("../constants/ResourceKindConstants");
const {NumberUtil} = require("../utils/NumberUtil");
const {GljUnitCostCodePrice} = require("../models/GljUnitCostCodePrice");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {getConnection, getRepository, getManager} = require('typeorm');
const RcjCommonConstants = require('../constants/RcjCommonConstants');
const TaxCalculationMethodEnum = require("../enums/TaxCalculationMethodEnum");
const DeTypeConstants = require("../constants/DeTypeConstants");
const UnitConstructMajorTypeConstants = require("../constants/UnitConstructMajorTypeConstants");
const {GljUnitCostSummary} = require("../models/GljUnitCostSummary");
const {GljConstructProjectRcj} = require('../models/GljConstructProjectRcj');
const CommonConstants = require("../../gongLiaoJiProject/constants/CommonConstants");

/**
 * 单位费用代码  service
 */
class GljUnitCostCodePriceService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取默认费用代码
     * @returns {any[]}
     */
    defaultUnitCostCodePrice(args) {
        const {constructId, singleId, unitId, qfMajorType} = args;

        let unitCostCodeArray = [];
        for (let i in gljFydm) {
            let obj = new GljUnitCostCodePrice();
            ConvertUtil.setDstBySrc(gljFydm[i], obj)
            obj.price = 0;
            obj.sequenceNbr = Snowflake.nextId();
            unitCostCodeArray.push(obj);
        }
        return unitCostCodeArray;
    }

    /**
     * 获取单位费用代码
     * @param args
     * @returns {*}
     */
    async getUnitCostCodePrice(args) {
        const {constructId, singleId, unitId, qfMajorType} = args;

        // 获取单位工程
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // 获取费用代码
        let unitCostCodeArray = ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
            .get(unitId + FunctionTypeConstants.SEPARATOR + qfMajorType);

        if (ObjectUtils.isNotEmpty(unitCostCodeArray) &&
            (unitProject.isSingleMajorFlag || (qfMajorType !== UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL && !unitProject.isSingleMajorFlag))) {
            unitCostCodeArray = unitCostCodeArray.filter(item => item.name !== "企业管理费" && item.name !== "利润"
                && item.name !== "安全文明施工费" && item.name !== "税金");
        }
        return unitCostCodeArray;
    }

    /**
     * 费用代码明细左侧目录树  注：因为类型用汉字表示了，该代码去重让前端写了
     * @returns {unknown[] | any[]}
     */
    async costCodeTypeList() {
        return Object.values(CostCodeTypeEnum)
    }

    /**
     * 根据费用代码类型获取费用代码
     * @param args
     * @returns {ResponseData}
     */
    async costCodePrice(args) {
        const {constructId, singleId, unitId, qfMajorType, type} = args;
        let unitCostCodePrice = await this.getUnitCostCodePrice(args);
        if (ObjectUtils.isNotEmpty(unitCostCodePrice)) {
            if (args.type) {
                return unitCostCodePrice.filter(item => item.type === args.type);
            } else {
                return unitCostCodePrice;
            }
        }
    }


    /**
     * 获取单位基础数据
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getUnitBaseDate(constructId, singleId, unitId) {
        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let budgetBookTypes, csxmTypes, independentCostTypes;
        // 1. 获取预算书定额的所有取费专业类型    //  && item.type === DeTypeConstants.DE_TYPE_DE
        let yssDes = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId && ObjectUtils.isNotEmpty(item.costFileCode));
        if (ObjectUtils.isNotEmpty(yssDes)) {
            budgetBookTypes = yssDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        }

        // 2. 获取措施项目定额的所有取费专业类型   //  && item.type === DeTypeConstants.DE_TYPE_DE    '03'
        let csxmDes = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && !['0', '01', '02'].includes(item.type));
        if (ObjectUtils.isNotEmpty(csxmDes)) {
            csxmTypes = csxmDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        }

        // 3. 独立费的所有取费专业类型
        let independentCosts = businessMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            .get(await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unitId))?.filter(item => item.isRcj !== true)
        if (ObjectUtils.isNotEmpty(independentCosts)) {
            independentCostTypes = independentCosts.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costMajorCode)).map(item => item.costMajorCode);
        }

        // 4. 获取该单位所有的专业：专业名称数组判空且合并去重，没有数据则返回
        let nonEmptyLists = [budgetBookTypes, csxmTypes, independentCostTypes].filter(arr => ObjectUtils.isNotEmpty(arr));
        let constructMajorTypeSet = [...new Set(nonEmptyLists.flat())];

        // 获取单位工程
        // let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // if (ObjectUtils.isEmpty(constructMajorTypeSet)) return;

        // 5. 获取措施项目中的清单，给措施项目的定额添加计价方式
        let csxmQds = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_DELIST);
        let csxmQdMap = new Map(csxmQds.map(item => [item.sequenceNbr, item]));  // 获取措施项目中的清单 Map

        let csxmAllDes = [];
        let csxmAllDeList = ConvertUtil.deepCopy(csxmDes);
        let csxmLength = csxmAllDeList.length;
        for (let i = 0; i < csxmLength; i++) {
            let de = csxmAllDeList[i];
            if (csxmQdMap.has(de.parentId)) {
                let qd = csxmQdMap.get(de.parentId);
                if (qd.sequenceNbr === de.parentId) {
                    de.pricingMethod = qd.pricingMethod;
                    csxmAllDes.push(de);
                }
            }
        }
        // 6. 定额分组
        const yssDeMap = new Map(yssDes.map(item => [item.sequenceNbr, item]));
        const zjcsxmDes = csxmAllDes.filter(item => item.pricingMethod === 2);
        const zjcsxmDeMap = new Map(csxmAllDes.filter(item => item.pricingMethod === 2).map(item => [item.sequenceNbr, item]));
        const fzjcsxmQdMap = new Map(csxmQds.filter(item => item.pricingMethod === 1 || item.pricingMethod === 3).map(item => [item.sequenceNbr, item]));
        const fzjcsxmQds = csxmQds.filter(item => item.pricingMethod === 1);

        // 7. 获取当前单位所有的人材机
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let allRcjs = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        let allRcjList = ConvertUtil.deepCopy(allRcjs);
        // 添加人材机二次解析
        allRcjList.filter(item => ObjectUtils.isNotEmpty(item.pbs)).forEach(item => {
            allRcjList.push(...item.pbs);
        });


        let yssAllRcjs = [], zjcsxmAllRcjs = [];
        for (let i = 0; i < allRcjList.length; i++) {
            let rcj = allRcjList[i];
            // 7.1 获取预算书人材机
            if (yssDeMap.has(rcj.deRowId)) {
                let yssDe = yssDeMap.get(rcj.deRowId);
                if (ObjectUtils.isNotEmpty(yssDe)) {
                    rcj.costFileCode = yssDe.costFileCode;
                    yssAllRcjs.push(rcj);
                }
            }
            // 7.2 获取措施项目人材机
            if (zjcsxmDeMap.has(rcj.deRowId)) {
                let csxmDe = zjcsxmDeMap.get(rcj.deRowId);
                if (ObjectUtils.isNotEmpty(csxmDe)) {
                    rcj.costFileCode = csxmDe.costFileCode;
                    zjcsxmAllRcjs.push(rcj);
                }
            }
        }

        // 7.3 遍历map，获取非组价措施项目人材机分摊
        let fzjcsxmAllRcjs = [];
        let objMap = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST);
        if (ObjectUtils.isEmpty(objMap)) {
            objMap = new Map();
            businessMap.set(FunctionTypeConstants.RCJ_SHARE_COST, objMap);
        }
        let shareCostUnit = objMap.get(unitId);
        if (ObjectUtils.isNotEmpty(shareCostUnit)) {
            for (const [key, value] of shareCostUnit.entries()) {
                console.log(key, value);
                if (fzjcsxmQdMap.has(key)) {
                    let de = fzjcsxmQdMap.get(key);
                    for (let i = 0; i < value.length; i++) {
                        let rcj = value[i];
                        let obj = {};
                        obj.sequenceNbr = Snowflake.nextId();
                        obj.deId = key;
                        obj.name = rcj.name;
                        obj.ratio = rcj.ratio;
                        obj.value = rcj.value;
                        obj.costFileCode = de.costFileCode;
                        fzjcsxmAllRcjs.push(obj);
                    }
                }
            }
        }

        let map = new Map();
        map.set("yssDes", yssDes);
        map.set("yssAllRcjs", yssAllRcjs);
        map.set("zjcsxmDes", zjcsxmDes);
        map.set("zjcsxmAllRcjs", zjcsxmAllRcjs);
        map.set("fzjcsxmAllRcjs", fzjcsxmAllRcjs);
        map.set("allRcjList", allRcjList);
        map.set("fzjcsxmQds", fzjcsxmQds);
        map.set("independentCosts", independentCosts);
        map.set("constructMajorTypeSet", constructMajorTypeSet);

        return map;
    }


    /**
     * 计算当前工程专业的费用代码和更新费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async countCostCodePrice(args) {
        let {constructId, singleId, unitId, qfMajorType} = args;


        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        // 获取单位工程
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);

        // let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        // let budgetBookTypes, csxmTypes, independentCostTypes;
        // // 1. 获取预算书定额的所有取费专业类型    //  && item.type === DeTypeConstants.DE_TYPE_DE
        // let yssDes = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId && ObjectUtils.isNotEmpty(item.costFileCode));
        // if (ObjectUtils.isNotEmpty(yssDes)) {
        //     budgetBookTypes = yssDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        // }
        //
        // // 2. 获取措施项目定额的所有取费专业类型   //  && item.type === DeTypeConstants.DE_TYPE_DE    '03'
        // let csxmDes = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && !['0', '01', '02'].includes(item.type));
        // if (ObjectUtils.isNotEmpty(csxmDes)) {
        //     csxmTypes = csxmDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        // }
        //
        // // 3. 独立费的所有取费专业类型
        // let independentCosts = businessMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
        //     .get(await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unitId))?.filter(item => item.isRcj !== true)
        // if (ObjectUtils.isNotEmpty(independentCosts)) {
        //     independentCostTypes = independentCosts.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costMajorCode)).map(item => item.costMajorCode);
        // }
        //
        // // 4. 获取该单位所有的专业：专业名称数组判空且合并去重，没有数据则返回
        // let nonEmptyLists = [budgetBookTypes, csxmTypes, independentCostTypes].filter(arr => ObjectUtils.isNotEmpty(arr));
        // let constructMajorTypeSet = [...new Set(nonEmptyLists.flat())];
        //
        // // 获取单位工程
        // let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // // if (ObjectUtils.isEmpty(constructMajorTypeSet)) return;
        //
        // // 5. 获取措施项目中的清单，给措施项目的定额添加计价方式
        // let csxmQds = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_DELIST);
        // let csxmQdMap = new Map(csxmQds.map(item => [item.sequenceNbr, item]));  // 获取措施项目中的清单 Map
        //
        // let csxmAllDes = [];
        // let csxmAllDeList = ConvertUtil.deepCopy(csxmDes);
        // let csxmLength = csxmAllDeList.length;
        // for (let i = 0; i < csxmLength; i++) {
        //     let de = csxmAllDeList[i];
        //     if (csxmQdMap.has(de.parentId)) {
        //         let qd = csxmQdMap.get(de.parentId);
        //         if (qd.sequenceNbr === de.parentId) {
        //             de.pricingMethod = qd.pricingMethod;
        //             csxmAllDes.push(de);
        //         }
        //     }
        // }
        // // 6. 定额分组
        // const yssDeMap = new Map(yssDes.map(item => [item.sequenceNbr, item]));
        // const zjcsxmDes = csxmAllDes.filter(item => item.pricingMethod === 2);
        // const zjcsxmDeMap = new Map(csxmAllDes.filter(item => item.pricingMethod === 2).map(item => [item.sequenceNbr, item]));
        // const fzjcsxmQdMap = new Map(csxmQds.filter(item => item.pricingMethod === 1 || item.pricingMethod === 3).map(item => [item.sequenceNbr, item]));
        // const fzjcsxmQds = csxmQds.filter(item => item.pricingMethod === 1);
        //
        // // 7. 获取当前单位所有的人材机
        // let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        // let allRcjList = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        //
        // let yssAllRcjs = [], zjcsxmAllRcjs = [];
        // for (let i = 0; i < allRcjList.length; i++) {
        //     let rcj = allRcjList[i];
        //     // 7.1 获取预算书人材机
        //     if (yssDeMap.has(rcj.deRowId)) {
        //         let yssDe = yssDeMap.get(rcj.deRowId);
        //         if (ObjectUtils.isNotEmpty(yssDe)) {
        //             rcj.costFileCode = yssDe.costFileCode;
        //             yssAllRcjs.push(rcj);
        //         }
        //     }
        //     // 7.2 获取措施项目人材机
        //     if (zjcsxmDeMap.has(rcj.deRowId)) {
        //         let csxmDe = zjcsxmDeMap.get(rcj.deRowId);
        //         if (ObjectUtils.isNotEmpty(csxmDe)) {
        //             rcj.costFileCode = csxmDe.costFileCode;
        //             zjcsxmAllRcjs.push(rcj);
        //         }
        //     }
        // }
        //
        // // 7.3 遍历map，获取非组价措施项目人材机分摊
        // let fzjcsxmAllRcjs = [];
        // let objMap = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST);
        // if (ObjectUtils.isEmpty(objMap)) {
        //     objMap = new Map();
        //     businessMap.set(FunctionTypeConstants.RCJ_SHARE_COST, objMap);
        // }
        // let shareCostUnit = objMap.get(unitId);
        // if (ObjectUtils.isNotEmpty(shareCostUnit)) {
        //     for (const [key, value] of shareCostUnit.entries()) {
        //         console.log(key, value);
        //         if (fzjcsxmQdMap.has(key)) {
        //             let de = fzjcsxmQdMap.get(key);
        //             for (let i = 0; i < value.length; i++) {
        //                 let rcj = value[i];
        //                 let obj = {};
        //                 obj.sequenceNbr = Snowflake.nextId();
        //                 obj.deId = key;
        //                 obj.name = rcj.name;
        //                 obj.ratio = rcj.ratio;
        //                 obj.value = rcj.value;
        //                 obj.costFileCode = de.costFileCode;
        //                 fzjcsxmAllRcjs.push(obj);
        //             }
        //         }
        //     }
        // }

        // 获取单位基础数据
        let map = await this.getUnitBaseDate(constructId, singleId, unitId);
        let yssDes = map.get("yssDes");
        let yssAllRcjs = map.get("yssAllRcjs");
        let zjcsxmDes = map.get("zjcsxmDes");
        let zjcsxmAllRcjs = map.get("zjcsxmAllRcjs");
        let fzjcsxmAllRcjs = map.get("fzjcsxmAllRcjs");
        let allRcjList = map.get("allRcjList");
        let fzjcsxmQds = map.get("fzjcsxmQds");
        let independentCosts = map.get("independentCosts");
        let constructMajorTypeSet = map.get("constructMajorTypeSet");

        // 获取取费表列表数据  根据取费表中的专业顺序进行排序  todo
        let qfbList = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList({
            constructId:constructId,
            singleId:singleId,
            unitId:unitId
        })
        qfbList = qfbList.sort((a, b) => a.sortNo - b.sortNo);
        constructMajorTypeSet = [...new Set(qfbList.map(item => item.qfCode))];


        // 获取费用代码
        let costCodePrices = businessMap.get(FunctionTypeConstants.UNIT_COST_CODE);

        let yssDeList = [], yssRcjList = [], zjcsxmDeList = [], zjcsxmRcjList = [], fzjcsxmRcjList = [],
            fzjcsxmQdList = [], rcjList = [],
            independentCostList = [], majorName, unitCostCodePrices;
        // 是单专业汇总
        if (unitProject.isSingleMajorFlag) {
            console.log("单专业取费。。。。");
            majorName = unitProject.qfMajorType;

            yssDeList = yssDes;
            yssRcjList = yssAllRcjs;
            zjcsxmDeList = zjcsxmDes;
            zjcsxmRcjList = zjcsxmAllRcjs;
            fzjcsxmRcjList = fzjcsxmAllRcjs;
            rcjList = allRcjList;
            fzjcsxmQdList = fzjcsxmQds;
            if (ObjectUtils.isNotEmpty(independentCosts)) {
                independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.levelType === 2);
            }
            unitCostCodePrices = costCodePrices.get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL);

            // 计算费用代码条目
            await this.countCostCodePriceItems(yssDeList, yssRcjList, zjcsxmDeList, zjcsxmRcjList, fzjcsxmRcjList, fzjcsxmQdList, rcjList, independentCostList, unitCostCodePrices, constructId, singleId, unitId);
            // 更新费用代码
            businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL, unitCostCodePrices);

            // 获取费用汇总
            let unitCostSummarys = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).get(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL);
            //更新费用汇总
            await this.service.gongLiaoJiProject.gljUnitCostSummaryService.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummarys, UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL);
            // 当前专业汇总行
            unitCostSummarys[0].price = unitCostSummarys.find(item => item.category === "工程造价").price;

            // 多专业汇总
        } else {
            console.log("当前单位取费主专业====>" + unitProject.qfMajorType);
            // 处理主专业与随主专业合并
            let newConstructMajorTypeSet = new Set(constructMajorTypeSet.map(item => item === CommonConstants.SYSTEM_SZGC ? unitProject.qfMainMajorType : item));
            let constructMajorTypeList = [...newConstructMajorTypeSet];

            let qfMajorTypeMoneyMap = new Map();
            // 修改单位的取费专业汇总
            constructMajorTypeList.forEach(majorName => {
                qfMajorTypeMoneyMap.set(majorName, 0);
            });
            unitProject.qfMajorTypeMoneyMap = Object.fromEntries(qfMajorTypeMoneyMap);
            ProjectDomain.getDomain(constructId).updateProject(unitProject);

            // 删除原来的费用代码
            let codeMap = businessMap.get(FunctionTypeConstants.UNIT_COST_CODE);
            let newCodeMap = new Map();
            for (let [key, value] of codeMap.entries()) {
                if (!key.includes(unitId)) {
                    newCodeMap.set(key, value);
                }
            }
            // 对专业类型进行费用代码，并对费用汇总填写数据
            businessMap.set(FunctionTypeConstants.UNIT_COST_CODE, newCodeMap);

            // 删除原来的费用汇总
            let codeSummaryMap = businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY);
            let newCodeSummaryMap = new Map();
            for (let [key, value] of codeSummaryMap.entries()) {
                if (!key.includes(unitId)) {
                    newCodeSummaryMap.set(key, value);
                }
            }
            businessMap.set(FunctionTypeConstants.UNIT_COST_SUMMARY, newCodeSummaryMap);

            if (constructMajorTypeList.length === 1) {
                console.log("多专业汇总，单专业时。。。。" + constructMajorTypeList);

                yssDeList = yssDes;
                yssRcjList = yssAllRcjs;
                zjcsxmDeList = zjcsxmDes;
                zjcsxmRcjList = zjcsxmAllRcjs;
                fzjcsxmRcjList = fzjcsxmAllRcjs;
                rcjList = allRcjList;
                fzjcsxmQdList = fzjcsxmQds;
                if (ObjectUtils.isNotEmpty(independentCosts)) {
                    independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.levelType === 2);
                }

                // 处理主专业与随主专业合并
                if (qfMajorType === CommonConstants.SYSTEM_SZGC) {
                    qfMajorType = unitProject.qfMajorType;
                }
                // 为空是，是多专业汇总按钮进来的
                if (ObjectUtils.isEmpty(qfMajorType) || !constructMajorTypeList.includes(qfMajorType)) {
                    majorName = constructMajorTypeList[0];
                    // 进行多专业汇总之后进来的
                } else {
                    majorName = qfMajorType;
                }
                unitCostCodePrices = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.defaultUnitCostCodePrice({
                    constructId: constructId,
                    singleId: singleId,
                    unitId: unitId,
                    qfMajorType: majorName
                });

                // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
                let unitCostSummarys = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary({
                    constructId: constructId,
                    singleId: singleId,
                    unitId: unitId,
                    qfMajorType: majorName
                });
                // 更新或添加费用汇总
                businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostSummarys);

                // 计算费用代码条目
                await this.countCostCodePriceItems(yssDeList, yssRcjList, zjcsxmDeList, zjcsxmRcjList, fzjcsxmRcjList, fzjcsxmQdList, rcjList, independentCostList, unitCostCodePrices, constructId, singleId, unitId);
                // 更新费用代码
                businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePrices);

                //更新费用汇总
                await this.service.gongLiaoJiProject.gljUnitCostSummaryService.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummarys, majorName);
                // 当前专业汇总行
                unitCostSummarys[0].price = unitCostSummarys.find(item => item.category === "工程造价").price;

            } else {
                console.log("多专业汇总，多专业时。。。。" + constructMajorTypeList); // todo 加随主
                for (const majorName of constructMajorTypeList) {
                    // 预算书定额分类
                    if (ObjectUtils.isNotEmpty(yssDes)) {
                        yssDeList = yssDes.filter(item => item.costFileCode === majorName);
                    }
                    if (majorName === unitProject.qfMajorType) {
                        yssDeList = yssDeList.concat(yssDes.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                    }
                    // 预算书人材机分类
                    if (ObjectUtils.isNotEmpty(yssAllRcjs)) {
                        yssRcjList = yssAllRcjs.filter(item => item.costFileCode === majorName);
                    }
                    if (majorName === unitProject.qfMajorType) {
                        yssRcjList = yssRcjList.concat(yssAllRcjs.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                    }

                    // 组价措施项目定额分类
                    if (ObjectUtils.isNotEmpty(zjcsxmDes)) {
                        zjcsxmDeList = zjcsxmDes.filter(item => item.costFileCode === majorName);
                    }
                    if (majorName === unitProject.qfMajorType) {
                        zjcsxmDeList = zjcsxmDeList.concat(zjcsxmDes.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                    }
                    // 组价措施项目人材机分类
                    if (ObjectUtils.isNotEmpty(zjcsxmAllRcjs)) {
                        zjcsxmRcjList = zjcsxmAllRcjs.filter(item => item.costFileCode === majorName);
                    }
                    if (majorName === unitProject.qfMajorType) {
                        zjcsxmRcjList = zjcsxmRcjList.concat(zjcsxmAllRcjs.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                    }

                    // 非组价措施项目人材机分类
                    if (ObjectUtils.isNotEmpty(fzjcsxmAllRcjs)) {
                        fzjcsxmRcjList = fzjcsxmAllRcjs.filter(item => item.costFileCode === majorName);
                    }
                    if (majorName === unitProject.qfMajorType) {
                        fzjcsxmRcjList = fzjcsxmRcjList.concat(fzjcsxmAllRcjs.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                    }

                    // 人材机分类
                    if (ObjectUtils.isNotEmpty(allRcjList)) {
                        rcjList = allRcjList.filter(item => item.costFileCode === majorName);
                    }
                    if (majorName === unitProject.qfMajorType) {
                        rcjList = rcjList.concat(allRcjList.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                    }

                    // 非组价措施项目清单分类
                    if (ObjectUtils.isNotEmpty(fzjcsxmQds)) {
                        fzjcsxmQdList = fzjcsxmQds.filter(item => item.costFileCode === majorName);
                    }
                    if (majorName === unitProject.qfMajorType) {
                        fzjcsxmQdList = fzjcsxmQdList.concat(fzjcsxmQds.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                    }

                    // 独立费分类
                    if (ObjectUtils.isNotEmpty(independentCosts)) {
                        independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.costMajorCode === majorName && item.levelType === 2);
                        if (majorName === unitProject.qfMajorType) {
                            independentCostList = independentCostList.concat(independentCosts.filter(item => item.costMajorCode === CommonConstants.SYSTEM_SZGC && item.levelType === 2))
                        }
                    }

                    // 对专业类型进行初始化费用代码
                    unitCostCodePrices = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.defaultUnitCostCodePrice({
                        constructId: constructId,
                        singleId: singleId,
                        unitId: unitId,
                        qfMajorType: majorName
                    });

                    // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
                    let unitCostSummarys = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary({
                        constructId: constructId,
                        singleId: singleId,
                        unitId: unitId,
                        qfMajorType: majorName
                    });
                    // 更新或添加费用汇总
                    businessMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostSummarys);

                    // 计算费用代码条目
                    await this.countCostCodePriceItems(yssDeList, yssRcjList, zjcsxmDeList, zjcsxmRcjList, fzjcsxmRcjList, fzjcsxmQdList, rcjList, independentCostList, unitCostCodePrices, constructId, singleId, unitId);
                    // 更新费用代码
                    businessMap.get(FunctionTypeConstants.UNIT_COST_CODE).set(unitId + FunctionTypeConstants.SEPARATOR + majorName, unitCostCodePrices);

                    //更新费用汇总
                    await this.service.gongLiaoJiProject.gljUnitCostSummaryService.countUnitCostSummary(constructId, unitId, unitCostCodePrices, unitCostSummarys, majorName);
                    // 当前专业汇总行
                    unitCostSummarys[0].price = unitCostSummarys.find(item => item.category === "工程造价").price;
                }
            }
            // 多专业费用代码合计
            await this.countCostCodePriceTotal({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                unitCostSummarys: null
            });
        }
        return unitCostCodePrices;
    }


    /**
     * 费用代码合计组装
     * @param args
     * @returns {Promise<*[]>}
     */
    async countCostCodePriceTotal(args) {
        let {constructId, singleId, unitId, unitCostSummarys} = args;
        // 获取费用代码模板
        let costCodeCodePriceArray = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.defaultUnitCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            qfMajorType: UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL
        });

        // 合并类型和名称映射
        const codeMaps = costCodeCodePriceArray.reduce((acc, item) => {
            acc.type.set(item.code, item.type);
            acc.name.set(item.code, item.name);
            return acc;
        }, {type: new Map(), name: new Map()});

        // 获取获取费用汇总价格
        let unitCostCodePriceMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostCode({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });

        // 获取汇总费用代码价格
        let unitCostCodeSummaryMap = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getUnitCostSummary({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });

        // 获取单位的工程造价
        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        // 重新组装费用代码
        let sort = 1;
        let unitCostCodePriceTotals = [];
        let array = ['企业管理费', '利润', '税金', '安全文明施工费'];
        for (let [key, value] of unitCostCodePriceMap) {
            let obj = new GljUnitCostCodePrice();
            ConvertUtil.setDstBySrc({code: key, price: value}, obj);
            obj.dispNo = sort++;
            obj.type = codeMaps.type.get(key);
            obj.name = codeMaps.name.get(key);
            if (array.includes(obj.name) && unitCostCodeSummaryMap.get(obj.name)) {
                obj.price = unitCostCodeSummaryMap.get(obj.name);
            }
            if (obj.code === 'GCGM') {
                obj.price = unitProject.average;
            }
            unitCostCodePriceTotals.push(obj);
        }

        // 更新费用代码合计
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_CODE)
            .set(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL, unitCostCodePriceTotals);

        // 获取费用代码模板
        let unitCostCodeSummaryTotals;
        if (unitCostSummarys) {
            unitCostCodeSummaryTotals = unitCostSummarys;
        } else {
            unitCostCodeSummaryTotals = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                qfMajorType: UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL
            });
        }

        // 计算费用汇总
        await this.service.gongLiaoJiProject.gljUnitCostSummaryService.countUnitCostSummaryTotalItem(constructId, unitId, unitCostCodePriceTotals, unitCostCodeSummaryTotals);

        // 获取取费专业
        let qfList = await this.service.gongLiaoJiProject.baseFeeFileService.getAllBaseFeeFile();
        qfList = qfList.filter(item => item.qfCode !== CommonConstants.SYSTEM_SZGC);
        let qfCodeList = qfList.map(item => item.qfCode);

        let qfCodeNameMap = qfList.reduce((acc, item) => {
            acc.set(item.qfCode, item.qfName);
            return acc;
        }, new Map());

        // 当前专业汇总行
        let qfMajorTypeMoneyMap = new Map(Object.entries(unitProject.qfMajorTypeMoneyMap));
        let unitCostSummaryArray = [], total = 0;
        for (let [key, value] of qfMajorTypeMoneyMap) {
            let obj = new GljUnitCostSummary();
            qfCodeList.forEach(qfCode => {
                if (key.includes(qfCode)) {
                    obj.sequenceNbr = Snowflake.nextId();
                    obj.name = qfCodeNameMap.get(qfCode);
                    obj.calculateFormula = qfCode;  //计算基数
                    obj.instructions = qfCodeNameMap.get(qfCode);  //基数说明
                    obj.rate = null;
                    obj.price = value;
                    obj.permission = [];
                    obj.isSummaryLine = true;
                    obj.whetherPrint = 1;
                    total = NumberUtil.add(total, value);
                    unitCostSummaryArray.push(obj);
                }
            })
        }
        // 合并数组
        unitCostSummaryArray.push(...unitCostCodeSummaryTotals);

        // 更新费用汇总
        ProjectDomain.getDomain(constructId).functionDataMap.get(FunctionTypeConstants.UNIT_COST_SUMMARY)
            .set(unitId + FunctionTypeConstants.SEPARATOR + UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL, unitCostSummaryArray);
        return unitCostSummaryArray;
    }

    groupByColumn(arr, column) {
        return arr.reduce((acc, obj) => {
            const key = obj[column];
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(obj);
            return acc;
        }, {});
    }

    mergeSystemSzgcGroup(groupedData, column1, column2) {
        // 检查是否存在 SYSTEM_SZGC 分组
        if (groupedData.hasOwnProperty(column1)) {
            const systemSzgcItems = groupedData[column1];
            // 检查是否存在 column2 分组
            if (groupedData.hasOwnProperty(column2)) {
                // 合并到现有 column2 分组
                groupedData[column2] = [...groupedData[column2], ...systemSzgcItems];
            } else {
                // 创建新的 column2 分组
                groupedData[column2] = [...systemSzgcItems];
            }
            // 删除原 column1 分组
            delete groupedData[column1];
        }
        return groupedData;
    }

    /**
     * 获取局部费用汇总
     * 计算当前工程专业的费用代码和更新费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async countCostCodePrice2(args) {
        let {constructId, singleId, unitId, constructMajorType, qfMajorType, deLists=[], csxmDeList=[], isCostSummary} = args;

        let unitProject = ProjectDomain.getDomain(constructId).getProjectById(unitId);
        if (ObjectUtils.isEmpty(qfMajorType)) {
            qfMajorType = unitProject.qfPartMajorType;
        }
        let costCodeSummarys = [];
        deLists.push(...csxmDeList);
        let partDeRowIds = isCostSummary === true ? deLists.filter(item => item.type !== '0' && item.isSelected === 1).map(item => item.deRowId)
            : deLists.filter(item => item.type !== '0' && item.isSelected === 1 && item.costFileCode === qfMajorType).map(item => item.deRowId)
        if (ObjectUtils.isEmpty(partDeRowIds)) {
            partDeRowIds = []
        }

        let businessMap = ProjectDomain.getDomain(constructId).functionDataMap;
        let budgetBookTypes, csxmTypes, independentCostTypes;
        // 1. 获取预算书定额的所有取费专业类型    //  && item.type === DeTypeConstants.DE_TYPE_DE
        let yssDes0 = ProjectDomain.getDomain(constructId).deDomain.getDeTree(item => item.unitId === unitId);
        let yssDes = yssDes0.filter(item => partDeRowIds.includes(item.deRowId));
        if (ObjectUtils.isNotEmpty(yssDes)) {
            budgetBookTypes = yssDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        }

        // 2. 获取措施项目定额的所有取费专业类型   //  && item.type === DeTypeConstants.DE_TYPE_DE    '03'
        let csxmDes0 = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && !['0', '01', '02'].includes(item.type));
        let csxmDes = csxmDes0.filter(item => partDeRowIds.includes(item.deRowId));
        if (ObjectUtils.isNotEmpty(csxmDes)) {
            csxmTypes = csxmDes.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costFileCode)).map(item => item.costFileCode);
        }

        // 3. 独立费的所有取费专业类型
        let independentCosts = businessMap.get(FunctionTypeConstants.UNIT_DLF_KEY)
            .get(await this.service.gongLiaoJiProject.gljIndependentCostsService.getDataMapKey(unitId))?.filter(item => item.isRcj !== true)
        if (ObjectUtils.isNotEmpty(independentCosts)) {
            independentCostTypes = independentCosts.filter(budgetBook => ObjectUtils.isNotEmpty(budgetBook.costMajorCode)).map(item => item.costMajorCode);
        }

        // 4. 获取该单位所有的专业：专业名称数组判空且合并去重，没有数据则返回
        let nonEmptyLists = [budgetBookTypes, csxmTypes, independentCostTypes].filter(arr => ObjectUtils.isNotEmpty(arr));
        let constructMajorTypeSet = [...new Set(nonEmptyLists.flat())];


        // 获取取费表列表数据  根据取费表中的专业顺序进行排序  todo
        let qfbList = await this.service.gongLiaoJiProject.gljFreeRateService.getUnitQfbList({
            constructId:constructId,
            singleId:singleId,
            unitId:unitId
        })
        qfbList = qfbList.sort((a, b) => a.sortNo - b.sortNo);
        constructMajorTypeSet = [...new Set(qfbList.map(item => item.qfCode))];

        if (ObjectUtils.isEmpty(constructMajorTypeSet)) {
            return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                qfMajorType: qfMajorType
            });
        }

        // 5. 获取措施项目中的清单，给措施项目的定额添加计价方式
        let csxmQds = ProjectDomain.getDomain(constructId).csxmDomain.getDeTree(item => item.unitId === unitId && item.type === DeTypeConstants.DE_TYPE_DELIST);
        let csxmQdMap = new Map(csxmQds.map(item => [item.sequenceNbr, item]));  // 获取措施项目中的清单 Map

        let csxmAllDes = [];
        let csxmAllDeList = ConvertUtil.deepCopy(csxmDes);
        let csxmLength = csxmAllDeList.length;
        for (let i = 0; i < csxmLength; i++) {
            let de = csxmAllDeList[i];
            if (csxmQdMap.has(de.parentId)) {
                let qd = csxmQdMap.get(de.parentId);
                if (qd.sequenceNbr === de.parentId) {
                    de.pricingMethod = qd.pricingMethod;
                    csxmAllDes.push(de);
                }
            }
        }
        // 6. 定额分组
        const yssDeMap = new Map(yssDes.map(item => [item.sequenceNbr, item]));
        const zjcsxmDes = csxmAllDes.filter(item => item.pricingMethod === 2);
        const zjcsxmDeMap = new Map(csxmAllDes.filter(item => item.pricingMethod === 2).map(item => [item.sequenceNbr, item]));
        const fzjcsxmQdMap = new Map(csxmQds.filter(item => item.pricingMethod === 1 || item.pricingMethod === 3).map(item => [item.sequenceNbr, item]));
        const fzjcsxmQds = csxmQds.filter(item => item.pricingMethod === 1);

        // 7. 获取当前单位所有的人材机
        let rcjKey = WildcardMap.generateKey(unitId) + WildcardMap.WILDCARD;
        let allRcjList0 = ProjectDomain.getDomain(constructId).resourceDomain.getResource(rcjKey);
        let allRcjList = allRcjList0.filter(item => partDeRowIds.includes(item.deRowId));

        let yssAllRcjs = [], zjcsxmAllRcjs = [];
        for (let i = 0; i < allRcjList.length; i++) {
            let rcj = allRcjList[i];
            // 7.1 获取预算书人材机
            if (yssDeMap.has(rcj.deRowId)) {
                let yssDe = yssDeMap.get(rcj.deRowId);
                if (ObjectUtils.isNotEmpty(yssDe)) {
                    rcj.costFileCode = yssDe.costFileCode;
                    yssAllRcjs.push(rcj);
                }
            }
            // 7.2 获取措施项目人材机
            if (zjcsxmDeMap.has(rcj.deRowId)) {
                let csxmDe = zjcsxmDeMap.get(rcj.deRowId);
                if (ObjectUtils.isNotEmpty(csxmDe)) {
                    rcj.costFileCode = csxmDe.costFileCode;
                    zjcsxmAllRcjs.push(rcj);
                }
            }
        }

        // 7.3 遍历map，获取非组价措施项目人材机分摊
        let fzjcsxmAllRcjs = [];
        let objMap = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST);
        if (ObjectUtils.isEmpty(objMap)) {
            objMap = new Map();
            businessMap.set(FunctionTypeConstants.RCJ_SHARE_COST, objMap);
        }
        let shareCostUnit = objMap.get(unitId);
        // let shareCostUnit = businessMap.get(FunctionTypeConstants.RCJ_SHARE_COST).get(unitId);
        if (ObjectUtils.isNotEmpty(shareCostUnit)) {
            for (const [key, value] of shareCostUnit.entries()) {
                console.log(key, value);
                if (fzjcsxmQdMap.has(key)) {
                    let de = fzjcsxmQdMap.get(key);
                    for (let i = 0; i < value.length; i++) {
                        let rcj = value[i];
                        let obj = {};
                        obj.sequenceNbr = Snowflake.nextId();
                        obj.deId = key;
                        obj.name = rcj.name;
                        obj.ratio = rcj.ratio;
                        obj.value = rcj.value;
                        obj.costFileCode = de.costFileCode;
                        fzjcsxmAllRcjs.push(obj);
                    }
                }
            }
        }

        let yssDeList = [], yssRcjList = [], zjcsxmDeList = [], zjcsxmRcjList = [], fzjcsxmRcjList = [],
            fzjcsxmQdList = [], rcjList = [],
            independentCostList, majorName, unitCostCodePrices;
        // 局部汇总-单专业汇总
        if (unitProject.isPartSingleMajorFlag) {

            console.log("单专业取费。。。。");
            yssDeList = yssDes;
            yssRcjList = yssAllRcjs;
            zjcsxmDeList = zjcsxmDes;
            zjcsxmRcjList = zjcsxmAllRcjs;
            fzjcsxmRcjList = fzjcsxmAllRcjs;
            rcjList = allRcjList;
            fzjcsxmQdList = fzjcsxmQds;
            if (ObjectUtils.isNotEmpty(independentCosts)) {
                independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.levelType === 2);
            }

            majorName = UnitConstructMajorTypeConstants.UNIT_CONSTRUCT_MAJOR_TYPE_TOTAL;
            // 多专业汇总
        } else {
            console.log("当前单位取费主专业====>" + unitProject.qfPartMajorType);
            // 处理主专业与随主专业合并
            let newConstructMajorTypeSet = new Set(constructMajorTypeSet.map(item => item === CommonConstants.SYSTEM_SZGC ? unitProject.qfPartMajorType : item));
            let constructMajorTypeList = [...newConstructMajorTypeSet];

            if (!constructMajorTypeList.includes(qfMajorType)) {
                // 对专业类型进行初始化费用汇总  注意：此时费用汇总汇总的费率跟取费表中的费率是保持一致的
                return await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary2({
                    constructId: constructId,
                    singleId: singleId,
                    unitId: unitId,
                    qfMajorType: qfMajorType
                })
            }

            if (constructMajorTypeList.length === 1) {
                console.log("多专业汇总，单专业时。。。。" + constructMajorTypeList);

                yssDeList = yssDes;
                yssRcjList = yssAllRcjs;
                zjcsxmDeList = zjcsxmDes;
                zjcsxmRcjList = zjcsxmAllRcjs;
                fzjcsxmRcjList = fzjcsxmAllRcjs;
                rcjList = allRcjList;
                fzjcsxmQdList = fzjcsxmQds;
                if (ObjectUtils.isNotEmpty(independentCosts)) {
                    independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.levelType === 2);
                }

                // 处理主专业与随主专业合并
                if (qfMajorType === CommonConstants.SYSTEM_SZGC) {
                    qfMajorType = unitProject.qfPartMajorType;
                }
                // 为空是，是多专业汇总按钮进来的
                if (ObjectUtils.isEmpty(qfMajorType) || !constructMajorTypeList.includes(qfMajorType)) {
                    majorName = constructMajorTypeList[0];
                    // 进行多专业汇总之后进来的
                } else {
                    majorName = qfMajorType;
                }
            } else {
                majorName = qfMajorType;
                console.log("多专业汇总，多专业时。。。。" + constructMajorTypeList); // todo 加随主
                // 预算书定额分类
                if (ObjectUtils.isNotEmpty(yssDes)) {
                    yssDeList = yssDes.filter(item => item.costFileCode === majorName);
                }
                if (majorName === unitProject.qfPartMajorType) {
                    yssDeList = yssDeList.concat(yssDes.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                }
                // 预算书人材机分类
                if (ObjectUtils.isNotEmpty(yssAllRcjs)) {
                    yssRcjList = yssAllRcjs.filter(item => item.costFileCode === majorName);
                }
                if (majorName === unitProject.qfPartMajorType) {
                    yssRcjList = yssRcjList.concat(yssAllRcjs.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                }

                // 组价措施项目定额分类
                if (ObjectUtils.isNotEmpty(zjcsxmDes)) {
                    zjcsxmDeList = zjcsxmDes.filter(item => item.costFileCode === majorName);
                }
                if (majorName === unitProject.qfPartMajorType) {
                    zjcsxmDeList = zjcsxmDeList.concat(zjcsxmDes.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                }
                // 组价措施项目人材机分类
                if (ObjectUtils.isNotEmpty(zjcsxmAllRcjs)) {
                    zjcsxmRcjList = zjcsxmAllRcjs.filter(item => item.costFileCode === majorName);
                }
                if (majorName === unitProject.qfPartMajorType) {
                    zjcsxmRcjList = zjcsxmRcjList.concat(zjcsxmAllRcjs.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                }

                // 非组价措施项目人材机分类
                if (ObjectUtils.isNotEmpty(fzjcsxmAllRcjs)) {
                    fzjcsxmRcjList = fzjcsxmAllRcjs.filter(item => item.costFileCode === majorName);
                }
                if (majorName === unitProject.qfPartMajorType) {
                    fzjcsxmRcjList = fzjcsxmRcjList.concat(fzjcsxmAllRcjs.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                }

                // 人材机分类
                if (ObjectUtils.isNotEmpty(allRcjList)) {
                    rcjList = allRcjList.filter(item => item.costFileCode === majorName);
                }
                if (majorName === unitProject.qfPartMajorType) {
                    rcjList = rcjList.concat(allRcjList.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                }

                // 非组价措施项目清单分类
                if (ObjectUtils.isNotEmpty(fzjcsxmQds)) {
                    fzjcsxmQdList = fzjcsxmQds.filter(item => item.costFileCode === majorName);
                }
                if (majorName === unitProject.qfPartMajorType) {
                    fzjcsxmQdList = fzjcsxmQdList.concat(fzjcsxmQds.filter(item => item.costFileCode === CommonConstants.SYSTEM_SZGC));
                }

                // 独立费分类
                if (ObjectUtils.isNotEmpty(independentCosts)) {
                    independentCostList = independentCosts.filter(item => ObjectUtils.isNotEmpty(item.costMajorCode) && item.costMajorCode === majorName && item.levelType === 2);
                    if (majorName === unitProject.qfPartMajorType) {
                        independentCostList = independentCostList.concat(independentCosts.filter(item => item.costMajorCode === CommonConstants.SYSTEM_SZGC && item.levelType === 2))
                    }
                }
            }
        }

        // 对专业类型进行初始化费用代码
        unitCostCodePrices = await this.service.gongLiaoJiProject.gljUnitCostCodePriceService.defaultUnitCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            qfMajorType: majorName
        });
        let unitCostSummaryArray = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.defaultUnitCostSummary2({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            qfMajorType: majorName
        });

        costCodeSummarys = ConvertUtil.deepCopy(unitCostSummaryArray);
        // 计算费用代码条目
        await this.countCostCodePriceItems(yssDeList, yssRcjList, zjcsxmDeList, zjcsxmRcjList, fzjcsxmRcjList, fzjcsxmQdList, rcjList, independentCostList, unitCostCodePrices, constructId, singleId, unitId);
        // 计算费用汇总
        await this.service.gongLiaoJiProject.gljUnitCostSummaryService.countUnitCostSummaryItem(constructId, unitId, unitCostCodePrices, costCodeSummarys);
        return costCodeSummarys.filter(item => item.isSummaryLine === false);
    }

    /**
     * 处理甲供数量
     * @param rcjs
     * @returns {any[]}
     */
    processDonorMaterialNumber(rcjs) {
        let result = [];
        //分组
        const grouped = rcjs.reduce((accumulator, currentValue) => {
            let tempcol = currentValue.materialCode
                .concat(currentValue.materialName)
                .concat(currentValue.kind)
                .concat(!ObjectUtils.isEmpty(currentValue.specification) ? currentValue.specification : '').concat(currentValue.unit).concat(currentValue.dePrice);
            // 将分组作为对象的 key，相同分组的项放入同一个数组
            (accumulator[tempcol] = accumulator[tempcol] || []).push(currentValue);
            return accumulator;
        }, {});
        //循环分组之后的人材机
        for (let group in grouped) {
            if (grouped.hasOwnProperty(group)) {
                let number = 0;
                grouped[group].forEach(item => {
                    number = NumberUtil.add(number, item.totalNumber);
                });
                grouped[group].forEach(item => {
                    if ((ObjectUtils.isEmpty(item.updateFalg) ? 0 : item.updateFalg) === 0 && item.ifDonorMaterial != RcjCommonConstants.DEFAULT_IFDONORMATERIAL) {
                        item.donorMaterialNumber = NumberUtil.numberScale(number, 4);
                    }
                    let constructProjectRcj = new GljConstructProjectRcj();
                    ConvertUtil.setDstBySrc(item, constructProjectRcj);
                    result.push(constructProjectRcj);
                });
            }
        }
        return result;
    }


    /**
     * 计算费用汇总条例
     * @param yssDeList  预算书定额
     * @param yssRcjList  预算书人材机
     * @param zjcsxmDeList  组价措施定额
     * @param zjcsxmRcjList  组价措施人材机
     * @param fzjcsxmRcjList  非组价措施项目人材机
     * @param fzjcsxmQdList  非组价措施项目清单
     * @param rcjList  人材机
     * @param independentCostList  独立费
     * @param unitCostCodePrices  费用代码模板
     * @param constructId
     * @param singleId
     * @param unitId
     */
    async countCostCodePriceItems(yssDeList, yssRcjList, zjcsxmDeList, zjcsxmRcjList, fzjcsxmRcjList, fzjcsxmQdList, rcjList, independentCostList, unitCostCodePrices, constructId, singleId, unitId) {

        // let yssRgRcj, yssClRcj, yssJxRcj, yssZcRcj, yssSbRcj, yssGrRcj;
        // if (ObjectUtils.isNotEmpty(yssRcjList)) {
        //     // 预算书人工
        //     yssRgRcj = yssRcjList.filter(item => item.kind == 1);
        //     // 预算书材料
        //     yssClRcj = yssRcjList.filter(item => (item.kind == 2 || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10));
        //     // 预算书机械
        //     yssJxRcj = yssRcjList.filter(item => item.kind == 3);
        //     // 预算书主材
        //     yssZcRcj = yssRcjList.filter(item => item.kind == 5);
        //     // 预算书设备
        //     yssSbRcj = yssRcjList.filter(item => item.kind == 4);
        //     // 预算书工日人工
        //     yssGrRcj = yssRcjList.filter(item => item.kind == 1 && item.unit == '工日');
        // }

        // let zjcsxmRgRcj, zjcsxmClRcj, zjcsxmJxRcj, zjcsxmZcRcj, zjcsxmSbRcj, zjcsxmGrRcj;
        // if (ObjectUtils.isNotEmpty(zjcsxmRcjList)) {
        //     // 组价措施人工
        //     zjcsxmRgRcj = zjcsxmRcjList.filter(item => item.kind == 1);
        //     // 组价措施材料
        //     zjcsxmClRcj = zjcsxmRcjList.filter(item => (item.kind == 2 || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10));
        //     // 组价措施机械
        //     zjcsxmJxRcj = zjcsxmRcjList.filter(item => item.kind == 3);
        //     // 组价措施主材
        //     zjcsxmZcRcj = zjcsxmRcjList.filter(item => item.kind == 5);
        //     // 组价措施设备
        //     zjcsxmSbRcj = zjcsxmRcjList.filter(item => item.kind == 4);
        //     // 组价措施工日人工
        //     zjcsxmGrRcj = zjcsxmRcjList.filter(item => item.kind == 1 && item.unit == '工日');
        // }

        let fzjcsxmRgRcj, fzjcsxmClRcj, fzjcsxmJxRcj;
        if (ObjectUtils.isNotEmpty(fzjcsxmRcjList)) {
            // 非组价措施人工
            fzjcsxmRgRcj = fzjcsxmRcjList.filter(item => item.name === "人工费");
            // 非组价措施材料
            fzjcsxmClRcj = fzjcsxmRcjList.filter(item => item.name === "材料费");
            // 非组价措施机械
            fzjcsxmJxRcj = fzjcsxmRcjList.filter(item => item.name === "机械费");
        }

        // 人工人材机
        let rgRcj = rcjList.filter(item => item.kind == 1);
        // 材料人材机
        let clRcj = rcjList.filter(item => (item.kind == 2 || item.kind == 6 || item.kind == 7 || item.kind == 8 || item.kind == 9 || item.kind == 10));
        // 机械人材机
        let jxRcj = rcjList.filter(item => item.kind == 3);
        // 主材人材机
        let zcRcj = rcjList.filter(item => item.kind == 5);
        // 设备人材机
        let sbRcj = rcjList.filter(item => item.kind == 4);


        //甲供人工人材机
        let jgRgRcj
        if (ObjectUtils.isNotEmpty(rgRcj)) {
            let jgRcjTemp = rgRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_1);
            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgRgRcj = Array.from(map.values());
            // jgRgRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }

        //甲供材料人材机
        let jgClRcj
        if (ObjectUtils.isNotEmpty(clRcj)) {
            let jgRcjTemp = clRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_1);
            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgClRcj = Array.from(map.values());
            // jgClRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }

        //甲供机械人材机
        let jgJxRcj
        if (ObjectUtils.isNotEmpty(jxRcj)) {
            let jgRcjTemp = jxRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_1);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgJxRcj = Array.from(map.values());
            // jgJxRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }

        //甲供主材人材机
        let jgZcRcj
        if (ObjectUtils.isNotEmpty(zcRcj)) {
            let jgRcjTemp = zcRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_1);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgZcRcj = Array.from(map.values());
            // jgZcRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }

        //甲供设备人材机
        let jgSbRcj
        if (ObjectUtils.isNotEmpty(sbRcj)) {
            let jgRcjTemp = sbRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_1);

            // 人材机按照编码去重
            const map = new Map();
            jgRcjTemp = this.processDonorMaterialNumber(jgRcjTemp);
            jgRcjTemp.forEach(item => map.set(item.materialCode, item));
            jgSbRcj = Array.from(map.values());
            // jgSbRcj = ObjectUtils.isNotEmpty(jgRcjTemp) ? new Array(jgRcjTemp[0]) : jgRcjTemp;
        }

        //甲定人工、材料、机械、主材、设备人材机
        let jdRgRcj, jdClRcj, jdJxRcj, jdZcRcj, jdSbRcj;
        if (ObjectUtils.isNotEmpty(rgRcj)) {
            jdRgRcj = rgRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_2);

            // // 人材机按照编码去重
            // const map = new Map();
            // jdRcjTemp = this.processDonorMaterialNumber(jdRcjTemp);
            // jdRcjTemp.forEach(item => map.set(item.materialCode, item));
            // jdRgRcj = Array.from(map.values());
        }

        if (ObjectUtils.isNotEmpty(clRcj)) {
            jdClRcj = clRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_2);

            // // 人材机按照编码去重
            // const map = new Map();
            // jdRcjTemp = this.processDonorMaterialNumber(jdRcjTemp);
            // jdRcjTemp.forEach(item => map.set(item.materialCode, item));
            // jdClRcj = Array.from(map.values());
        }

        //甲定机械人材机

        if (ObjectUtils.isNotEmpty(jxRcj)) {
            jdJxRcj = jxRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_2);

            // // 人材机按照编码去重
            // const map = new Map();
            // jdRcjTemp = this.processDonorMaterialNumber(jdRcjTemp);
            // jdRcjTemp.forEach(item => map.set(item.materialCode, item));
            // jdJxRcj = Array.from(map.values());
        }

        if (ObjectUtils.isNotEmpty(zcRcj)) {
            jdZcRcj = zcRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_2);

            // 人材机按照编码去重
            // const map = new Map();
            // jdRcjTemp = this.processDonorMaterialNumber(jdRcjTemp);
            // jdRcjTemp.forEach(item => map.set(item.materialCode, item));
            // jdZcRcj = Array.from(map.values());
        }

        if (ObjectUtils.isNotEmpty(sbRcj)) {
            jdSbRcj = sbRcj.filter(item => item.ifDonorMaterial == ResourceKindConstants.IF_DONORMATERIAL_2);

            // 人材机按照编码去重
            // const map = new Map();
            // jdRcjTemp = this.processDonorMaterialNumber(jdRcjTemp);
            // jdRcjTemp.forEach(item => map.set(item.materialCode, item));
            // jdSbRcj = Array.from(map.values());
        }


        // 暂估材料人材机
        let zgClRcj = clRcj.filter(item => item.ifProvisionalEstimate === 1);
        // 暂估设备人材机
        let zgSbRcj = sbRcj.filter(item => item.ifProvisionalEstimate === 1);

        // 价格
        let yssZjf = 0, yssRgf = 0, yssClf = 0, yssJxf = 0, yssZcf = 0, yssSbf = 0, yssjjZjf = 0, yssjjRgf = 0,
            yssjjClf = 0, yssjjJxf = 0, csxmHj = 0, zjcsxmHj = 0, zjcsxmZjf = 0, zjcsxmRgf = 0, zjcsxmClf = 0,
            zjcsxmJxf = 0, zjcsxmZcf = 0, zjcsxmSbf = 0, zjcsxmjjZjf = 0, zjcsxmjjRgf = 0, zjcsxmjjClf = 0,
            zjcsxmjjJxf = 0, fzjcsxmHj = 0, fzjcsxmRgf = 0, fzjcsxmClf = 0, fzjcsxmJxf = 0;
        // 价差
        let rcjjc = 0, rgjc = 0, cljc = 0, jxjc = 0, zcjc = 0, sbjc = 0;
        //甲供
        let jgrgf = 0, jgclf = 0, jgjxf = 0, jgzcf = 0, jgsbf = 0;
        //甲定
        let jdrgf = 0, jdclf = 0, jdjxf = 0, jdzcf = 0, jdsbf = 0;
        // 暂估材料费、暂估设备费
        let zgclf = 0, zgsbf = 0;

        // 记取安全文明施工费的独立费
        let calculateAwfIndependentCostList;
        if (ObjectUtils.isNotEmpty(independentCostList)) {
            calculateAwfIndependentCostList = independentCostList.filter(item => item.isCalculateAwf === true);
        }
        // 水电费获取
        let waterElectricCost = await this.service.gongLiaoJiProject.gljWaterElectricCostMatchService.getWaterElectricCost({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        })

        // 建筑面积(m、㎡)
        let gcgm = ProjectDomain.getDomain(constructId).getProjectById(unitId).average;

        // 获取单位的计税方式   taxMethod  一般计税1   简易0
        let taxMethod = ProjectDomain.getDomain(constructId).getRoot().projectTaxCalculation.taxCalculationMethod;

        // 获取是否按照市场价组价 0 不按市场价， 1 按市场价，默认0
        let pricingMethod = ProjectDomain.getDomain(constructId).getRoot().pricingMethod;

        let simple = false, pricing = false;
        //判断计税
        if (taxMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            //简易计税
            simple = true;
        }
        //判断组价方式
        if (pricingMethod == 1) {
            // 市场价组价
            pricing = true;
        }

        // 小数点精度
        let precision = await this.service.gongLiaoJiProject.gljCommonService.getPrecisionSetting(constructId);
        let jzgm = precision.COST_ANALYSIS.jzgm;
        let totalNumber = precision.RCJ_COLLECT.totalNumber;
        let baseJournalPrice = precision.RCJ_COLLECT.baseJournalPrice;  // 不含税基期价
        let baseJournalTaxPrice = precision.RCJ_COLLECT.baseJournalTaxPrice; // 含税基期价
        let marketPrice = precision.RCJ_COLLECT.marketPrice;  // 不含税市场价
        let marketTaxPrice = precision.RCJ_COLLECT.marketTaxPrice;  // 含税市场价
        let je = precision.RCJ_COLLECT.je;  // 价格
        let donorMaterialPrice = precision.COST_CODE.donorMaterialPrice;  // 甲供价格

        if (ObjectUtils.isNotEmpty(unitCostCodePrices)) {
            for (let i = 0; i < unitCostCodePrices.length; i++) {
                let unitCostCodePrice = unitCostCodePrices[i];
                switch (unitCostCodePrice.code) {
                    case 'ZJF': {
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            if (pricing) {
                                // 直接费 ∑预算书下（定额层级的 人工费合价+材料费合价+机械费合价）
                                // ∑预算书下 定额层级的 人工费合价
                                yssRgf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                                }, 0));
                                // ∑预算书下 定额层级的 材料费合价
                                yssClf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                                }, 0));
                                // ∑预算书下 定额层级的 机械费合价
                                yssJxf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                                }, 0));
                            } else {
                                // ∑预算书下 定额层级的 人工费合价
                                yssRgf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                                }, 0));
                                // ∑预算书下 定额层级的 材料费合价
                                yssClf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                                }, 0));
                                // ∑预算书下 定额层级的 机械费合价
                                yssJxf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                                }, 0));
                            }
                        }
                        yssZjf = NumberUtil.addParams(yssRgf, yssClf, yssJxf);
                        if (!ObjectUtils.isEmpty(yssZjf)) {
                            unitCostCodePrice.price = yssZjf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RGF': {
                        // 预算书人工费 ∑预算书下 定额层级的 人工费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            if (pricing) {
                                yssRgf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                                }, 0));
                            } else {
                                yssRgf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = yssRgf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'CLF': {
                        // 预算书材料费（不含主材）合价  ∑预算书下 定额层级的 材料费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            if (pricing) {
                                yssClf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                                }, 0));
                            } else {
                                yssClf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = yssClf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JXF': {
                        // 预算书机械费  ∑预算书下 定额层级的 机械费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            if (pricing) {
                                yssJxf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                                }, 0));
                            } else {
                                yssJxf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = yssJxf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZCF': {
                        // 预算书主材费 ∑预算书下 定额层级的 主材费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            if (pricing) {
                                yssZcf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zTotalSum);
                                }, 0));
                            } else {
                                yssZcf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = yssZcf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'SBF': {
                        //  预算书设备费  ∑预算书下 定额层级的 设备费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            if (pricing) {
                                yssSbf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sTotalSum);
                                }, 0));
                            } else {
                                yssSbf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = yssSbf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'GR': {
                        // 工日合计  ∑（组价措施下 类型=“人工费”且单位=“工日”的数量（定额人工时为工程量）
                        let deGr, rcjGr;
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            let yssDesGr = yssDeList.filter(item => item.kind === 1 && item.unit === '工日');
                            deGr = yssDesGr.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0);
                        }
                        if (!ObjectUtils.isEmpty(yssRcjList)) {
                            let yssRcjsGr = yssRcjList.filter(item => item.kind === 1 && item.unit === '工日');
                            rcjGr = yssRcjsGr.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0);
                        }
                        if (!ObjectUtils.isEmpty(yssRcjList)) {
                            unitCostCodePrice.price = NumberUtil.numberScale(NumberUtil.addParams(deGr, rcjGr), totalNumber);
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZJFYSJ': {
                        // 基期价直接费  ∑预算书下（ 按基期价计算的定额层级的 人工费合价+材料费合价+机械费合价）
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            //  ∑预算书下 按基期价计算的 定额层级的 人工费合价
                            yssjjRgf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                            }, 0));
                            //  ∑预算书下 按基期价计算的 定额层级的 材料费合价
                            yssjjClf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                            }, 0));
                            //  ∑预算书下 按基期价计算的 定额层级的 机械费费合价
                            yssjjJxf = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                            }, 0));
                        }
                        yssjjZjf = NumberUtil.addParams(yssjjRgf, yssjjClf, yssjjJxf);
                        if (!ObjectUtils.isEmpty(yssjjZjf)) {
                            unitCostCodePrice.price = yssjjZjf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RGYSJ': {
                        // 基期价人工费  ∑预算书下 按基期价计算的 定额层级的 人工费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RGSCJ': {
                        // 市场价人工费  ∑预算书下 按市场价计算的 定额层级的 人工费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'CLYSJ': {
                        // 基期价材料费 ∑预算书下 按基期价计算的 定额层级的 材料费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JXYSJ': {
                        // 基期价机械费  ∑预算书下 按基期价计算的 定额层级的 机械费费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JXSCJ': {
                        // 市场价机械费  ∑预算书下 按市场价计算的 定额层级的 机械费合价
                        if (!ObjectUtils.isEmpty(yssDeList)) {
                            unitCostCodePrice.price = Number(yssDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'CSXMHJ': {
                        // 措施项目合计  ∑（非组价措施项目合价+组价措施项目合价）
                        // 非组价措项目合计 ∑直接用措施项合价计算，组价方式为“计算公式组价”方式的所有措施项的合价
                        if (!ObjectUtils.isEmpty(fzjcsxmQdList)) {
                            fzjcsxmHj = Number(fzjcsxmQdList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0));
                        }
                        // 组价措施项目合计 ∑组价措施下（人工费+材料费+机械费+主材费+设备费）合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                //  ∑组价措施下编辑区及人材机明细区类型为“人工”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“材料”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“机械”的合价 = “数量”*“市场价”
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“主材”的合价 = “数量”*“市场价”
                                zjcsxmZcf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“设备”的合价 = “数量”*“市场价”
                                zjcsxmSbf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sTotalSum);
                                }, 0));
                            } else {
                                // ∑组价措施下编辑区及人材机明细区类型为“人工”的合价 = “数量”*“市场价”
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“材料”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                                }, 0));

                                // ∑组价措施下编辑区及人材机明细区类型为“机械”的合价 = “数量”*“市场价”
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“主材”的合价 = “数量”*“市场价”
                                zjcsxmZcf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“设备”的合价 = “数量”*“市场价”
                                zjcsxmSbf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sdTotalSum);
                                }, 0));
                            }
                        }

                        csxmHj = NumberUtil.addParams(zjcsxmRgf, zjcsxmClf, zjcsxmJxf, zjcsxmZcf, zjcsxmSbf, fzjcsxmHj);
                        if (!ObjectUtils.isEmpty(csxmHj)) {
                            unitCostCodePrice.price = csxmHj;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZZCSF': {
                        // 非组价措项目合计 ∑直接用措施项合价计算，组价方式为“计算公式组价”方式的所有措施项的合价
                        if (!ObjectUtils.isEmpty(fzjcsxmQdList)) {
                            fzjcsxmHj = Number(fzjcsxmQdList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0));
                            unitCostCodePrice.price = fzjcsxmHj;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZZCS_RGF': {
                        // 非组价措项目人工费 ∑措施项目下编辑区非组价直接费分摊-人工费
                        if (!ObjectUtils.isEmpty(fzjcsxmRgRcj)) {
                            fzjcsxmRgf = Number(fzjcsxmRgRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.value);
                            }, 0));
                            unitCostCodePrice.price = fzjcsxmRgf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZZCS_CLF': {
                        // 非组价措项目材料费  ∑措施项目下编辑区非组价直接费分摊-材料费
                        if (!ObjectUtils.isEmpty(fzjcsxmClRcj)) {
                            fzjcsxmClf = Number(fzjcsxmClRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.value);
                            }, 0));
                            unitCostCodePrice.price = fzjcsxmClf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZZCS_JXF': {
                        // 非组价措项目机械费  ∑措施项目下编辑区非组价直接费分摊-机械费
                        if (!ObjectUtils.isEmpty(fzjcsxmJxRcj)) {
                            fzjcsxmJxf = Number(fzjcsxmJxRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.value);
                            }, 0));
                            unitCostCodePrice.price = fzjcsxmJxf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }

                    case 'JSCSF': {
                        // 组价措施项目合计 ∑组价措施下（人工费+材料费+机械费+主材费+设备费）合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                //  ∑组价措施下编辑区及人材机明细区类型为“人工”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“材料”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“机械”的合价 = “数量”*“市场价”
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“主材”的合价 = “数量”*“市场价”
                                zjcsxmZcf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zTotalSum);
                                }, 0));
                                //  ∑组价措施下编辑区及人材机明细区类型为“设备”的合价 = “数量”*“市场价”
                                zjcsxmSbf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sTotalSum);
                                }, 0));
                            } else {
                                // ∑组价措施下编辑区及人材机明细区类型为“人工”的合价 = “数量”*“市场价”
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“材料”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                                }, 0));

                                // ∑组价措施下编辑区及人材机明细区类型为“机械”的合价 = “数量”*“市场价”
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“主材”的合价 = “数量”*“市场价”
                                zjcsxmZcf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“设备”的合价 = “数量”*“市场价”
                                zjcsxmSbf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sdTotalSum);
                                }, 0));
                            }
                            zjcsxmHj = NumberUtil.addParams(zjcsxmRgf, zjcsxmClf, zjcsxmJxf, zjcsxmZcf, zjcsxmSbf);
                        }
                        if (!ObjectUtils.isEmpty(zjcsxmHj)) {
                            unitCostCodePrice.price = zjcsxmHj;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_ZJF': {
                        // 组价措项目直接费  ∑组价措施下（人工费+材料费+机械费）合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                // ∑组价措施下编辑区及人材机明细区类型为“人工”的合价 = “数量”*“市场价”
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“材料”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“机械”的合价 = “数量”*“市场价”
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                                }, 0));
                            } else {
                                // ∑组价措施下编辑区及人材机明细区类型为“人工”的合价 = “数量”*“市场价”
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“材料”（不含主材及设备）的合价 = “数量”*“市场价”
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                                }, 0));
                                // ∑组价措施下编辑区及人材机明细区类型为“机械”的合价 = “数量”*“市场价”
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                                }, 0));
                            }
                            zjcsxmZjf = NumberUtil.addParams(zjcsxmRgf, zjcsxmClf, zjcsxmJxf);
                        }
                        if (!ObjectUtils.isEmpty(zjcsxmZjf)) {
                            unitCostCodePrice.price = zjcsxmZjf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_RGF': {
                        // 组价措项目人工费  ∑组价措施下人工费合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                                }, 0));
                            } else {
                                zjcsxmRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = zjcsxmRgf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_CLF': {
                        // 组价措项目材料费  ∑组价措施下材料费（不含主材）合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cTotalSum);
                                }, 0));
                            } else {
                                zjcsxmClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = zjcsxmClf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_JXF': {
                        // 组价措项目机械费  ∑组价措施下机械费合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                                }, 0));
                            } else {
                                zjcsxmJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = zjcsxmJxf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_SBF': {
                        // 组价措项目设备费  ∑组价措施下设备费合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                zjcsxmSbf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sTotalSum);
                                }, 0));
                            } else {
                                zjcsxmSbf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.sdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = zjcsxmSbf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_ZCF': {
                        // 组价措项目主材费  ∑组价措施下主材费合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            if (pricing) {
                                zjcsxmZcf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zTotalSum);
                                }, 0));
                            } else {
                                zjcsxmZcf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, constructProjectRcj.zdTotalSum);
                                }, 0));
                            }
                            unitCostCodePrice.price = zjcsxmZcf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_GR': {
                        // 组价措项目工日合计  ∑（组价措施下 类型=“人工费”且单位=“工日”的数量（定额人工时为工程量）
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            let deGr, rcjGr;
                            let zjcsxmDesGr = zjcsxmDeList.filter(item => item.kind === 1 && item.unit === '工日');
                            deGr = zjcsxmDesGr.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0);

                            let zjcsxmRcjsGr = zjcsxmRcjList.filter(item => item.kind === 1 && item.unit === '工日');
                            rcjGr = zjcsxmRcjsGr.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.totalNumber);
                            }, 0);
                            unitCostCodePrice.price = NumberUtil.numberScale(NumberUtil.addParams(deGr, rcjGr), totalNumber);
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_ZJFYSJ': {
                        // 组价措施项目基期价直接费 ∑组价措施下（人工费+材料费+机械费）基期价合价
                        // ∑预算书下编辑区及人材机明细区类型为“人工”（不含主材及设备）的合价 = “数量”*“市场价”
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            zjcsxmjjRgf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                            }, 0));
                            // ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的合价 = “数量”*“市场价”
                            zjcsxmjjClf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                            }, 0));
                            // ∑预算书下编辑区及人材机明细区类型为“机械”的合价 = “数量”*“市场价”
                            zjcsxmjjJxf = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                            }, 0));
                        }
                        zjcsxmjjZjf = NumberUtil.addParams(zjcsxmjjRgf, zjcsxmjjClf, zjcsxmjjJxf);
                        if (!ObjectUtils.isEmpty(zjcsxmjjZjf)) {
                            unitCostCodePrice.price = zjcsxmjjZjf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_RGYSJ': {
                        // 组价措施项目基期价人工费  ∑组价措施下人工费基期价合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            unitCostCodePrice.price = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_RGSCJ': {
                        // 组价措施项目市场价人工费  ∑组价措施下人工费市场价合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            unitCostCodePrice.price = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.rTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_CLYSJ': {
                        // 组价措施项目基期价材料费	∑组价措施下材料费基期价合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            unitCostCodePrice.price = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.cdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_JXYSJ': {
                        // 组价措施项目基期价机械费	∑组价措施下机械费基期价合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            unitCostCodePrice.price = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jdTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JSCS_JXSCJ': {
                        // 组价措施项目市场价机械费	∑组价措施下机械费市场价合价
                        if (!ObjectUtils.isEmpty(zjcsxmDeList)) {
                            unitCostCodePrice.price = Number(zjcsxmDeList.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.jTotalSum);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }

                    case 'DLF': {
                        // 独立费	∑独立费下费用合计
                        if (ObjectUtils.isNotEmpty(independentCostList)) {
                            unitCostCodePrice.price = Number(independentCostList.reduce((accumulator, independentCost) => {
                                return NumberUtil.add(accumulator, independentCost.totalPrice);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'DLF_AQWMSGF': {
                        // 记取安全文明施工费的独立费   ∑独立费下勾选为记取安文费的独立费合计金额
                        if (!ObjectUtils.isEmpty(calculateAwfIndependentCostList)) {
                            unitCostCodePrice.price = Number(calculateAwfIndependentCostList.reduce((accumulator, independentCost) => {
                                return NumberUtil.add(accumulator, independentCost.totalPrice);
                            }, 0));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JKTZ': {
                        // 价款调整   人材机价差RCJJC + 记取安全文明施工费的独立费DLF_AQWMSGF
                        if (simple) {
                            // 人材机价差 = 人工价差+材料价差+机械价差+主材价差  ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“市场价”- ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(rgRcj)) {
                                rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(clRcj)) {
                                cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(jxRcj)) {
                                jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“主材”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(zcRcj)) {
                                zcjc = Number(zcRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                        } else {
                            // 人材机价差 = 人工价差+材料价差+机械价差+主材价差  ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“市场价”- ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(rgRcj)) {
                                rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(clRcj)) {
                                cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(jxRcj)) {
                                jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“主材”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(zcRcj)) {
                                zcjc = Number(zcRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                        }

                        let jqDlf = 0;
                        if (!ObjectUtils.isEmpty(calculateAwfIndependentCostList)) {
                            jqDlf = Number(calculateAwfIndependentCostList.reduce((accumulator, independentCost) => {
                                return NumberUtil.add(accumulator, independentCost.totalPrice);
                            }, 0));
                        }
                        unitCostCodePrice.price = NumberUtil.addParams(rgjc, cljc, jxjc, zcjc, jqDlf);
                        break;
                    }

                    case 'RCJJC': {
                        if (simple) {
                            // 人材机价差 = 人工价差+材料价差+机械价差+主材价差  ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“市场价”- ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(rgRcj)) {
                                rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(clRcj)) {
                                cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(jxRcj)) {
                                jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“主材”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(zcRcj)) {
                                zcjc = Number(zcRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                        } else {
                            // 人材机价差 = 人工价差+材料价差+机械价差+主材价差  ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“市场价”- ∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(rgRcj)) {
                                rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“材料”（不含主材及设备）的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(clRcj)) {
                                cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(jxRcj)) {
                                jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            // ∑预算书下编辑区及人材机明细区类型为“主材”的“数量”*“市场价” - ∑预算书下编辑区及人材机明细区类型为“机械”的“数量”*“定额价”
                            if (!ObjectUtils.isEmpty(zcRcj)) {
                                zcjc = Number(zcRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                        }
                        rcjjc = NumberUtil.addParams(rgjc, cljc, jxjc, zcjc);
                        if (!ObjectUtils.isEmpty(rcjjc)) {
                            unitCostCodePrice.price = rcjjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'RGJC': {
                        // 人工价差  ∑预算书下编辑区及人材机明细区类型=“人工”的“数量”*“市场价”（编辑区为“工程量”*“单价”）-∑预算书下编辑区及人材机明细区类型为“人工”的“数量”*“定额价”
                        if (!ObjectUtils.isEmpty(rgRcj)) {
                            if (simple) {
                                rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            } else {
                                rgjc = Number(rgRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            unitCostCodePrice.price = rgjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'CLJC': {
                        // 材料价差  ∑预算书下编辑区及人材机明细区类型为="材料、商砼、砼、浆、商浆、配比"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(clRcj)) {
                            if (simple) {
                                cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            } else {
                                cljc = Number(clRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            unitCostCodePrice.price = cljc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JXJC': {
                        // 机械价差	∑预算书下编辑区及人材机明细区类型为="机械"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(jxRcj)) {
                            if (simple) {
                                jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            } else {
                                jxjc = Number(jxRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            unitCostCodePrice.price = jxjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZCJC': {
                        // 主材价差	∑预算书下编辑区及人材机明细区类型为="主材"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(zcRcj)) {
                            if (simple) {
                                zcjc = Number(zcRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            } else {
                                zcjc = Number(zcRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            unitCostCodePrice.price = zcjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'SBJC': {
                        // 设备价差	∑预算书下编辑区及人材机明细区类型为="设备"的"市场价"与"定额价"价差*数量
                        if (!ObjectUtils.isEmpty(sbRcj)) {
                            if (simple) {
                                sbjc = Number(sbRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            } else {
                                sbjc = Number(sbRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je);
                                }, 0));
                            }
                            unitCostCodePrice.price = sbjc;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGRGF': {
                        // 甲供人工费	  ∑单位工程人材机汇总下类型="人工"且为“甲供的市场价*甲供数量
                        if (!ObjectUtils.isEmpty(jgRgRcj)) {
                            if (simple && pricing) {
                                jgrgf = Number(jgRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jgrgf = Number(jgRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jgrgf = Number(jgRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jgrgf = Number(jgRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jgrgf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGCLF': {
                        // 甲供材料费  ∑单位工程人材机汇总下类型="材料、商砼、砼、浆、商浆、配比"且为甲供的市场价*甲供数量
                        if (!ObjectUtils.isEmpty(jgClRcj)) {
                            if (simple && pricing) {
                                jgclf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jgclf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jgclf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jgclf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jgclf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGJXF': {
                        // 甲供机械费  ∑单位工程人材机类型=“机械”且为甲供的市场价*甲供数量
                        if (!ObjectUtils.isEmpty(jgJxRcj)) {
                            if (simple && pricing) {
                                jgjxf = Number(jgJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jgjxf = Number(jgJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jgjxf = Number(jgJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jgjxf = Number(jgJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.donorMaterialPrice, donorMaterialPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jgjxf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGZCF': {
                        // 甲供主材费  ∑单位工程人材机类型=“主材”且为甲供的市场价*甲供数量
                        if (!ObjectUtils.isEmpty(jgZcRcj)) {
                            if (simple && pricing) {
                                jgzcf = Number(jgZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jgzcf = Number(jgZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jgzcf = Number(jgZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jgzcf = Number(jgZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jgzcf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGSBF': {
                        // 甲供设备费	  ∑单位工程人材机类型=“设备”且为甲供的市场价*甲供数量
                        if (!ObjectUtils.isEmpty(jgSbRcj)) {
                            if (simple && pricing) {
                                jgsbf = Number(jgSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jgsbf = Number(jgSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jgsbf = Number(jgSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jgsbf = Number(jgSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jgsbf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }

                    case 'JDRGF': {
                        // 甲定人工费   ∑单位工程人材机类型=“人工”且为“甲定乙供”的市场价*数量
                        if (!ObjectUtils.isEmpty(jdRgRcj)) {
                            if (simple && pricing) {
                                jdrgf = Number(jdRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jdrgf = Number(jdRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jdrgf = Number(jdRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jdrgf = Number(jdRgRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jdrgf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JDCLF': {
                        // 甲定材料费  ∑单位工程人材机类型=“材料、商砼、砼、浆、商浆、配比”且为“甲定乙供”的市场价*数量
                        if (!ObjectUtils.isEmpty(jdClRcj)) {
                            if (simple && pricing) {
                                jdclf = Number(jdClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jdclf = Number(jdClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jdclf = Number(jdClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jdclf = Number(jdClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jdclf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JDJXF': {
                        // 甲定机械费  ∑单位工程人材机类型=“机械”且为“甲定乙供”的市场价*数量
                        if (!ObjectUtils.isEmpty(jdJxRcj)) {
                            if (simple && pricing) {
                                jdjxf = Number(jdJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jdjxf = Number(jdJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jdjxf = Number(jdJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jdjxf = Number(jdJxRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jdjxf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JDZCF': {
                        // 甲定主材费   ∑单位工程人材机类型=“主材”且为“甲定乙供”的市场价*数量
                        if (!ObjectUtils.isEmpty(jdZcRcj)) {
                            if (simple && pricing) {
                                jdzcf = Number(jdZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jdzcf = Number(jdZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jdzcf = Number(jdZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jdzcf = Number(jdZcRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jdzcf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JDSBF': {
                        // 甲定设备费  ∑单位工程人材机类型=“设备”且为“甲定乙供”的市场价*数量
                        if (!ObjectUtils.isEmpty(jdSbRcj)) {
                            if (simple && pricing) {
                                jdsbf = Number(jdSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (simple && !pricing) {
                                jdsbf = Number(jdSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && pricing) {
                                jdsbf = Number(jdSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            if (!simple && !pricing) {
                                jdsbf = Number(jdSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jdsbf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZGJCLHJ': {
                        // 暂估材料费 ∑单位工程人材机汇总下类型="材料"且为“暂估”的市场价*数量
                        if (!ObjectUtils.isEmpty(zgClRcj)) {
                            if (simple) {
                                zgclf = Number(zgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            } else {
                                zgclf = Number(zgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = zgclf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'ZGJSBHJ': {
                        // 暂估设备费 ∑单位工程人材机汇总下类型="设备"且为“暂估”的市场价*数量
                        if (!ObjectUtils.isEmpty(zgSbRcj)) {
                            if (simple) {
                                zgsbf = Number(zgSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            } else {
                                zgsbf = Number(zgSbRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.totalNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = zgsbf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }

                    case 'JGCLYSJHJ': {
                        // 甲供材料基期价合计  ∑单位工程人材机汇总下类型="材料、商砼、砼、浆、商浆、配比"且为“甲供”的基期价*甲供数量
                        if (!ObjectUtils.isEmpty(jgClRcj)) {
                            let jgcljjf;
                            if (simple) {
                                jgcljjf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            } else {
                                jgcljjf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jgcljjf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGCLSCJHJ': {
                        // 甲供材料市场价合计  ∑单位工程人材机汇总下类型="材料、商砼、砼、浆、商浆、配比"且为“甲供”的市场价*甲供数量
                        if (!ObjectUtils.isEmpty(jgClRcj)) {
                            let jgclsjf;
                            if (simple) {
                                jgclsjf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            } else {
                                jgclsjf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    return NumberUtil.add(accumulator, NumberUtil.numberScale(NumberUtil.multiply(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je));
                                }, 0));
                            }
                            unitCostCodePrice.price = jgclsjf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'JGCLJCHJ': {
                        // 甲供材料价差合计  ∑单位工程人材机汇总下类型="材料、商砼、砼、浆、商浆、配比"且为“甲供”市场价-基期价的价差*甲供数量
                        if (!ObjectUtils.isEmpty(jgClRcj)) {
                            let jgcljcf;
                            if (simple) {
                                jgcljcf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketTaxPrice, marketTaxPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalTaxPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je);
                                }, 0));
                            } else {
                                jgcljcf = Number(jgClRcj.reduce((accumulator, constructProjectRcj) => {
                                    let jc = NumberUtil.subtract(NumberUtil.numberScale(constructProjectRcj.marketPrice, marketPrice), NumberUtil.numberScale(constructProjectRcj.baseJournalPrice, baseJournalTaxPrice));
                                    return accumulator + NumberUtil.numberScale(NumberUtil.multiply(jc, NumberUtil.numberScale(constructProjectRcj.donorMaterialNumber, totalNumber)), je);
                                }, 0));
                            }
                            unitCostCodePrice.price = jgcljcf;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'GCSF': {
                        // 工程水费
                        if (!ObjectUtils.isEmpty(waterElectricCost)) {
                            unitCostCodePrice.price = waterElectricCost.GCSF;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'GCDF': {
                        // 工程电费
                        if (!ObjectUtils.isEmpty(waterElectricCost)) {
                            unitCostCodePrice.price = waterElectricCost.GCDF;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'GCSDF': {
                        // 工程水电费
                        if (!ObjectUtils.isEmpty(waterElectricCost)) {
                            unitCostCodePrice.price = waterElectricCost.GCSDF;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                    case 'GCGM': {
                        // 建筑规模	单位工程-项目概况（工程特征）-工程规模
                        if (!ObjectUtils.isEmpty(gcgm)) {
                            unitCostCodePrice.price = NumberUtil.numberScale(Number(gcgm), jzgm);
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        break;
                    }
                }
            }
        }
    }

}

GljUnitCostCodePriceService.toString = () => '[class GljUnitCostCodePriceService]';
module.exports = GljUnitCostCodePriceService;