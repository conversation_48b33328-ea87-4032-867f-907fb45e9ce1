<template>
  <a-dropdown :trigger="['click']">
    <!-- @click="visible = true" -->
    <div :class="props.type==='zkt' ? 'user' : 'childuser'">
      <span><icon-font
          class="icon"
          type="icon-qiehuanshenfen"
          style="color: white"
          v-if="props.loginType === 'inlineLogin' && props.infoVisible"
        />{{ props.userInfo.showName || '未登录' }}</span>
      <div class="photo">
        <img
          :src="
            props.loginType !== 'offlineLogin'
              ? props.userInfo.showName
                ? props.userInfo.logo
                : getUrl('newCsProject/user-bg.png')
              : getUrl('outline.png')
          "
          alt="登录用户头像"
        />
        <img
          :src="getUrl('huiyuanvip.png')"
          v-if="props.userInfo.isvip"
        />
      </div>
    </div>
    <template #overlay>
      <a-menu
        class="agencyList"
        v-if="props.loginType === 'inlineLogin' && props.infoVisible"
      >
        <a-menu-item style="
            border-bottom: 1px solid #eeeeee;
            background: rgba(244, 243, 244, 0.39);
            color: #707070;
            position: relative;
          ">
          <img
            :src="props.userInfo.logo"
            :alt="props.userInfo.showName"
            style="max-width: 40px"
          />
          <img
            :src="getUrl('huiyuanvip.png')"
            class="vip"
            v-if="props.userInfo.isvip"
          />

          <span
            v-if="props.userInfo.type === 'enterpriseAgency'"
            class="userDetail"
          >
            {{ props.userInfo.userName }}
            <span class="enterInfo">
              <img
                :src="
                  props.userInfo.type === 'enterpriseAgency'
                    ? getUrl('entAge.png')
                    : getUrl('perAge.png')
                "
                alt=""
              />
              {{ props.userInfo.showName }}
            </span>
          </span>
          <span
            v-else
            class="userDetail"
            style="margin-bottom: -6px"
          >
            <img
              :src="
                props.userInfo.type === 'enterpriseAgency'
                  ? getUrl('entAge.png')
                  : getUrl('perAge.png')
              "
              alt=""
            />
            {{ props.userInfo.showName }}
          </span>
          <span
            class="roleType"
            v-if="props.userInfo.roleType || props.userInfo.roleName"
          >
            {{
              props.userInfo.roleType === 0
                ? '普通角色'
                : props.userInfo.roleType === 1
                ? '超级管理员'
                : props.userInfo.roleName
            }}
          </span>
          <icon-font
            class="icon"
            type="icon-tuichudenglu"
            style="
              position: absolute;
              top: 17px;
              right: 10px;
              font-size: 14px;
              color: #409eff;
            "
            @click="emit('layoutOrChange', 'layout')"
          />
        </a-menu-item>
        <a-menu-item
          v-for="(info, index) in props.infoList"
          :key="index"
          @click="emit('layoutOrChange', 'changeUserInfo', info)"
        >
          <span class="userDetail">
            {{ info.showName }}
          </span>
          <!-- 总控台进行切换身份 -->
          <icon-font
            v-if="props.type==='zkt'"
            class="icon isShow"
            type="icon-qiehuanshenfen"
          />
        </a-menu-item>
        <a-menu-item v-if="props.infoList.length === 0">
          暂无其他可切换身份
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>
<script setup>
import { ref, reactive, computed } from 'vue';
import { propTypes } from '@/utils/propTypes';
import { getUrl } from '@/utils/index';

const props = defineProps({
  loginType: propTypes.string.def(''), //是否是主窗口
  infoVisible: propTypes.bool.def(true), //是否是主窗口
  userInfo: propTypes.object.def({}),
  infoList: propTypes.array.def([]),
  type: propTypes.string.def(''), //是工作台还是总控台
});
const emit = defineEmits('layoutOrChange');
</script>
<style lang="scss" scoped>
.user,
.childuser {
  width: 120px;
  height: 55px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: relative;
  span {
    display: inline-block;
    color: white;
    margin-right: 5px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .photo {
    position: relative;
    img:nth-of-type(1) {
      width: 28px;
    }
    img:nth-of-type(2) {
      width: 13px;
      bottom: -1px;
      right: -2px;
      position: absolute;
    }
  }
}
.childuser {
  height: 29px;
  font-size: 12px;
  .photo {
    position: relative;
    img:nth-of-type(1) {
      width: 26px;
    }
    img:nth-of-type(2) {
      width: 9px;
      bottom: 6px;
    }
  }
}
.agencyList {
  width: 210px;
  font-size: 12px;
  position: relative;
  .roleType {
    color: white;
    font-size: 10px;
    position: absolute;
    top: 7px;
    right: 28px;
    background: #409eff;
    border-radius: 5px;
    padding: 0px 2px;
    display: inline-block;
    height: 18px;
    line-height: 18px;
    z-index: 2;
  }
  .userDetail {
    width: 135px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;

    .enterInfo {
      img {
        margin: 0;
      }
      width: 80px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
      position: absolute;
      bottom: 3px;
      left: 58px;
    }
  }
}
.ant-dropdown-menu-item:nth-child(1) {
  position: relative;
  .userDetail {
    width: 110px;
    margin-left: 5px;
  }
  .vip {
    width: 13px;
    bottom: 2px;
    left: 40px;
    position: absolute;
  }
}
:deep(.ant-dropdown-menu-item) {
  // width: 200px;
  font-size: 12px;
  display: block !important;
  .isShow {
    display: none;
    float: right;
    font-size: 14px;
    margin-top: 5px;
    color: #409eff;
  }
  &:hover {
    .isShow {
      display: block !important;
    }
  }
}
</style>
