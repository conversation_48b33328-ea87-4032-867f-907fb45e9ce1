"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitFeeDescription = void 0;
const BaseModel_1 = require("./BaseModel");
class UnitFeeDescription extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, constructId, unitId, unitFeeFileId, singleId, feeFileCode, sortNo, name, context, optionList) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.constructId = constructId;
        this.unitId = unitId;
        this.unitFeeFileId = unitFeeFileId;
        this.singleId = singleId;
        this.feeFileCode = feeFileCode;
        this.sortNo = sortNo;
        this.name = name;
        this.context = context;
        this.optionList = optionList;
    }
}
exports.UnitFeeDescription = UnitFeeDescription;
//# sourceMappingURL=UnitFeeDescription.js.map