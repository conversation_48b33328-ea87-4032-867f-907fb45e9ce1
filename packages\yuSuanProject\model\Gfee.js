"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Gfee = void 0;
const BaseModel_1 = require("./BaseModel");
class Gfee extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, unitId, costMajorName, costFeeBase, gfeeRate, feeAmount) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.unitId = unitId;
        this.costMajorName = costMajorName;
        this.costFeeBase = costFeeBase;
        this.gfeeRate = gfeeRate;
        this.feeAmount = feeAmount;
    }
}
exports.Gfee = Gfee;
//# sourceMappingURL=Gfee.js.map