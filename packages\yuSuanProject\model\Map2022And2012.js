"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.get2022BY2012Cslb = exports.get2022BY2012 = void 0;
//取费文件2022-2012映射
let data = [
    {
        2012: {
            "name": "一般土建工程",
            "qfCode": "YBTJGC",
        },
        2022: {
            "name": "一般土建工程",
            "qfCode": "YBTJGC",
        }
    },
    {
        2012: {
            "name": "预制桩工程",
            "qfCode": "YZZGC",
        },
        2022: {
            "name": "地基处理与边坡支护工程、打拔钢板桩及预制混凝土桩",
            "qfCode": "DJCL",
        }
    },
    {
        2012: {
            "name": "土石方工程",
            "qfCode": "TSFGC",
        },
        2022: {
            "name": "土石方、建筑物超高、垂直运输、大型机械场外运输及一次安拆",
            "qfCode": "JZTSF",
        }
    },
    {
        2012: {
            "name": "灌注桩工程",
            "qfCode": "GZZGC",
        },
        2022: {
            "name": "现场混凝土灌注桩",
            "qfCode": "XCHNTGZZ",
        }
    },
    {
        2012: {
            "name": "钢结构工程",
            "qfCode": "GJGGC",
        },
        2022: {
            "name": "一般土建工程",
            "qfCode": "YBTJGC",
        }
    },
    {
        2012: {
            "name": "装配式混凝土结构工程",
            "qfCode": "ZPSHNTJGGC",
        },
        2022: {
            "name": "一般土建工程",
            "qfCode": "YBTJGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "安装工程",
            "qfCode": "AZGC",
        },
        2022: {
            "name": "安装工程",
            "qfCode": "AZGC",
        }
    },
    {
        2012: {
            "name": "装饰工程",
            "qfCode": "ZSGC",
        },
        2022: {
            "name": "装饰工程",
            "qfCode": "ZSGC",
        }
    },
    {
        2012: {
            "name": "市政土石方工程、大型机械一次安拆及场外运输、拆除工程",
            "qfCode": "SZTSFGC_DXJXYCACJCWYS_CCGC",
        },
        2022: {
            "name": "市政工程土石方、大型机械一次安拆及场外运输",
            "qfCode": "SZTSF",
        }
    },
    {
        2012: {
            "name": "道路工程",
            "qfCode": "DLGC",
        },
        2022: {
            "name": "道路工程",
            "qfCode": "DLGC",
        }
    },
    {
        2012: {
            "name": "路灯工程",
            "qfCode": "LDGC",
        },
        2022: {
            "name": "路灯工程",
            "qfCode": "LDGC",
        }
    },
    {
        2012: {
            "name": "排水工程",
            "qfCode": "PSGC",
        },
        2022: {
            "name": "一般市政工程",
            "qfCode": "YBSZGC",
        }
    },
    {
        2012: {
            "name": "给水工程",
            "qfCode": "GSGC",
        },
        2022: {
            "name": "一般市政工程",
            "qfCode": "YBSZGC",
        }
    },
    {
        2012: {
            "name": "隧道工程",
            "qfCode": "SDGC",
        },
        2022: {
            "name": "一般市政工程",
            "qfCode": "YBSZGC",
        }
    },
    {
        2012: {
            "name": "桥涵工程",
            "qfCode": "QHGC",
        },
        2022: {
            "name": "一般市政工程",
            "qfCode": "YBSZGC",
        }
    },
    {
        2012: {
            "name": "燃气与集中供热",
            "qfCode": "RQYJZJZGR",
        },
        2022: {
            "name": "一般市政工程",
            "qfCode": "YBSZGC",
        }
    }
];
let map = new Map();
data.forEach(item => {
    map.set(item[2012].qfCode, item[2022]);
});
function get2022BY2012(qfCode) {
    return map.get(qfCode);
}
exports.get2022BY2012 = get2022BY2012;
let cslbMap = {
    "2012-JZGC-DEY-002-TSFGC": "2022-JZGC-DEY-102-JZTSF",
    "2012-JZGC-DEY-003-YZZGC": "2022-JZGC-DEY-103-DJCL",
    "2012-JZGC-DEY-003-GZZGC": "2022-JZGC-DEY-104-XCHNTGZZ",
    "2012-JZGC-DEY-001-YBJZGC": "2022-JZGC-DEY-101-YBTJGC",
    "2018-ZPSGJGGC-001-GJGGC": "2022-JZGC-DEY-101-YBTJGC",
    "2016-ZPSHNT-001-ZPSHNTJGGC": "2022-JZGC-DEY-101-YBTJGC",
    "2012-ZSZX-DEY-008-ZSZXGC": "2022-ZSZX-DEY-105-ZSGC",
    "2012-AZGC-DEK-004-SYFFSJRGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-GYGDGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-JXSBAZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-DQSBAZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-JZZNHXTSBAZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-JPSCNRQGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-JZSBYGYJSJGZZAZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-LJQZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-ZDHKZYBAZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-RLSBAZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-TFKDGC": "2022-AZGC-DEK-106-AZGC",
    "2012-AZGC-DEK-004-XFSBAZGC": "2022-AZGC-DEK-106-AZGC",
    "2012-SZGC-DEK-002-TSFGC": "2022-SZGC-DEK-108-SZTSF",
    "2012-SZGC-DEK-007-DLGC": "2022-SZGC-DEK-109-DLGC",
    "2012-SZGC-DEK-005-QHGC": "2022-SZGC-DEK-107-QHGC",
    "2012-SZGC-DEK-006-LDGC": "2022-SZGC-DEK-110-LDGC",
};
function get2022BY2012Cslb(cslbCode) {
    return cslbMap[cslbCode] || "2022-SYSTEM-SZGC";
}
exports.get2022BY2012Cslb = get2022BY2012Cslb;
/*
*
*   "2012-SZGC-DEK-005-PSGC":"",
  "2012-SZGC-DEK-005-SDGC":"",
  "2012-SZGC-DEK-005-JSGC":""*/
//# sourceMappingURL=Map2022And2012.js.map