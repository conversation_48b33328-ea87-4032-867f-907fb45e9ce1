# 窗口标题显示文件名功能测试

## 功能说明
在打开预算、工料机、结算等项目窗体时，在任务管理器的进程中显示文件名称。

## 修改内容

### 1. 预算项目 (packages/yuSuanProject/service/SystemService.js)
- 修改 `openWindowForProject` 方法
- 从文件路径提取文件名（不含扩展名）
- 如果文件名和项目名不同，则在窗口标题中显示：`项目名 - 文件名`
- 更新现有窗口的标题

### 2. 工料机项目 (packages/gongLiaoJiProject/service/gljWindowService.js)
- 修改 `openWindowForProject` 方法
- 从项目路径提取文件名
- 在窗口标题中显示文件名
- 更新现有窗口的标题

### 3. 结算项目 (packages/jieSuanProject/service/jieSuanProjectService.js)
- 修改新建和打开结算项目的窗口创建逻辑
- 从文件路径提取文件名
- 在窗口标题中显示文件名

### 4. 结算项目窗口管理 (packages/jieSuanProject/utils/JieSuanWinManageUtils.js)
- 修改 `createWindow` 方法
- 在窗口已存在时更新标题

### 5. 审核预算项目 (packages/shenHeYuSuanProject/service/shenHeProjectService.js)
- 修改 `openProject` 方法
- 从文件路径提取文件名
- 在窗口标题中显示文件名
- 更新现有窗口的标题

### 6. 概算项目 (packages/PreliminaryEstimate/service/gsWindowService.js)
- 修改 `openWindowForProject` 方法
- 从项目路径提取文件名
- 在窗口标题中显示文件名
- 更新现有窗口的标题

### 7. Electron 主进程 (electron/service/SystemService.js)
- 修改 `openWindowForProject` 方法
- 从文件路径提取文件名
- 在窗口标题中显示文件名
- 更新现有窗口的标题

## 实现逻辑

```javascript
// 从文件路径提取文件名（不含扩展名）
const pathModule = require('path');
let fileName = '';
if (path) {
    fileName = pathModule.basename(path, pathModule.extname(path));
    // 如果文件名和项目名不同，则在窗口标题中显示文件名
    if (fileName && fileName !== windowName) {
        windowName = `${windowName} - ${fileName}`;
    }
}

// 更新现有窗口的标题
if (existingWindow) {
    win.setTitle(windowName);
}
```

## 测试步骤

1. **预算项目测试**
   - 打开一个预算文件（.ysf）
   - 检查任务管理器中的进程名称是否显示文件名

2. **工料机项目测试**
   - 打开一个工料机文件（.ygs）
   - 检查任务管理器中的进程名称是否显示文件名

3. **结算项目测试**
   - 新建或打开一个结算文件（.yjs）
   - 检查任务管理器中的进程名称是否显示文件名

4. **概算项目测试**
   - 打开一个概算文件（.ygs）
   - 检查任务管理器中的进程名称是否显示文件名

5. **审核预算项目测试**
   - 打开一个审核预算文件（.ysh）
   - 检查任务管理器中的进程名称是否显示文件名

## 预期结果

- 在任务管理器中，每个项目窗口的进程名称应该显示为：`项目名 - 文件名`
- 如果项目名和文件名相同，则只显示项目名
- 窗口标题栏也应该显示相应的标题

## 注意事项

- 文件名提取使用 `path.basename()` 方法，自动去除扩展名
- 只有当文件名和项目名不同时才会在标题中显示文件名
- 对于已存在的窗口，会动态更新标题
- 所有项目类型（预算、工料机、结算、概算、审核预算）都支持此功能
