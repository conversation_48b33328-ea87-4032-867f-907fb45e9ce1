<!--
 * @Descripttion: 侧边树
 * @Author: renmingming
 * @Date: 2023-05-16 14:18:57
 * @LastEditors: liuxia
 * @LastEditTime: 2024-12-09 16:22:29
-->
<template>
  <!-- 展开收起 -->
  <div :class="[expand ? 'operation-list' : 'expand-menu']">
    <div v-show="!expand && store.type !== 'yssh'">
      <a-tooltip placement="right">
        <template #title>编辑结构</template>
        <a-button type="text" @click="openVisible"
          ><icon-font type="icon-bianjijiegou"
        /></a-button>
      </a-tooltip>

      <a-dropdown>
        <a-button type="text" @click.prevent>
          <a-tooltip placement="right">
            <template #title>导入/导出</template>
            <icon-font type="icon-daorudaochu1" />
          </a-tooltip>
        </a-button>
        <template #overlay>
          <a-menu @click="onOperate">
            <a-menu-item
              key="import"
              v-if="
                (store.type === 'ys' &&
                  !store.currentTreeGroupInfo.optionLock) ||
                (store.type !== 'ys' && store.type !== 'jieSuan')
              "
              >导入项目
            </a-menu-item>
            <a-menu-item key="export" v-if="store.type !== 'jieSuan'"
              >导出项目</a-menu-item
            >
            <a-menu-item key="importJs" v-if="store.type == 'jieSuan'"
              >导入结算文件</a-menu-item
            >
            <a-menu-item key="exportJs" v-if="store.type == 'jieSuan'"
              >导出结算文件</a-menu-item
            >
            <a-menu-item
              key="importExcel"
              v-if="
                store.type == 'ys' && !store.currentTreeGroupInfo.optionLock
              "
              :disabled="
                store.currentTreeInfo.levelType < 3 &&
                store.currentTreeInfo?.children?.find(a => a.levelType === 2)
              "
              >导入excel</a-menu-item
            >
          </a-menu>
        </template>
      </a-dropdown>
      <a-tooltip placement="right" title="上移" v-if="upDownBtn">
        <icon-font
          type="icon-shangyi"
          style="margin: 5px 5px 10px 0px; width: 46px"
          @click="upOrDown('up')"
          :class="currentInfo.isFirst ? 'disabled-icon' : 'click-icon'"
        ></icon-font>
        <!--  -->
      </a-tooltip>
      <a-tooltip placement="right" title="下移" v-if="upDownBtn">
        <icon-font
          type="icon-xiayi"
          @click="upOrDown('down')"
          :class="currentInfo.isLast ? 'disabled-icon' : 'click-icon'"
        ></icon-font>
        <!-- -->
      </a-tooltip>
    </div>

    <div v-show="expand && store.type !== 'yssh'">
      <a-button type="text" @click="openVisible"
        ><icon-font type="icon-bianjijiegou" class="icon" />编辑结构</a-button
      >
      <a-dropdown>
        <a-button type="text" @click.prevent
          ><icon-font
            type="icon-daorudaochu1"
            class="icon"
          />导入/导出</a-button
        >
        <template #overlay>
          <a-menu @click="onOperate">
            <a-menu-item
              key="import"
              v-if="
                (store.type === 'ys' &&
                  !store.currentTreeGroupInfo.optionLock) ||
                (store.type !== 'ys' && store.type !== 'jieSuan')
              "
              >导入项目</a-menu-item
            >
            <a-menu-item key="export" v-if="store.type !== 'jieSuan'"
              >导出项目</a-menu-item
            >
            <a-menu-item key="importJs" v-if="store.type == 'jieSuan'"
              >导入结算文件</a-menu-item
            >
            <a-menu-item key="exportJs" v-if="store.type == 'jieSuan'"
              >导出结算文件</a-menu-item
            >
            <a-menu-item
              key="importExcel"
              v-if="
                store.type == 'ys' && !store.currentTreeGroupInfo.optionLock
              "
              :disabled="
                store.currentTreeInfo.levelType < 3 &&
                store.currentTreeInfo?.children?.find(a => a.levelType === 2)
              "
              >导入excel</a-menu-item
            >
          </a-menu>
        </template>
      </a-dropdown>
      <a-tooltip placement="top" title="上移" v-if="upDownBtn">
        <icon-font
          type="icon-shangyi"
          style="margin-right: 5px"
          @click="upOrDown('up')"
          :class="currentInfo.isFirst ? 'disabled-icon' : 'click-icon'"
        ></icon-font>
        <!--  -->
      </a-tooltip>
      <a-tooltip placement="top" title="下移" v-if="upDownBtn">
        <icon-font
          type="icon-xiayi"
          @click="upOrDown('down')"
          :class="currentInfo.isLast ? 'disabled-icon' : 'click-icon'"
        ></icon-font>
        <!-- -->
      </a-tooltip>
    </div>
  </div>

  <!-- 左侧树 -->
  <div
    class="tree-wraps"
    ref="treeContent"
    :class="expand ? 'tree-content' : 'tree-content-container'"
  >
    <common-aside-tree-jiesuan
      ref="asideTree"
      v-if="store.type == 'jieSuan'"
      @getTreeList="handleSuccess"
      @handleChange="onOperate"
      :treeData="props.treeData"
      pageFrom="jiesuan"
      :leftIsExpand="expand"
    ></common-aside-tree-jiesuan>
    <common-aside-tree
      v-else
      ref="asideTree"
      @getTreeList="handleSuccess"
      :treeData="props.treeData"
      :leftIsExpand="expand"
      @drop="drop"
      :compareMatch="props.compareMatch"
    ></common-aside-tree>
  </div>

  <edit-project-structure
    v-model:visible="visible"
    :majorList="engineerMajorList"
    @success="handleSuccess"
    :config="config"
  ></edit-project-structure>
  <edit-project-Js-structure
    v-model:visible="visibleJS"
    :majorList="engineerMajorList"
    @success="handleSuccess"
  ></edit-project-Js-structure>
  <ExportJsFileAsyncComponent
    v-if="OperateStatus === 'exportJs'"
    @closeDialog="closeDialog"
    ref="ExportJsFileRef"
  >
  </ExportJsFileAsyncComponent>
  <importJsFileAsyncComponent
    @closeDialog="closeDialog"
    :importJsList="importJsList"
    ref="ImportJsFileRef"
    v-if="OperateStatus === 'ImportJsFileRef'"
  >
  </importJsFileAsyncComponent>
  <ExportFileAsyncComponent
    v-if="OperateStatus === 'export'"
    @closeDialog="closeDialog"
    ref="ExportFileRef"
  ></ExportFileAsyncComponent>
  <importFileAsyncComponent
    @closeDialog="closeDialog"
    :importList="importList"
    v-if="OperateStatus === 'ImportFileRef'"
  ></importFileAsyncComponent>
  <setProjectBelong
    @closeDialog="closeDialog"
    :belongData="belongData"
    ref="ProjectBelongRef"
    v-if="OperateStatus === 'ProjectBelongRef'"
  >
  </setProjectBelong>
  <importJsOtherFileAsyncComponent
    @closeDialog="closeDialog"
    :otherData="otherData"
    :importJsOtherList="importJsOtherList"
    ref="ImportJsOtherFileRef"
    v-if="OperateStatus === 'ImportJsOtherFileRef'"
  >
  </importJsOtherFileAsyncComponent>
  <import-excel @closeImportExcel="closeImportExcel" ref="importExcelRef" />
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  defineAsyncComponent,
  watch,
  watchEffect,
  nextTick,
  computed,
  toRaw,
} from 'vue';
import { useRoute } from 'vue-router';
import csProject from '../../../api/csProject';
import feePro from '../../../api/feePro';
import xeUtils from 'xe-utils';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import { proModelStore } from '@/store/proModel.js';
import operateList from './operate';
import { useCheckProjectBefore } from '@/hooks/useCheckProjectBefore';
import infoMode from '@/plugins/infoMode.js';
import jieSuanApi from '@/api/jiesuanApi';

const { showInfo } = useCheckProjectBefore();
const importList = ref(null);
const importJsList = ref(null);
const asideTree = ref(null);
let upDownBtn = ref(false);
const ExportFileAsyncComponent = defineAsyncComponent(() =>
  import('@/components/fileSection/exportFile.vue')
); //导出项目组建
const importFileAsyncComponent = defineAsyncComponent(() =>
  import('@/components/fileSection/importFile.vue')
); //导出项目组建

const editProjectStructure = defineAsyncComponent(() =>
  import('@/components/editProjectStructure/index.vue')
);
const editProjectJsStructure = defineAsyncComponent(() =>
  import('@/components/editJsProjectStructure/index.vue')
);
const importJsFileAsyncComponent = defineAsyncComponent(() =>
  import('@/components/fileSection/importJsFile.vue')
); //导入结算项目组建
const ExportJsFileAsyncComponent = defineAsyncComponent(() =>
  import('@/components/fileSection/exportJsFile.vue')
); //导出结算项目组件
const importJsOtherFileAsyncComponent = defineAsyncComponent(() =>
  import('@/components/fileSection/importJsOtherFile.vue')
); //导入其他项目组建
const setProjectBelong = defineAsyncComponent(() =>
  import('@/components/fileSection/setJsProjectBelong.vue')
); //工程归属
const importExcel = defineAsyncComponent(() =>
  import('@/components/fileSection/importExcel.vue')
);
const route = useRoute();
const store = projectDetailStore();
const vexTable = ref();

let visible = ref(false);
let visibleJS = ref(false);
const proStore = proModelStore();

let engineerMajorList = ref([]);
const props = defineProps(['treeData', 'isExpand', 'compareMatch']);
const emit = defineEmits(['getTreeList', 'upOrDown', 'drop']);
let unifyData = ref(); //统一应用按钮是否禁用
let config = ref([]); //编辑项目结构设置
let importExcelData = ref([]); //预算导入excel数据
const openVisible = (isImport = true) => {
  if (store.type === 'jieSuan') {
    visibleJS.value = true;
  } else {
    if (!isImport && store.type === 'ys') {
      config.value = {
        showMenu: false, //隐藏编辑按钮
        constructObj: importExcelData.value,
        isEdit: true,
        type: 'importExcel',
      };
    } else {
      config.value = {
        showMenu: true, //隐藏编辑按钮
        constructObj: null,
        isEdit: false,
        type: '',
      };
    }
    visible.value = true;
  }
};

watchEffect(() => {
  if (proStore.openEditModalStatus) {
    openVisible();
    // 编辑弹窗还是自动打开，则关闭
    proStore.onEditModal(false);
  }
});
watch(
  () => store.currentTreeInfo,
  () => {
    if (
      // store.biddingType !== 2 &&
      (store.type !== 'jieSuan' ||
        (store.type === 'jieSuan' && !store.currentTreeInfo.originalFlag)) &&
      store.currentTreeInfo &&
      (store.currentTreeInfo.levelType === 2 ||
        store.currentTreeInfo.levelType === 3)
    ) {
      upDownBtn.value = true;
    } else {
      upDownBtn.value = false;
    }
  }
);
watch(
  () => props.treeData,
  (newVal, oldVal) => {
    setUpOrDownBtn();
    xeUtils.clone(props.treeData).map(i => {
      if (i.name) {
        i.copyName = i.name;
      }
      return i;
    });
    store.projectTree = newVal;
    let change;
    if (!change) return;
    if (newVal && oldVal && newVal.length > oldVal.length) {
      let change = newVal.filter(x => !oldVal.some(y => y.id === x.id));
      if (change) {
        //默认展开一级节点，所以此处只需要判断添加的是单位展开其父节点
        if (change[0]?.levelType === 3) {
          let parant = newVal.filter(x => x.id === change[0].parentId);
          console.log('parant', parant);
        }
      }
    } else if (newVal && oldVal && newVal.length < oldVal.length) {
      change = oldVal.filter(x => !newVal.some(y => y.id === x.id))[0];
    }
  },
  { deep: true }
);
const drop = data => {
  emit('drop', data);
};
const upOrDown = type => {
  emit('upOrDown', type);
};
let currentInfo = reactive({
  isFirst: true,
  isLast: true,
});
const setUpOrDownBtn = () => {
  //设置当前选中目标上下移动的置灰状态
  if (!store.currentTreeInfo?.parentId) return;
  let sameLevel = props.treeData.filter(
    item => item.parentId === store.currentTreeInfo.parentId
  );
  if (sameLevel.length > 1) {
    currentInfo.isFirst = false;
    currentInfo.isLast = false;
    if (sameLevel[0].id === store.currentTreeInfo.id) {
      //第一项不可以上移
      currentInfo.isFirst = true;
    }
    if (sameLevel[sameLevel.length - 1].id === store.currentTreeInfo.id) {
      //最后一项不可以下移
      currentInfo.isLast = true;
    }
  } else {
    currentInfo.isFirst = true;
    currentInfo.isLast = true;
  }
};
watch(
  () => store.currentTreeInfo,
  () => {
    setUpOrDownBtn();
  }
);
// 外层的展开收起
const expand = computed(() => {
  return props.isExpand;
});

// 导入导出项目
const OperateStatus = ref(null);
const ExportFileRef = ref(null);
const ExportJsFileRef = ref(null);
const ImportFileRef = ref(null);
const ImportJsFileRef = ref(null);
const importJsOtherList = ref(null);
const ProjectBelongRef = ref(null);
let belongData = ref({});
let otherData = ref({});
const importExcelRef = ref(null);
const onOperate = ({ key }, type, data) => {
  if (!showInfo()) return;
  if (type === 1) {
    belongData.value = data;
  } else if (type === 2) {
    otherData.value = data;
  }
  switch (key) {
    case 'import':
      upFile();
      break;
    case 'export':
      OperateStatus.value = 'export';
      break;
    case 'importJs':
      upFileJs();
      break;
    case 'exportJs':
      OperateStatus.value = 'exportJs';
      break;
    case 'projectBelong':
      OperateStatus.value = 'ProjectBelongRef';
      break;
    case 'other':
      upUnitFile();
      break;
    case 'importExcel':
      //非单位工程
      OperateStatus.value = 'importExcel';
      console.log('importExcelRef.value', importExcelRef.value);
      importExcelRef.value.open();
      break;
    default:
      break;
  }
};
// 选择结算文件
const upFileJs = () => {
  jieSuanApi.importYjsFile(route.query.constructSequenceNbr).then(res => {
    console.log(
      '🚀 ~ file: AsideTree.vue:497 ~ csProject.importYjsFile ~ res:',
      res
    );
    if (res?.status) {
      message.error(res.message);
    } else {
      importJsList.value = res;
      OperateStatus.value = 'ImportJsFileRef';
    }
  });
};
// 导入单位工程
const upUnitFile = () => {
  jieSuanApi.importJsFile(route.query.constructSequenceNbr).then(res => {
    if (res?.status) {
      message.error(res.message);
    } else {
      importJsOtherList.value = res;
      OperateStatus.value = 'ImportJsOtherFileRef';
    }
  });
};
// 导入弹窗回调
const closeDialog = e => {
  OperateStatus.value = null;
};
let visibleExcel = ref(false);
const closeImportExcel = result => {
  if (result) {
    result.map(a => (a.id = a.uid));
  }
  importExcelData.value = result;
  openVisible(false); //导入需要编辑专业
};
// 选择文件
const upFile = () => {
  csProject.importYsfFile(route.query.constructSequenceNbr).then(res => {
    console.log(
      '🚀 ~ file: AsideTree.vue:497 ~ csProject.importYsfFile ~ res:',
      res
    );
    if (res?.status) {
      message.error(res.message);
    } else {
      importList.value = res;
      OperateStatus.value = 'ImportFileRef';
    }
  });
};

const saveHumanData = oldVal => {
  unifyData.value = operateList.value.find(
    item => item.name === store.humanUpdataData.name
  );
  let infoText =
    store.humanUpdataData.name === 'unify'
      ? '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？'
      : '人材机数据已修改，是否应用整个工程项目?';
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      store.humanUpdataData?.name === 'unify'
        ? feeTotalSave(oldVal)
        : humanSave(oldVal);
      infoMode.hide();
    },
    close: () => {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
      infoMode.hide();
    },
  });
  // Modal.confirm({
  //   title: `${infoText}`,
  //   onOk() {
  //     store.humanUpdataData.name === 'unify'
  //       ? feeTotalSave(oldVal)
  //       : humanSave(oldVal);
  //   },
  //   onCancel() {
  //     resetHumanData(oldVal);
  //     unifyData.value.disabled = true;
  //   },
  // });
};
const humanSave = oldVal => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    constructProjectRcjList: JSON.parse(
      JSON.stringify(store.humanUpdataData.updataData)
    ),
  };
  csProject.changeRcjConstructProject(apiData).then(res => {
    console.log('asideTree统一应用接口返回结果', res);
    if (res.status === 200) {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
    }
  });
};
const feeTotalSave = oldVal => {
  if (store.humanUpdataData.updataData.policy) {
    feePro
      .checkPolicyDocument(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.policy))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.feeTotal) {
    feePro
      .unifiedUse(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.feeTotal))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.calEditData) {
    let apiData = {
      constructId:
        store.currentTreeInfo.levelType === 1
          ? store.currentTreeInfo?.id
          : store.currentTreeGroupInfo?.constructId,
      feeCalculateBaseList: JSON.parse(
        JSON.stringify(store.humanUpdataData.updataData.calEditData)
      ),
    };
    feePro.updateProjectUnitCalculateBaseApply(apiData).then(res => {
      if (res.status === 200) {
        console.log('updateProjectUnitCalculateBaseApply2', res, apiData);
      }
    });
  }
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
};
const resetHumanData = oldVal => {
  store.SET_HUMAN_UPDATA_DATA(null);
  vexTable.value.setCurrentRow(oldVal.newValue);
  currentChangeEvent(oldVal);
};
const currentChangeEvent = newValue => {
  if (store.humanUpdataData && store.humanUpdataData.isEdit) {
    vexTable.value.setCurrentRow(newValue.oldValue); //人材机数据有需要保存的，先确定是否要先保存
    saveHumanData(newValue);
    return;
  }
  let obj = {
    constructId: '',
    name: '',
    singleId: '',
    singleName: '',
  };
  if (newValue.row.levelType === 3) {
    props.treeData.forEach(item => {
      if (item.levelType === 1) {
        obj.constructId = item.id;
        obj.name = item.name;
      } else if (item.id === newValue.row.parentId) {
        obj.singleId = item.id;
        obj.singleName = item.name;
      }
    });
  } else if (newValue.row.levelType === 2) {
    props.treeData.forEach(item => {
      obj.constructId = item.id;
      obj.name = item.name;
    });
    obj.singleId = newValue.row.id;
    obj.name = newValue.row.name;
  } else {
    obj.constructId = newValue.row.id;
    obj.name = newValue.row.name;
  }
  // console.log('newValue', newValue);
  // console.log(store);
  store.SET_CURRENT_TREE_INFO(newValue.row);
  store.SET_CURRENT_TREE_GROUP_INFO(obj);
};

const handleSuccess = nofresh => {
  emit('getTreeList', nofresh);
};

const setCheckRow = checkRow => {
  asideTree.value?.setCheckRow(checkRow);
};

defineExpose({
  setCheckRow,
});
</script>

<style lang="scss" scoped>
.expand-menu {
  padding-top: 5px;
  width: 47px;
  height: auto;
  background-color: white;
  :deep(.ant-btn) {
    position: relative;
    left: -1px;
  }
}
.expand-table {
  margin-top: 10px;
  background-color: #f8fbff;
}
.expand-span {
  display: block;
  text-align: center;
}
.sc-tree {
  .vxe-cell {
    .dropdown-context-menu {
      display: flex;
    }
    .ant-dropdown-trigger {
      flex: 1;
    }
  }
}

.dropdown-context-menu {
  :deep(.ant-dropdown) {
    .disabled {
      background: #dfdfdf;
    }
  }
}

.operation-list {
  display: flex;
  align-items: center;
  height: 42px;
  border-bottom: 2px solid #e8ecf1;
  background: #f2f6fc;
  overflow: hidden;
  button {
    font-size: 12px;
    .icon {
      font-size: 14px;
    }
  }
  :deep(.ant-btn) {
    padding: 4px 10px;
  }
  :deep(.ant-btn > .anticon + span, .ant-btn > span + .anticon) {
    margin-left: 3px;
  }
}

.tree-wraps {
  padding: 10px 0 0;
  height: calc(100% - 11px);
  width: 100%;
  // height: calc(100% - 42px);
  // width: 225px;
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  background: #f2f6fc;
  // &::-webkit-scrollbar {
  //   width: 5px;
  //   height: 6px;
  // }
  // ::-webkit-scrollbar-thumb {
  //   //滚动条的设置
  //   background-color: rgba(24, 144, 255, 0.2);
  //   background-clip: padding-box;
  //   min-height: 28px;
  //   border-radius: 5px;
  // }
  // ::-webkit-scrollbar-thumb:hover {
  //   background-color: rgba(24, 144, 255, 0.8);
  // }
  :deep(.vxe-table) {
    background: transparent;
    font-size: 14px;
    .vxe-table--body-wrapper {
      background-color: transparent;
      overflow: hidden;
      table {
        background-color: transparent;
      }
    }
    .vxe-body--column {
      // padding: 5px 0;
      cursor: pointer;
      padding: 0px 0 !important;
      margin-bottom: 5px;
    }
    .vxe-icon-caret-right {
      color: var(--primary-color);
    }
    .row--current {
      background-color: #deeaff;
    }
  }
}

.tree-content {
  :deep(.vxe-table) {
    .vxe-body--column {
      height: 24px !important ;
    }
  }
}

.tree-content-container {
  width: 47px;
  height: calc(100% - 70px);
  overflow-x: hidden;
  background-color: #fff;
}
.disabled-icon {
  opacity: 0.3;
  cursor: none;
}
.click-icon {
  cursor: pointer;
  opacity: 1;
}
</style>
