"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseManageRate2022 = exports.BaseManageRate = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 行政区域
 */
let BaseManageRate = class BaseManageRate extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "project_type" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "projectType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_code" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_code" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "sortNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "precast_rate" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "precastRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee1" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "managementFee1", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee2" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "managementFee2", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee3" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "managementFee3", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit1" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "profit1", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit2" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "profit2", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit3" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "profit3", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseManageRate.prototype, "remark", void 0);
BaseManageRate = __decorate([
    (0, typeorm_1.Entity)()
], BaseManageRate);
exports.BaseManageRate = BaseManageRate;
let BaseManageRate2022 = class BaseManageRate2022 extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, projectType, libraryCode, qfCode, sortNo, precastRate, managementFee1, managementFee2, managementFee3, profit1, profit2, profit3, remark, profit1Ybjs, profit2Ybjs, profit3Ybjs, profit1Jyjs, profit2Jyjs, profit3Jyjs, managementFee1Ybjs, managementFee2Ybjs, managementFee3Ybjs, managementFee1Jyjs, managementFee2Jyjs, managementFee3Jyjs) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.projectType = projectType;
        this.libraryCode = libraryCode;
        this.qfCode = qfCode;
        this.sortNo = sortNo;
        this.precastRate = precastRate;
        this.managementFee1 = managementFee1;
        this.managementFee2 = managementFee2;
        this.managementFee3 = managementFee3;
        this.profit1 = profit1;
        this.profit2 = profit2;
        this.profit3 = profit3;
        this.remark = remark;
        this.profit1Ybjs = profit1Ybjs;
        this.profit2Ybjs = profit2Ybjs;
        this.profit3Ybjs = profit3Ybjs;
        this.profit1Jyjs = profit1Jyjs;
        this.profit2Jyjs = profit2Jyjs;
        this.profit3Jyjs = profit3Jyjs;
        this.managementFee1Ybjs = managementFee1Ybjs;
        this.managementFee2Ybjs = managementFee2Ybjs;
        this.managementFee3Ybjs = managementFee3Ybjs;
        this.managementFee1Jyjs = managementFee1Jyjs;
        this.managementFee2Jyjs = managementFee2Jyjs;
        this.managementFee3Jyjs = managementFee3Jyjs;
    }
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "project_type" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "projectType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "library_code" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "qf_code" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "qfCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "sortNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "precast_rate" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "precastRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit1_ybjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "profit1Ybjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit2_ybjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "profit2Ybjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit3_ybjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "profit3Ybjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit1_jyjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "profit1Jyjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit2_jyjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "profit2Jyjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "profit3_jyjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "profit3Jyjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee1_ybjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "managementFee1Ybjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee2_ybjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "managementFee2Ybjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee3_ybjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "managementFee3Ybjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee1_jyjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "managementFee1Jyjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee2_jyjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "managementFee2Jyjs", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "management_fee3_jyjs" }),
    __metadata("design:type", String)
], BaseManageRate2022.prototype, "managementFee3Jyjs", void 0);
BaseManageRate2022 = __decorate([
    (0, typeorm_1.Entity)({ name: "base_manage_rate_2022" }),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String, String])
], BaseManageRate2022);
exports.BaseManageRate2022 = BaseManageRate2022;
//# sourceMappingURL=BaseManageRate.js.map