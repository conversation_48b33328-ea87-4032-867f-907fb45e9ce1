const { Snowflake } = require('../../utils/Snowflake');
class ConversionInfoUtil {

    constructor() {
        this.STARDARD_CONVERSION_SOURCE = "标准换算";
        this.DIRECT_INPUT = "直接输入";
        this.UNITE_CONVERSION_SOURCE = "统一换算";
        this.RCJ_DETAIL_SOURCE = "人材机明细";
        this.RCJ_COUNT_SOURCE = "人材机汇总";
        this.SZSSWXYHZXZX22 = "全局设置-市政设施维修养护工程执行中修";
        this.TYPE1 = "add";
        this.TYPE2 = "del";
        this.TYPE3 = "update";
        this.TYPE4 = "updateQty";
        this.TYPE5 = "replace";
        this.RCJ_CONVERSION_INFO_ADD_TYPE = this.TYPE1;
        this.RCJ_CONVERSION_INFO_DEL_TYPE = this.TYPE2;
        this.RCJ_CONVERSION_INFO_UPDATE_TYPE = this.TYPE3;
        this.RCJ_CONVERSION_INFO_UPDATEQTY_TYPE = this.TYPE4;
        this.RCJ_CONVERSION_INFO_REPLACE_TYPE = this.TYPE5;

    }

    initConversionInfo(rule,source,conversionString,sortNo = null){
        return {
            sequenceNbr: Snowflake.nextId(),
            ...rule,
            ruleId: rule.sequenceNbr,
            source,
            conversionExplain: null,
            conversionString,
            kind: rule.kind,
            type: rule.type,
            sortNo,
            children: []
        };
    }

    /**
     * 判断人材机是否“河北省建设工程消耗量标准（2025）-市政设施维修养护工程”定额册下的标准人材机（费用人材机除外）
     * @param rcj
     * @return {boolean}
     */
    isSzsswxyhzxzxRCJ22(rcj){
        return rcj.libraryCode === "2024-SZSS-DEX" && (!!rcj.standardId || rcj.supplementRcjFlag === 1)  && rcj.isFyrcj !==0;
    }
}

module.exports = {
    ConversionInfoUtil: new ConversionInfoUtil()
}