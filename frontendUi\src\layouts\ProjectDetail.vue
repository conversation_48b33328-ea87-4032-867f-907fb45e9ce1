<!--
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-16 10:30:47
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-01-23 15:44:34
-->
<template>
  <a-layout class="layout">
    <a-layout-header>
      <project-header ref="headerMenu" />
    </a-layout-header>
    <a-layout-content><router-view :commands="commands" /></a-layout-content>
  </a-layout>
</template>

<script setup>
import ProjectHeader from '@/components/Header/ProjectHeader.vue';
import { ref, watch } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
const projectStore = projectDetailStore();
let headerMenu = ref(null);
watch(
  () => headerMenu.value,
  val => {
    console.log('bcdd',val)
    projectStore.headerMenu = val;
  },
  { deep: true }
);
</script>
<style lang="scss" scoped>
:deep(.ant-layout-header) {
  background-color: #fff;
  padding: 0;
  height: var(--project-detail-header-height);
  line-height: var(--project-detail-header-height);
}
</style>
