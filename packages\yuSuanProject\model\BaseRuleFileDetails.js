"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRuleFileDetails = void 0;
const typeorm_1 = require("typeorm");
const BaseModel_1 = require("./BaseModel");
const typeorm_2 = require("typeorm");
/**
 * 规则文件明细表
 */
let BaseRuleFileDetails = class BaseRuleFileDetails extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "file_details", nullable: true }),
    __metadata("design:type", String)
], BaseRuleFileDetails.prototype, "fileDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_code", nullable: true }),
    __metadata("design:type", String)
], BaseRuleFileDetails.prototype, "relationCode", void 0);
BaseRuleFileDetails = __decorate([
    (0, typeorm_2.Entity)({ name: "base_rule_file_details" })
], BaseRuleFileDetails);
exports.BaseRuleFileDetails = BaseRuleFileDetails;
//# sourceMappingURL=BaseRuleFileDetails.js.map