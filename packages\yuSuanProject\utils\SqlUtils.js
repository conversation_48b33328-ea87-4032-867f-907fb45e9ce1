class SqlUtils {
    constructor() {
        this.retain = 2;
    }

    convertToModel(arr) {
        if (!Array.isArray(arr)) {
            console.error('convertToModel仅支持数组');
            return []
        }
        return arr.map(v => {
            if (Array.isArray(v)) {
                return this.convertToModel(v)
            }
            return Object.fromEntries(Object.keys(v).map(key => {
                return [key.replace(/_([a-z])/g, (match, char) => char.toUpperCase()), v[key]]
            }))
        })
    }
}

module.exports = {
    SqlUtils: new SqlUtils()
};
