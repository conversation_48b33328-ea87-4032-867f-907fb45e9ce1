const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");

/**
 * 基本信息接口
 */
class GljOverviewController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 保存
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async save(args, redo="插入或编辑- -概况"){
        const res = await this.service.gongLiaoJiProject.gljOverviewService.saveList(args);
        return ResponseData.success(res);
    }

    /**
     * 删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async delete(args, redo="删除- -概况"){
        const res = await this.service.gongLiaoJiProject.gljOverviewService.delete(args);
        return ResponseData.success(res);
    }

    /**
     * 查询
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getList(args){
        const res = await this.service.gongLiaoJiProject.gljOverviewService.getList(args);
        return ResponseData.success(res);
    }

    /**
     * 上移下移基本信息，工程特征
     * @param args
     * @returns {*}
     */
    async moveUpAndDownOverview(args, redo="移动- -工程特征") {
        return await this.service.gongLiaoJiProject.gljOverviewService.moveUpAndDownOverview(args);
    }

    /**
     * 获取工程基本信--建筑分类的二级联动
     * @param args
     */
    async getJzType(args) {
        return ResponseData.success(await this.service.gongLiaoJiProject.gljOverviewService.getJzType(args));
    }
}

GljOverviewController.toString = () => '[class GljOverviewController]';
module.exports = GljOverviewController;