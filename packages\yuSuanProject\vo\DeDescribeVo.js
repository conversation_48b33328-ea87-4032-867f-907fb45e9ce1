/**
 * 分部目录树(分部分项、单价措施...)
 * TreeModel
 */
class DeDescribeVo {

    /**
     * 工作内容
     * String
     */
    jobContent;

    /**
     * 计量单位
     * String
     */
    unit;

    /**
     * 定额编号
     * String
     */
    deCode;

    /**
     * 名称
     * String
     */
    deName;

    /**
     * 基价
     * BigDecimal
     */
    basePrice;

    /**
     * 基价其中数据
     * List<Map<String,BigDecimal>>
     */
    baseData;

    /**
     * 人材机数据
     * List<List<ConstructProjectRcjDTO>>
     */
    rcjData;

}

module.exports = DeDescribeVo;
