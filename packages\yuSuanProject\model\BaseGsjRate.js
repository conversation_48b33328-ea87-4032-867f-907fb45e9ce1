"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseGsjRate2022 = exports.BaseGsjRate = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 行政区域
 */
let BaseGsjRate = class BaseGsjRate extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "code" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "name" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "kind" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "rate" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "rate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "sortNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "tax_reform_documents_id" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "taxReformDocumentsId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "tax_calculation_method" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "taxCalculationMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "tax_paying_region" }),
    __metadata("design:type", String)
], BaseGsjRate.prototype, "taxPayingRegion", void 0);
BaseGsjRate = __decorate([
    (0, typeorm_1.Entity)()
], BaseGsjRate);
exports.BaseGsjRate = BaseGsjRate;
let BaseGsjRate2022 = class BaseGsjRate2022 extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "code" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "name" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "kind" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "kind", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "rate" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "rate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "sort_no" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "sortNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "remark" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "tax_reform_documents_id" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "taxReformDocumentsId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "tax_calculation_method" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "taxCalculationMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, name: "tax_paying_region" }),
    __metadata("design:type", String)
], BaseGsjRate2022.prototype, "taxPayingRegion", void 0);
BaseGsjRate2022 = __decorate([
    (0, typeorm_1.Entity)("base_gsj_rate_2022")
], BaseGsjRate2022);
exports.BaseGsjRate2022 = BaseGsjRate2022;
//# sourceMappingURL=BaseGsjRate.js.map