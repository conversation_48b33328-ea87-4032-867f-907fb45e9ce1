"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectOverview = void 0;
const BaseModel_1 = require("./BaseModel");
class ProjectOverview extends BaseModel_1.BaseModel {
    constructor(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description, name, context, remark, type, lockFlag, unitId, constructId, sortNo, jsonStr, parentId, groupCode, addFlag, levelType, dispNo, requiredFlag) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.name = name;
        this.context = context;
        this.remark = remark;
        this.type = type;
        this.lockFlag = lockFlag;
        this.unitId = unitId;
        this.constructId = constructId;
        this.sortNo = sortNo;
        this.jsonStr = jsonStr;
        this.parentId = parentId;
        this.groupCode = groupCode;
        this.addFlag = addFlag;
        this.levelType = levelType;
        this.dispNo = dispNo;
        this.requiredFlag = requiredFlag;
    }
}
exports.ProjectOverview = ProjectOverview;
//# sourceMappingURL=ProjectOverview.js.map