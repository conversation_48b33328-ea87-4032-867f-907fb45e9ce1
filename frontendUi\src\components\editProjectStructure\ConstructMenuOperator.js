/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-23 11:33:46
 * @LastEditors: wangru
 * @LastEditTime: 2024-12-18 15:47:35
 */
import { Menu, MenuLevel, MenuOperator } from './Menu';
import { VXETable } from 'vxe-table';
import { projectDetailStore } from '@/store/projectDetail.js';
export class ConstructMenuOperator extends MenuOperator {
  static constructorLevel = MenuLevel.One;
  static quickNewUnit = MenuLevel.QuickNewUnit;
  static singleLevel = MenuLevel.Two;
  static singleChildLevel = MenuLevel.TwoToFive; //子单项---2.5
  static unitLevel = MenuLevel.Three;
  static delete = MenuLevel.Delete;
  static copy = MenuLevel.Copy; //右键复制
  static paste = MenuLevel.Paste; //右键粘贴
  static batchDelete = MenuLevel.BatchDelete; //右键批量删除
  static copyTo = MenuLevel.CopyTo; //右键复制到
  static batchModify = MenuLevel.BatchModify; // 批量修改
  static optionLock = MenuLevel.OptionLock; // 标段结构保护
  static rename = MenuLevel.Rename; // 重命名

  allTreeData;
  $VTable;
  newRecord;
  levelLimit; //子单项层级
  deleteLength; //删除项目个数
  constructor() {
    super();
    this.addMenu(
      new Menu('添加单项', false, ConstructMenuOperator.singleLevel)
    );
    this.addMenu(
      new Menu('添加子单项', false, ConstructMenuOperator.singleChildLevel)
    );
    this.addMenu(new Menu('添加单位', false, ConstructMenuOperator.unitLevel));
    this.addMenu(new Menu('删除', false, ConstructMenuOperator.delete));
    this.addMenu(
      new Menu('批量删除', false, ConstructMenuOperator.batchDelete)
    );
    // this.addMenu(new Menu('复制', false, ConstructMenuOperator.copy));
    this.addMenu(new Menu('复制到', false, ConstructMenuOperator.copyTo));
    // this.addMenu(new Menu('粘贴', false, ConstructMenuOperator.paste));
    this.addMenu(
      new Menu('批量修改名称', false, ConstructMenuOperator.batchModify)
    );
    this.addMenu(
      new Menu('标段结构保护', false, ConstructMenuOperator.optionLock)
    );
    this.addMenu(
      new Menu('快速新建单位工程', false, ConstructMenuOperator.quickNewUnit)
    );
    this.addMenu(
        new Menu('重命名', false, ConstructMenuOperator.rename)
    );
  }

  set allTreeData(data) {
    this.allTreeData = data;
  }

  get allTreeData() {
    return this.allTreeData;
  }

  set $VTable(tableRef) {
    this.$VTable = tableRef;
  }

  get $VTable() {
    return this.$VTable;
  }
  copyOperate(tar, treeList) {
    //右键复制操作
    console.log('copyOperate', tar, treeList);
  }
  getLevelLimit(tar, treeList) {
    //获取层级---子弹项目前层级限制为10级
    tar.parentId ? this.levelLimit++ : '';
    let parent = treeList.find(i => i.id === tar.parentId);
    parent ? this.getLevelLimit(parent, treeList) : '';
  }
  resetMenus(clickNode, treeList = []) {
    let store = projectDetailStore();
    this.menus.forEach(m => {
      if (
        m.menuLevel === ConstructMenuOperator.batchModify &&
        !store?.currentTreeGroupInfo?.optionLock
      ) {
        // 处理批量修改
        m.isValid = true;
      } else if (
        [
          ConstructMenuOperator.delete,
          ConstructMenuOperator.copyTo,
          ConstructMenuOperator.batchDelete,
        ].includes(m.menuLevel)
      ) {
        // 处理删除按钮
        m.isValid =
          clickNode.levelType !== ConstructMenuOperator.constructorLevel &&
          !store?.currentTreeGroupInfo?.optionLock;
      } else if (
        m.menuLevel === ConstructMenuOperator.copy &&
        !store?.currentTreeGroupInfo?.optionLock
      ) {
        m.isValid = true;
      } else if (m.menuLevel === ConstructMenuOperator.singleChildLevel) {
        //选中行有只有在没有单位的情况下才可以添加
        this.levelLimit = 0;
        this.getLevelLimit(clickNode, treeList);
        // console.log('this.levelLimit', this.levelLimit);
        m.isValid =
          (clickNode.levelType === 2 || clickNode.levelType === 2.5) &&
          !clickNode.children?.some(item => item.levelType === 3) &&
          this.levelLimit < 10 &&
          !store?.currentTreeGroupInfo?.optionLock; //选中项有单位工程的子项
      } else if (m.menuLevel === ConstructMenuOperator.optionLock) {
        let projectObj = treeList.find(i => i.levelType === 1);
        m.isValid = true;
        if (projectObj.optionLock) {
          m.name = '取消标段结构保护';
        } else {
          m.name = '标段结构保护';
        }
      } else if (
        clickNode.levelType === 3 &&
        treeList.find(i => i.levelType === 2)
      ) {
        //工程项目下只有单位，添加单项不置灰
        m.isValid =
          m.menuLevel >= clickNode.levelType &&
          !store?.currentTreeGroupInfo?.optionLock;
      } else {
        let sub =
          clickNode?.children && clickNode?.children.length
            ? clickNode?.children[0]
            : false;
        m.isValid =
          (m.menuLevel >= clickNode.levelType ||
            m.menuLevel === clickNode.levelType - 0.5 ||
            m.menuLevel >
              (clickNode.parent?.levelType ||
                ConstructMenuOperator.constructorLevel)) &&
          (sub
            ? m.menuLevel <= sub.levelType ||
              (sub.levelType === 3 && m.menuLevel === 120)
            : true) &&
          !store?.currentTreeGroupInfo?.optionLock;
      }
      //重命名高亮
      if(m.menuLevel === MenuLevel.Rename) {
        let projectObj = treeList.find(i => i.levelType === 1);
        m.isValid = !projectObj.optionLock;
      }
    });
  }

  async addLocation(xTableRef, allTree, menuLevel, insertIndex) {
    // console.log(xTableRef, allTree, menuLevel, insertIndex);
    const insertRowIndex = insertIndex || 1;
    console.log('接受的下标', insertRowIndex);
    this.allTreeData = allTree;
    this.$VTable = xTableRef.value;
    let clickNode = xTableRef.value?.getCurrentRecord();
    if (menuLevel === ConstructMenuOperator.delete) {
      // this.removeData(clickNode)
      return;
    }

    const record = {
      name: ``,
      id: Date.now(),
      parentId: clickNode.id,
      parent: clickNode,
      levelType: menuLevel,
      // levelType:
      //   menuLevel === ConstructMenuOperator.singleChildLevel ? 2 : menuLevel,
      constructMajorType: null,
      isNew: true,
    };
    let isSingChild = false;
    if (menuLevel === ConstructMenuOperator.singleChildLevel) {
      //增加子单项
      isSingChild = true;
    }
    const { isSing, isUnit } = this.isThereSingOrUnit(allTree);
    console.log('插入标识', isSing, isUnit, isSingChild);
    // 选中工程项操作
    if (!isUnit && !isSing) {
      // 没有单项单位或者没有单位有单项
      if (clickNode.levelType === ConstructMenuOperator.constructorLevel) {
        console.log('1');
        this.insertData({ index: 1, record });
      }
    }
    if (!isUnit && isSing) {
      // 仅有单项
      if (clickNode.levelType === ConstructMenuOperator.constructorLevel) {
        console.log('2');
        // this.insertData({index: allTree.length, record})
        this.insertData({ index: insertRowIndex, record });
      } else if (
        clickNode.levelType === ConstructMenuOperator.singleLevel ||
        clickNode.levelType === ConstructMenuOperator.singleChildLevel
      ) {
        // 选中单项
        if (menuLevel === ConstructMenuOperator.singleLevel) {
          record.parentId = clickNode.parentId;
          record.parent = clickNode.parent;
          console.log('3');
          this.insertNext({ clickNode, record });
        } else {
          const index = allTree.findIndex(item => item.id === clickNode.id) + 1;
          console.log('4');
          this.insertData({ index, record });
        }
      } else {
        if (menuLevel === ConstructMenuOperator.unitLevel) {
          record.parentId = clickNode.parentId;
          record.parent = clickNode.parent;
          console.log('7');
          this.insertNext({ clickNode, record });
        }
      }
    }
    if (isUnit && !isSing) {
      // 仅有单位
      if (clickNode.levelType === ConstructMenuOperator.constructorLevel) {
        // 选中工程行
        if (menuLevel === ConstructMenuOperator.unitLevel) {
          console.log('5');
          // this.insertData({index: allTree.length, record})
          this.insertData({ index: insertRowIndex, record });
        }
        if (menuLevel === ConstructMenuOperator.singleLevel) {
          // 将单位工程挂在到添加的单项上
          allTree.forEach(item => {
            if (item.levelType === ConstructMenuOperator.unitLevel) {
              item.parentId = record.id;
              item.parent = record;
            }
          });
          console.log('6');
          this.insertData({ index: 1, record });
        }
      }
      if (clickNode.levelType === ConstructMenuOperator.unitLevel) {
        // 选中单位
        if (menuLevel === ConstructMenuOperator.unitLevel) {
          record.parentId = clickNode.parentId;
          record.parent = clickNode.parent;
          console.log('7');
          this.insertNext({ clickNode, record });
        }
        if (menuLevel === ConstructMenuOperator.singleLevel) {
          // 添加单项，并将先有得单位加入单项
          record.parentId = clickNode.parentId;
          record.parent = clickNode.parent;
          allTree.forEach(item => {
            if (item.levelType === ConstructMenuOperator.unitLevel) {
              item.parentId = record.id;
              item.parent = record;
            }
          });
          console.log('8');
          this.insertData({ index: 1, record });
        }
      }
    }
    if (isUnit && isSing) {
      if (clickNode.levelType === ConstructMenuOperator.constructorLevel) {
        console.log('9', insertRowIndex, record);
        // this.insertData({index: allTree.length, record})
        this.insertData({ index: insertRowIndex, record });
      }
      if (
        clickNode.levelType === ConstructMenuOperator.singleLevel ||
        clickNode.levelType === ConstructMenuOperator.singleChildLevel
      ) {
        if (menuLevel === ConstructMenuOperator.singleLevel) {
          record.parentId = clickNode.parentId;
          record.parent = clickNode.parent;
          console.log('10');
          this.insertNext({ clickNode, record });
        }
        if (
          menuLevel === ConstructMenuOperator.unitLevel ||
          menuLevel === ConstructMenuOperator.singleChildLevel
        ) {
          // 添加单位
          console.log('11');
          if (clickNode.children.length) {
            this.insertData({ index: insertRowIndex + 1, record });
          } else {
            this.insertNext({ clickNode, record });
          }
        }
      }
      if (clickNode.levelType === ConstructMenuOperator.unitLevel) {
        record.parentId = clickNode.parentId;
        record.parent = clickNode.parent;
        console.log('12');
        this.insertNext({ clickNode, record });
      }
    }
  }
  getDeleteLength(tar) {
    //获取层级---子弹项目前层级限制为10级
    this.deleteLength += tar.length;
    tar &&
      tar.map(i => {
        i.children.length > 0 ? (this.deleteLength += i.children.length) : '';
        i.children.length > 0 ? this.getDeleteLength(i.children) : '';
      });
  }
  // 删除操作
  async removeData(clickNode) {
    const content =
      clickNode.levelType === ConstructMenuOperator.unitLevel
        ? '是否删除该单位工程？'
        : '是否确定删除，删除后将会将单项工程下关联得所有得数据删除？';
    const status = await this.modalTip(content);
    let handleStatus = true;
    if (!status) {
      // 点击了取消
      handleStatus = false;
    } else {
      let allTree = this.allTreeData;
      console.log('点击删除', allTree, clickNode);
      const index = allTree.findIndex(item => item.id === clickNode.id);
      this.deleteLength = 0;
      if (clickNode.children) {
        // this.deleteLength += clickNode.children.length;
        this.getDeleteLength(clickNode.children);
      } else {
        this.deleteLength = 1;
      }
      allTree.splice(index, this.deleteLength + 1);
      await this.$VTable?.loadData(allTree);
      console.log('找到的下标', allTree, index);
      const handleIndex = index - 1 > -1 ? index - 1 : 0;
      this.newRecord = allTree[handleIndex];
      console.log('设置', this.newRecord);
      const l = await this.$VTable?.setCurrentRow(this.newRecord); // 选中
      console.log('设置回掉', l);
    }
    console.log('删除之后数据', this.allTreeData);
    return new Promise((resolve, reject) => {
      resolve(handleStatus);
    });
  }

  modalTip(content) {
    return new Promise((resolve, reject) => {
      VXETable.modal
        .confirm({
          content: content,
          title: '',
          className: 'dialog-comm confirm-dialog',
          status: 'error',
          iconStatus: 'vxe-icon-info-circle',
        })
        .then(res => {
          resolve(res === 'confirm');
        });
    });
  }

  // 插入到当前下一个
  async insertNext({ clickNode, record }) {
    console.log('插入到', this.allTreeData, clickNode.id);
    let currentItem = {};
    let index = this.allTreeData.findIndex(item => {
      currentItem = item;
      return item.id === clickNode.id;
    });
    console.log('插入到当前下一个', index);
    if (currentItem.children && currentItem.children.length) {
      index += currentItem.children.length;
      console.log('插入到当前下二个', index);
    }
    index += 1;
    this.insertData({ index, record });
  }
  // 插入任意位置数据
  async insertData({ index, record }) {
    this.newRecord = record;
    this.allTreeData.splice(index, 0, record);
    await this.$VTable?.loadData(this.allTreeData);
    await this.$VTable?.setTreeExpand(record, true); // 将父节点展开
    await this.$VTable?.setEditRow(record); // 激活编辑
    await this.$VTable?.setCurrentRow(record); // 选中
    console.log('插入任意位置后', this.allTreeData);
  }

  isThereSingOrUnit(allTree, isSing = false, isUnit = false) {
    for (let item of allTree || []) {
      if (item.levelType === ConstructMenuOperator.singleLevel) {
        isSing = true;
        if (!isUnit && item.children && item.children.length) {
          return this.isThereSingOrUnit(item.children, isSing, isUnit);
        }
      } else if (item.levelType === ConstructMenuOperator.unitLevel) {
        isUnit = true;
        if (isSing && isUnit) {
          return { isSing, isUnit };
        }
      } else {
        if ((!isUnit || !isSing) && item.children && item.children.length) {
          return this.isThereSingOrUnit(item.children, isSing, isUnit);
        }
      }
    }
    return { isSing, isUnit };
  }
}
