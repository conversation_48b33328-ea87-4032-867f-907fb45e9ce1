"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDeRuleRelation = void 0;
const BaseModel_1 = require("./BaseModel");
const typeorm_1 = require("typeorm");
/**
 * 定额和规则关联关系表
 */
let BaseDeRuleRelation = class BaseDeRuleRelation extends BaseModel_1.BaseModel {
};
__decorate([
    (0, typeorm_1.Column)({ name: "sequence_nbr", nullable: true }),
    __metadata("design:type", String)
], BaseDeRuleRelation.prototype, "sequenceNbr", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRuleRelation.prototype, "deCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "de_name", nullable: true }),
    __metadata("design:type", String)
], BaseDeRuleRelation.prototype, "deName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "library_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRuleRelation.prototype, "libraryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "relation_group_code", nullable: true }),
    __metadata("design:type", String)
], BaseDeRuleRelation.prototype, "relationGroupCode", void 0);
BaseDeRuleRelation = __decorate([
    (0, typeorm_1.Entity)({ name: "base_de_rule_relation" })
], BaseDeRuleRelation);
exports.BaseDeRuleRelation = BaseDeRuleRelation;
//# sourceMappingURL=BaseDeRuleRelation.js.map