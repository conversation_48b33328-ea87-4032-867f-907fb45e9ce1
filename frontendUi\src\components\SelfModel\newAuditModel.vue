<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-05-17 10:58:45
 * @LastEditors: liuxia
 * @LastEditTime: 2024-06-14 10:31:20
-->

<template>
  <common-modal
    width="700"
    height="500"
    v-model:modelValue="fatherProps.visible"
    @close="cancel"
    :title="fatherProps.showType"
    className="dialog-self"
  >
    <!-- 增加计税方式后样式变化   height="500" -->
    <div class="headerPro">
      <div class="proList">
        <new-audit-aside
          class="content"
          @getProType="getProType"
          v-if="fatherProps.showType === '新建审核项目'"
        ></new-audit-aside>
        <img src="~@/assets/img/openPro.png" alt="" class="icon" />
      </div>
      <div class="iptInfo">
        <input-list-audit
          :projectType="iptList.iptType"
          v-bind="$attrs"
          @closeDialog="cancel"
          @comparativeMatchingModel="comparativeMatching"
          v-if="fatherProps.showType === '新建审核项目'"
        ></input-list-audit>
      </div>
    </div>
  </common-modal>
</template>

<script setup>
import newAuditAside from './newAuditAside.vue';
import inputListAudit from './inputListAudit.vue';
import { defineEmits, onMounted, reactive, ref } from 'vue';
const iptList = reactive({
  iptType: 'yusuan',
});
const fatherProps = defineProps({
  showType: {
    type: String, //类型字符串
  },
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:visible', 'comparativeMatching']);
const cancel = () => {
  iptList.iptType = 'yusuan';
  emit('update:visible', false); //关闭弹框
};
const comparativeMatching = val => {
  emit('comparativeMatching', val);
};
// const emit = defineEmits(['isvisible']);
// const handleOk = () => {
//   emit('isvisible', false);
// };
const getProType = type => {
  iptList.iptType = type;
};
</script>
<style lang="scss" scoped>
.headerPro {
  width: 100%;
  display: flex;
  height: 100%;
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    background: rgba(216, 216, 216, 0.15);
  }
  .proList {
    width: 30%;
    height: 100%;
    background: linear-gradient(
      180deg,
      #3f78ce 0%,
      rgba(51, 131, 252, 0.75) 100%
    );
    .content{
      position: relative;
      z-index: 3;
    }
    .icon {
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .iptInfo {
    width: 70%;
    overflow: auto;
    &:hover {
      overflow: auto;
    }
  }
}
</style>
